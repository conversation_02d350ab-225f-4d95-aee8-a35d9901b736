<template>
  <div class="favorites-page">
    <h1>我的收藏</h1>
    
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="favorites.length === 0" class="empty-favorites">
      <el-empty description="您还没有收藏任何产品">
        <el-button type="primary" @click="$router.push('/products')">浏览产品</el-button>
      </el-empty>
    </div>
    
    <div v-else class="favorites-container">
      <el-row :gutter="16">
        <el-col v-for="item in favorites" :key="item.productId || item.id" :xs="12" :sm="12" :md="12" :lg="12">
          <el-card class="product-card" shadow="hover">
            <div class="product-image">
              <img :src="(item.products && item.products.mainImage) || item.mainImage || '/placeholder.png'" 
                   :alt="(item.products && item.products.name) || item.name"
                   @click="viewProduct(item.productId || (item.products && item.products.id) || item.id)">
            </div>
            <div class="card-content">
              <div class="product-info" @click="viewProduct(item.productId || (item.products && item.products.id) || item.id)">
                <h3>{{ (item.products && item.products.name) || item.name }}</h3>
                <p class="price">￥{{ (item.products && item.products.price) || item.price }}</p>
                <p class="date">Added: {{ formatDate(item.createdAt) }}</p>
              </div>
              <div class="action-area">
                <el-button 
                  class="remove-btn" 
                  size="small"
                  type="danger" 
                  plain
                  @click="confirmRemove(item.productId || (item.products && item.products.id) || item.id)" 
                >
                  Remove
                </el-button>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getFavorites, removeFavorite } from '@/api/favorites'

export default {
  name: 'FavoritesView',
  setup() {
    const router = useRouter()
    const favorites = ref([])
    const loading = ref(true)

    const fetchFavorites = async () => {
      loading.value = true
      try {
        const response = await getFavorites()
        console.log('API返回的完整响应：', response)
        
        // 处理不同可能的数据结构
        if (response.data && Array.isArray(response.data)) {
          favorites.value = response.data
        } else if (response.data && response.data.list && Array.isArray(response.data.list)) {
          favorites.value = response.data.list
        } else if (Array.isArray(response)) {
          favorites.value = response
        } else if (response.code === 200 && Array.isArray(response.data)) {
          favorites.value = response.data
        } else {
          console.error('无法识别的数据格式:', response)
          favorites.value = []
        }
        
        console.log('处理后的收藏数据:', favorites.value)
        
        if (favorites.value.length > 0) {
          // 检查数据结构，查看第一个元素的属性
          console.log('第一个收藏项的数据结构:', JSON.stringify(favorites.value[0]))
        }
      } catch (error) {
        console.error('获取收藏列表失败:', error)
        ElMessage.error('加载收藏产品失败')
      } finally {
        loading.value = false
      }
    }

    const confirmRemove = (productId) => {
      ElMessageBox.confirm(
        'Are you sure you want to remove this item from favorites?',
        'Confirm',
        {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }
      ).then(() => {
        removeFromFavorites(productId)
      }).catch(() => {
        // 用户点击取消，不做任何操作
      })
    }

    const removeFromFavorites = async (productId) => {
      try {
        const response = await removeFavorite(productId)
        console.log('取消收藏响应:', response)
        
        // 无论返回结果如何，从本地列表中移除
        favorites.value = favorites.value.filter(item => {
          // 处理可能的不同数据结构
          const itemId = item.productId || (item.products && item.products.id) || item.id
          return itemId != productId // 使用非严格比较，因为ID可能是字符串或数字
        })
        
        ElMessage.success('取消收藏成功')
      } catch (error) {
        console.error('移除收藏失败:', error)
        ElMessage.error('取消收藏失败')
      }
    }

    const viewProduct = (productId) => {
      console.log('查看产品详情:', productId)
      router.push({ name: 'product-detail', params: { id: productId } })
    }

    const formatDate = (dateString) => {
      if (!dateString) return 'Unknown date'
      
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    }

    onMounted(() => {
      fetchFavorites()
    })

    return {
      favorites,
      loading,
      confirmRemove,
      removeFromFavorites,
      viewProduct,
      formatDate
    }
  }
}
</script>

<style scoped>
.favorites-page {
  padding: 1.5rem 1rem;
  max-width: 1000px;
  margin: 0 auto;
}

.favorites-page h1 {
  margin-bottom: 1.5rem;
  text-align: center;
  color: var(--el-color-primary);
  font-size: 1.5rem;
}

.loading-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.empty-favorites {
  margin: 2rem auto;
  max-width: 400px;
}

.favorites-container {
  margin-top: 1rem;
}

.product-card {
  margin-bottom: 1rem;
  height: 85%;
  cursor: pointer;
  transition: transform 0.2s;
  overflow: hidden;
}

.product-card:hover {
  transform: translateY(-3px);
}

.product-image {
  height: 130px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: -1px;
  margin-bottom: 0;
}

.product-image img {
  width: 101%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.card-content {
  display: flex;
  padding: 0.6rem 0.5rem;
}

.product-info {
  flex: 1;
  cursor: pointer;
}

.product-info h3 {
  margin: 0 0 0.3rem 0;
  font-size: 0.9rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.price {
  font-weight: bold;
  color: var(--el-color-danger);
  margin: 0.2rem 0;
  font-size: 0.9rem;
}

.date {
  font-size: 0.75rem;
  color: #888;
  margin: 0.2rem 0;
}

.action-area {
  display: flex;
  align-items: center;
  margin-left: 0.4rem;
}

.remove-btn {
  font-size: 0.65rem;
  padding: 0 0.2rem;
  height: 18px;
  min-height: 18px;
  line-height: 1;
  --el-button-size: mini;
  --el-button-border-radius: 2px;
  margin: 0;
}

.remove-btn :deep(.el-button__text) {
  margin: 0;
  padding: 0;
}

@media (max-width: 768px) {
  .favorites-page {
    padding: 1rem 0.5rem;
  }
  
  .product-image {
    height: 110px;
  }
  
  .product-info h3 {
    font-size: 0.8rem;
  }
  
  .price {
    font-size: 0.75rem;
  }
  
  .date {
    font-size: 0.65rem;
  }
  
  .remove-btn {
    font-size: 0.55rem;
    padding: 0 0.2rem;
    height: 16px;
    min-height: 16px;
  }
}
</style> 