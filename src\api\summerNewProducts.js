import apiClient from '@/services/api';

/**
 * 获取推荐新品列表
 * @returns {Promise<Object>} 推荐新品数据
 */
export const getSummerNewProductsList = async () => {
    try {
        // 实际API调用
        const response = await apiClient.get('/summerNewProducts/list');
        // 兼容不同响应格式
        if (response && response.code === 200 && Array.isArray(response.data)) {
            return response.data;
        } else if (Array.isArray(response)) {
            return response;
        } else {
            return [];
        }
    } catch (error) {
        console.error('获取推荐新品失败:', error);
        return [];
    }
};

// 获取首页Banner轮播图
export function getHomeBanners() {
    return apiClient.get('/homeBanners/getBanners');
}

// 获取工厂照片轮播图
export function getFactoryBanners() {
    return apiClient.get('/homeFactoryBanners/getFactoryBanners');
}

export default {
    getSummerNewProductsList,
    getHomeBanners,
    getFactoryBanners
};

