<template>
  <div class="related-product-card"
       :class="{ 'dark-theme': computedIsDarkMode, 'light-theme': !computedIsDarkMode }"
       @click="handleClick">
    <div class="product-image-container" :class="{ 'dark-theme': computedIsDarkMode, 'light-theme': !computedIsDarkMode }">
      <img
        :src="product.image"
        :alt="product.name"
        @error="handleImageError"
        class="product-image"
      >
    </div>
    <div class="product-content">
      <h3 class="product-title" :class="{ 'dark-theme': computedIsDarkMode, 'light-theme': !computedIsDarkMode }">{{ product.name }}</h3>
      <div class="price-section">
        <span class="current-price" :class="{ 'dark-theme': computedIsDarkMode, 'light-theme': !computedIsDarkMode }">
          {{ currencySymbol }}{{ formatPrice(product.price) }}
        </span>
        <span v-if="product.originalPrice" class="original-price" :class="{ 'dark-theme': computedIsDarkMode, 'light-theme': !computedIsDarkMode }">
          {{ currencySymbol }}{{ formatPrice(product.originalPrice) }}
        </span>
      </div>
      <div class="product-actions">
        <div @click.stop="handleLike"
             class="action-item"
             :class="{
               'active': product.isLiked,
               'dark-theme': computedIsDarkMode,
               'light-theme': !computedIsDarkMode
             }">
          <i class="fas fa-thumbs-up"></i>
          <span>{{ product.likes || 0 }}</span>
        </div>
        <div @click.stop="handleViews"
             class="action-item"
             :class="{ 'dark-theme': computedIsDarkMode, 'light-theme': !computedIsDarkMode }">
          <i class="fas fa-eye"></i>
          <span>{{ getTotalViews() }}</span>
        </div>
        <div @click.stop="handleComments"
             class="action-item"
             :class="{ 'dark-theme': computedIsDarkMode, 'light-theme': !computedIsDarkMode }">
          <i class="fas fa-comment"></i>
          <span>{{ getCommentsCount() }}</span>
        </div>
        <div @click.stop="handleCollect"
             class="action-item"
             :class="{
               'active': product.isCollected,
               'dark-theme': computedIsDarkMode,
               'light-theme': !computedIsDarkMode
             }">
          <i class="fas fa-heart"></i>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import productsApi from '@/api/products'
import { addCollection, removeFavorite } from '@/api/favorites'

export default {
  name: 'RelatedProductCard',
  props: {
    product: {
      type: Object,
      required: true
    },
    currencySymbol: {
      type: String,
      default: '$'
    },
    exchangeRate: {
      type: Number,
      default: 1
    },
    isDarkMode: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentTheme: document.documentElement.getAttribute('data-theme') || 'light',
      observer: null
    }
  },
  computed: {
    computedIsDarkMode() {
      // Use the internal state to avoid recursive updates
      return this.currentTheme === 'dark';
    }
  },
  mounted() {
    // Set up observer to detect theme changes
    this.observer = new MutationObserver(() => {
      const newTheme = document.documentElement.getAttribute('data-theme') || 'light';
      if (this.currentTheme !== newTheme) {
        this.currentTheme = newTheme;
      }
    });

    this.observer.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    });
  },
  beforeUnmount() {
    // Clean up the observer
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }
  },
  methods: {
    // Remove updateTheme method as it's no longer needed
    
    handleClick() {
      this.$emit('product-click', this.product)
    },

    // handleImageError(event) {
    //   console.error('Image load error for related product:', this.product.name)
    //   event.target.src = 'https://via.placeholder.com/280x280/333/fff?text=No+Image'
    // },

    async handleLike() {
      const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
      if (!currentUser.id) {
        ElMessage.warning('Please log in to like products')
        return
      }

      try {
        const response = await productsApi.toggleLike(this.product.id || this.product.productId)
        if (response.code === 200) {
          this.$emit('like-updated', {
            ...this.product,
            isLiked: !this.product.isLiked,
            likes: this.product.likes + (this.product.isLiked ? -1 : 1)
          })
          ElMessage.success(this.product.isLiked ? 'Like removed' : 'Liked!')
        }
      } catch (error) {
        console.error('Error toggling like:', error)
        ElMessage.error('Failed to update like status')
      }
    },

    async handleCollect() {
      const currentUser = JSON.parse(localStorage.getItem('currentUser') || '{}')
      if (!currentUser.id) {
        ElMessage.warning('Please log in to collect products')
        return
      }

      try {
        if (this.product.isCollected) {
          await removeFavorite(this.product.id || this.product.productId)
          this.$emit('collect-updated', { ...this.product, isCollected: false })
          ElMessage.success('Removed from favorites')
        } else {
          await addCollection({
            productId: this.product.id || this.product.productId,
            userId: currentUser.id
          })
          this.$emit('collect-updated', { ...this.product, isCollected: true })
          ElMessage.success('Added to favorites')
        }
      } catch (error) {
        console.error('Error toggling collection:', error)
        ElMessage.error('Failed to update collection status')
      }
    },

    handleViews() {
      this.$emit('views-click', this.product)
    },

    handleComments() {
      this.$emit('comments-click', this.product)
    },

    formatPrice(price) {
      // 现在后台录入的是人民币价格，需要转换为美元显示
      // exchangeRate 现在表示人民币对美元的汇率（例如 6.5）
      // 所以需要用人民币价格除以汇率得到美元价格
      return (price / this.exchangeRate).toFixed(2)
    },

    getCommentsCount() {
      return (this.product.comments && this.product.comments.length) || 0
    },

    // 计算总浏览量（真实浏览量 + 虚拟浏览量）
    getTotalViews() {
      const realViews = parseInt(this.product.views || 0);
      const virtualViews = parseInt(this.product.virtualViews || 0);
      return realViews + virtualViews;
    }
  }
}
</script>

<style scoped>
/* CSS variables */
:root {
  --card-bg-light: #ffffff;
  --card-shadow-light: 0 4px 12px rgba(139, 92, 246, 0.15);
  --card-border-light: 1px solid rgba(139, 92, 246, 0.2);
  --text-color-light: #333333;
  --price-color-light: #8b5cf6;
  --price-shadow-light: 0 0 8px rgba(139, 92, 246, 0.3);
  --original-price-color-light: #666;
  --action-color-light: #8b5cf6;
  --image-bg-light: rgba(248, 250, 252, 0.9);

  --card-bg-dark: #1e1a2e;
  --card-shadow-dark: 0 4px 15px rgba(0, 0, 0, 0.3);
  --card-border-dark: 1px solid rgba(120, 70, 200, 0.25);
  --text-color-dark: #ffffff;
  --price-color-dark: #bb86fc;
  --price-shadow-dark: 0 0 8px rgba(187, 134, 252, 0.6);
  --original-price-color-dark: #888;
  --action-color-dark: #c3a3ff;
  --image-bg-dark: rgba(15, 15, 20, 0.3);
}

.related-product-card {
  border-radius: 12px;
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease, border-color 0.3s ease;
  cursor: pointer;
  height: 100%;
  min-height: 280px;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Light theme styles */
.related-product-card.light-theme {
  background: var(--card-bg-light);
  box-shadow: var(--card-shadow-light);
  border: var(--card-border-light);
}

/* Dark theme styles */
.related-product-card.dark-theme {
  background: var(--card-bg-dark);
  box-shadow: var(--card-shadow-dark);
  border: var(--card-border-dark);
}

/* Hover effects */
.related-product-card:hover {
  transform: translateY(-5px);
}

.related-product-card.dark-theme:hover {
  box-shadow: 0 8px 25px rgba(195, 163, 255, 0.4);
  border-color: rgba(195, 163, 255, 0.5);
}

.related-product-card.light-theme:hover {
  box-shadow: 0 8px 25px rgba(139, 92, 246, 0.25);
  border-color: rgba(139, 92, 246, 0.6);
}

.product-image-container {
  width: 100%;
  height: 220px;
  overflow: hidden;
  position: relative;
  padding: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid rgba(139, 92, 246, 0.1);
}

.product-image-container.light-theme {
  background: var(--image-bg-light);
}

.product-image-container.dark-theme {
  background: var(--image-bg-dark);
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  display: block;
  vertical-align: middle;
  padding: 10px;
}

.related-product-card:hover .product-image {
  transform: scale(1.03);
}

.product-content {
  padding: 0.7rem;
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
}

.product-title {
  font-size: 0.85rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.1;
  height: 1.2em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-title.light-theme {
  color: var(--text-color-light);
}

.product-title.dark-theme {
  color: var(--text-color-dark);
}

.price-section {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  flex-wrap: wrap;
  margin: 0.2rem 0;
}

.current-price {
  font-size: 1rem;
  font-weight: bold;
}

.current-price.light-theme {
  color: var(--price-color-light);
  text-shadow: var(--price-shadow-light);
}

.current-price.dark-theme {
  color: var(--price-color-dark);
  text-shadow: var(--price-shadow-dark);
}

.original-price {
  font-size: 0.75rem;
  text-decoration: line-through;
}

.original-price.light-theme {
  color: var(--original-price-color-light);
}

.original-price.dark-theme {
  color: var(--original-price-color-dark);
}

.product-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.2rem;
  gap: 0.5rem;
  padding-top: 0.3rem;
  border-top: 1px dashed rgba(139, 92, 246, 0.15);
}

.action-item {
  font-size: 0.7rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 0.2rem;
  flex: 1;
  justify-content: center;
  background: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  padding: 0 !important;
  transition: transform 0.2s ease;
}

.action-item.light-theme {
  color: var(--action-color-light);
}

.action-item.dark-theme {
  color: var(--action-color-dark);
}

.action-item:hover {
  transform: scale(1.05);
}

.action-item.active.light-theme {
  color: #f43f5e;
  text-shadow: 0 0 8px rgba(244, 63, 94, 0.3);
}

.action-item.active.dark-theme {
  color: #ff6b6b;
  text-shadow: 0 0 8px rgba(255, 107, 107, 0.8);
}

.action-item i {
  font-size: 0.85rem;
}

.action-item span {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Responsive design */
@media (max-width: 768px) {
  .related-product-card {
    min-height: 320px;
    border-radius: 10px;
  }
  
  .product-image-container {
    height: 210px;
    padding: 0;
  }
  
  .product-image {
    object-fit: contain;
    padding: 8px;
    max-height: 100%;
    max-width: 100%;
  }
  
  .product-content {
    padding: 0.8rem 0.7rem 0.5rem;
    gap: 0.3rem;
  }
  
  .product-title {
    font-size: 0.9rem;
    height: 2.4em;
    line-height: 1.2;
    -webkit-line-clamp: 2;
    margin-bottom: 0.15rem;
  }

  .price-section {
    margin: 0.2rem 0 0.15rem;
  }
  
  .product-actions {
    gap: 0.25rem;
  }
}

@media (max-width: 480px) {
  .related-product-card {
    min-height: 300px;
  }
  
  .product-image-container {
    height: 190px;
  }
  
  .product-image {
    padding: 5px;
  }
  
  .product-content {
    padding: 0.6rem 0.6rem 0.3rem;
    gap: 0.25rem;
  }
  
  .price-section {
    margin: 0.2rem 0 0.1rem;
  }
}
</style> 