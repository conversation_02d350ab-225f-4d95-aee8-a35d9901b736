import apiClient from '@/services/api';

// 只保留真实API请求

/**
 * 获取全部商家
 */
export const getAllSellers = () => apiClient.get('/omg/sellers/all');

/**
 * 根据类型获取卖家列表
 * @param {string} type - 卖家类型 (promoted, trusted)
 */
export const getSellersByType = async (type) => {
  if (type === 'trusted') {
    const res = await apiClient.get('/omg/sellers/trusted', { params: { trusted: 'active' } });
    if (res.data && Array.isArray(res.data)) {
      res.data = res.data.map(mapSellerData);
    }
    return res;
  }
  if (type === 'promoted') {
    return getFeaturedSellers();
  }
  // 其它类型可根据后端实际情况扩展
  return Promise.resolve({ data: [], code: 200, message: 'No data' });
};

/**
 * 获取商家详情
 */
export const getSellerDetails = (id) => apiClient.get(`/sellers/${id}`);

/**
 * 分页获取卖家列表（如后端支持可补充真实接口）
 */
export const getSellersPaginated = (page, limit, type = '') => {
  const params = { page, limit };
  if (type && type !== 'all') params.type = type;
  return apiClient.get('/sellers/paginated', { params });
};

/**
 * 搜索卖家（如后端支持可补充真实接口）
 */
export const searchSellers = (query) => apiClient.get('/sellers/search', { params: { query } });

/**
 * 获取推荐商家
 */
export const getFeaturedSellers = async () => {
  const res = await apiClient.get('/omg/sellers/recommended');
  if (res.data && Array.isArray(res.data)) {
    res.data = res.data.map(mapSellerData);
  }
  return res;
};

// 工具函数：后端数据转前端格式
function mapSellerData(seller) {
  return {
    id: seller.sellerId,
    name: seller.storeName,
    description: seller.description,
    logoUrl: seller.logo ? `https://static.agtfind.com/${seller.logo}` : '',
    platformUrl: seller.platformUrl || '',
    websiteUrl: seller.platformUrl || '',
    type: seller.status === 'recommended' ? 'promoted' : (seller.type || ''),
    coverImage: seller.coverImage ? `https://static.agtfind.com/${seller.coverImage}` : '',
    contactEmail: seller.contactEmail || '',
    joinedDate: seller.joinedDate || '',
    platformDiscountCode: seller.platformDiscountCode || '',
    status: seller.status || '',
    storeSlug: seller.storeSlug || '',
    like: seller.likes != null ? seller.likes : (seller.like || '0'),
    collect: seller.collect != null ? seller.collect : (seller.bookmarks || '0')
  };
}

// 其它功能接口（如关注、评分等）保留原有实现

export const getSellerProducts = async () => {
  // 这里建议补充真实API
  return { data: [], code: 200, message: 'Success' };
};

export const followSeller = async () => {
  // 这里建议补充真实API
  return { data: { followed: true }, code: 200, message: 'Successfully followed seller' };
};

export const unfollowSeller = async () => {
  // 这里建议补充真实API
  return { data: { followed: false }, code: 200, message: 'Successfully unfollowed seller' };
};

export const getFollowedSellers = async () => {
  // 这里建议补充真实API
  return { data: [], code: 200, message: 'Success' };
};

export const rateSeller = async () => {
  // 这里建议补充真实API
  return { data: { success: true }, code: 200, message: 'Rating submitted successfully' };
};

export const getSellerRatings = async () => {
  // 这里建议补充真实API
  return { data: { ratings: [], average: 0, count: 0 }, code: 200, message: 'Success' };
};

export default {
  getAllSellers,
  getSellersByType,
  getSellerDetails,
  getSellersPaginated,
  searchSellers,
  getFeaturedSellers,
  getSellerProducts,
  followSeller,
  unfollowSeller,
  getFollowedSellers,
  rateSeller,
  getSellerRatings
};