<template>
  <div class="home">
    <!-- Introduction Section -->
    <section class="intro-section">
      <div class="intro-container">
        <div class="intro-content">
          <div class="logo-container">
            <img src="@/assets/logo2.png" alt="OMGBUY Logo" class="site-logo">
          </div>
          <h1 class="intro-title-glow">Welcome to OMGBUY</h1>
          <div class="intro-desc-glow">
            <p class="intro-tagline">It's like Google Spreadsheets, but on steroids (the Pro Max version)！<br>5 points to let you know why to choose OMGBUYSHEETS.</p>
            
            <ul class="benefits-list">
              <li><span class="benefit-number">1.</span> Over 50 hot items under $5 USD</li>
              <li><span class="benefit-number">2.</span> Over 30+ direct China factories/sources (Information Hub)</li>
              <li><span class="benefit-number">3.</span> Explore 3,000+ Curated Product Links</li>
              <li><span class="benefit-number">4.</span> Genuine QC photos and real user evaluations</li>
              <li><span class="benefit-number">5.</span> Easier product discovery and smarter purchasing decisions</li>
            </ul>
          </div>
        </div>

        <!-- 在线用户飞船计数器 -->
        <div class="neon-spaceship-counter" :class="{'hide-on-mobile': isMobile}">
          <div class="spaceship-container">
            <!-- 飞船图形 -->
            <div class="spaceship-shape">
              <div class="spaceship-body"></div>
              <div class="spaceship-cabin"></div>
              <div class="spaceship-light light-1"></div>
              <div class="spaceship-light light-2"></div>
            </div>
            
            <!-- 在线用户计数区域 -->
            <div class="counter-content">
              <div class="counter-title">{{ $t('home.activeUsers') }}</div>
              <div class="counter-value">
                <div class="user-icon"></div>
                <div class="user-count">{{ onlineUsers }}</div>
              </div>
            </div>
            
            <!-- 雷达扫描效果 -->
            <div class="radar-effect">
              <div class="radar-circle"></div>
              <div class="radar-beam"></div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Product Grid with Carousel -->
    <section class="products">
      <h2>{{ $t('home.elevateRep') }}</h2>
      
      <!-- 使用EnhancedCarousel组件替换原有轮播图 -->
      <EnhancedCarousel
        :slides="banners"
        :loading="isLoading"
        :autoplay="true"
        :autoplay-interval="2000"
        :height="isMobile ? 150 : 400"
        :show-counter="false"
        :show-indicators="!isMobile"
        :show-controls="!isMobile"
        :class="{ 'mobile-carousel': isMobile }"
        @slide-click="handleBannerClick"
      />
    </section>

    <!-- Recommended Products Section -->
    <section class="recommended-products">
      <h2>TRENDING NOW</h2>
      <div class="discount-code">Discount Code: OMGBUY 15% OFF</div>
      <div class="products-grid">
        <div v-for="product in displayedProducts" :key="product.productId" :class="['product-card', {'view-more-card': product.isViewMore}]" @click="product.isViewMore ? goToProducts() : goToProductDetail(product.productId)">
          <div v-if="product.isViewMore" class="view-more-simple">
            <div class="custom-arrow">
              <div class="arrow-circle"></div>
              <i class="fas fa-arrow-right arrow-icon"></i>
            </div>
          </div>
          <div v-else class="trending-product-container">
            <!-- Rocket Icon -->
            <div class="rocket-icon">
              <i class="fas fa-rocket"></i>
            </div>
            
            <!-- Main Product Image -->
            <div class="product-main-image">
              <img :src="product.image" :alt="product.name">
            </div>
            
            <!-- Small Product Variants -->
            <!-- <div class="product-variants">
              <div class="variant-image" v-for="(variant, idx) in getProductVariants(product)" :key="idx">
                <img :src="variant.image" :alt="product.name + ' variant'">
              </div>
            </div> -->
            
            <!-- Product Price and Views -->
            <div class="product-price-views">
              <div class="price-container">
                <p class="current-price">{{ currencySymbol }}{{ formatPrice(product.price) }}</p>
                <div class="views-badge">1688</div>
              </div>
              <div class="favorite-icon" @click.stop="handleCollect(product)">
                <i class="fas fa-heart" :class="{ 'collected': product.isCollected === true }"></i>
              </div>
            </div>
            
            <!-- Product Name -->
            <div class="product-info">
              <h3 class="product-name">{{ product.name }}</h3>
          </div>
            
            <!-- Social Stats -->
            <div class="social-stats">
              <div class="stat-container">
                <i class="fas fa-thumbs-up"></i>
                <span>{{ formatStatCount(product.likes) }}</span>
              </div>
              <div class="stat-container">
                <i class="fas fa-comment"></i>
                <span>{{ formatStatCount((product.comments && product.comments.length) || product.comments || 0) }}</span>
              </div>
              <div class="stat-container">
                <i class="fas fa-eye"></i>
                <span>{{ formatStatCount(getTotalViews(product)) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Information Sections -->
    <section class="info-section">
      <!-- 购物指南部分 -->
    </section>

    <!-- Bestseller Section -->
    <!-- <section class="bestseller"> -->
      <!-- 添加板块星星 -->
      <!-- <div class="section-stars">
        <div class="section-star s-star-21"></div>
        <div class="section-star s-star-22"></div>
        <div class="section-star s-star-23"></div>
        <div class="section-star s-star-24"></div>
        <div class="section-star s-star-25"></div>
        </div> -->
      <!-- <h2>BESTSELLER</h2> -->
      <!-- <div class="bestseller-container">
        <div class="bestseller-grid">
          <div v-for="item in bestsellers" :key="item.id" class="bestseller-card">
            <div class="product-image" @click="goToProductDetail(item.productId)">
              <img :src="item.mainImage" :alt="item.name">
        </div>
            <div class="product-details">
              <h3>{{ item.name }}</h3>
              <div class="price">{{ currencySymbol }}{{ formatPrice(item.price) }}</div>
              <div class="product-stats">
                <span><i class="fas fa-heart"></i> {{ item.likes || Math.floor(Math.random() * 200) + 50 }}</span>
                <span><i class="fas fa-eye"></i> {{ item.views || Math.floor(Math.random() * 1000) + 500 }}</span>
                <span><i class="fas fa-comment"></i> {{ item.comments ? item.comments.length : Math.floor(Math.random() * 40) + 5 }}</span>
                <span><i class="fas fa-bookmark"></i> {{ item.favorites || Math.floor(Math.random() * 80) + 20 }}</span>
            </div>
          </div>
            </div>
          </div>
      </div> -->
    <!-- </section> -->

    <!-- About Section -->
    

   <!-- OMGBUY Factory Photos Section -->
   <section class="factory-photos">
      <h2>OMGBUY FACTORY PHOTOS</h2>
      
      <div class="carousel-container">
        <div class="carousel" :style="{ transform: `translateX(-${currentFactorySlide * 100}%)` }">
          <div v-for="(banner, index) in factoryBanners" :key="index" class="carousel-slide" 
               @click="handleFactoryBannerClick(banner)"
               @mouseenter="pauseFactoryAutoSlide" 
               @mouseleave="resumeFactoryAutoSlide">
            <div class="slide-image-container">
              <img :src="banner.imageUrl" :alt="banner.title || 'Factory Photo'" class="slide-background">
              <div class="slide-overlay"></div>
        </div>
          </div>
        </div>
        
        <!-- OMGBUY FACTORY PHOTOS 轮播图 -->
        <button class="carousel-btn prev" @click.stop="prevFactorySlide(); pauseFactoryAutoSlide()">❮</button>
        <button class="carousel-btn next" @click.stop="nextFactorySlide(); pauseFactoryAutoSlide()">❯</button>
        
        <!-- OMGBUY FACTORY PHOTOS 轮播图的指示点 -->
        <div class="carousel-dots">
          <span 
            v-for="(_, index) in factoryBanners" 
            :key="index" 
            class="dot"
            :class="{ active: currentFactorySlide === index }"
            @click="goToFactorySlide(index); pauseFactoryAutoSlide()"
          ></span>
        </div>
        
        <button class="view-all-btn" @click="viewAllFactoryPhotos(); pauseFactoryAutoSlide()">VIEW ALL</button>
      </div>
    </section>

    <!-- FAQ Section -->
    <section class="faq" id="faq-anchor">
      <h2>FAQ</h2>
      <div class="faq-items">
        <!-- About Us -->
        <div class="faq-category">
          <h3>About Us</h3>
          <div class="faq-list">
            <div class="faq-item" @click="toggleFaq('what-is-agtfind')">
              <div class="faq-question">
                <span>What is OMGBUY?</span>
                <span class="toggle-icon">{{ faqStates['what-is-agtfind'] ? '−' : '+' }}</span>
              </div>
              <div class="faq-answer" v-if="faqStates['what-is-agtfind']">
                <p>OMGBUY is like Google Spreadsheets, but on steroids (the Pro Max version)! We have summarized 5 advantages of OMGBUY for you to choose it.</p>
                <ol>
                  <li>Over 50 hot items under $5 USD</li>
                  <li>Over 30+ direct China factories/sources (Information Hub)</li>
                  <li>Explore 3,000+ Curated Product Links</li>
                  <li>Genuine QC photos and real user evaluations</li>
                  <li>Easier product discovery and smarter purchasing decisions</li>
                </ol>
              </div>
            </div>
            <div class="faq-item" @click="toggleFaq('agtfind-purpose')">
              <div class="faq-question">
                <span>What is the purpose of OMGBUY?</span>
                <span class="toggle-icon">{{ faqStates['agtfind-purpose'] ? '−' : '+' }}</span>
              </div>
              <div class="faq-answer" v-if="faqStates['agtfind-purpose']">
                OMGBUY is designed to be your trusted resource, making it easier to find quality items, understand their true value, and connect with reliable sourcing information. We're committed to keeping our platform فائد (updated) with fresh discoveries and cost-effective options.
              </div>
            </div>
          </div>
        </div>

        <!-- Website Information -->
        <div class="faq-category">
          <h3>Website Information</h3>
          <div class="faq-list">
            <div class="faq-item" @click="toggleFaq('leave-review')">
              <div class="faq-question">
                <span>How do I leave a review?</span>
                <span class="toggle-icon">{{ faqStates['leave-review'] ? '−' : '+' }}</span>
              </div>
              <div class="faq-answer" v-if="faqStates['leave-review']">
                Ready to share the truth? Log in to leave your real review ✓ within the OMGBUY spreadsheet. Your valuable feedback—good or bad—directly helps fellow OMGBUY users discover the best products by enriching our shared data. Join us in building a transparent community!
              </div>
            </div>
          </div>
        </div>

        <!-- Purchasing Information -->
        <div class="faq-category">
          <h3>Purchasing Information</h3>
          <div class="faq-list">
            <div class="faq-item" @click="toggleFaq('how-to-purchase')">
              <div class="faq-question">
                <span>How do I purchase products from the OMGBUY spreadsheet?</span>
                <span class="toggle-icon">{{ faqStates['how-to-purchase'] ? '−' : '+' }}</span>
              </div>
              <div class="faq-answer" v-if="faqStates['how-to-purchase']">
                <router-link to="/how-to" class="faq-action-btn">check here</router-link>
              </div>
            </div>
            <div class="faq-item" @click="toggleFaq('shipping-time')">
              <div class="faq-question">
                <span>How long will it take to ship the products I ordered through information found on OMGBUY?</span>
                <span class="toggle-icon">{{ faqStates['shipping-time'] ? '−' : '+' }}</span>
              </div>
              <div class="faq-answer" v-if="faqStates['shipping-time']">
                (Merchants on OMGBUY usually ship your items within 3-5 days.)
              </div>
            </div>
            <div class="faq-item" @click="toggleFaq('platform-choice')">
              <div class="faq-question">
                <span>Should I use OMGBUY for information and purchase through its recommended channels, or buy privately?</span>
                <span class="toggle-icon">{{ faqStates['platform-choice'] ? '−' : '+' }}</span>
              </div>
              <div class="faq-answer" v-if="faqStates['platform-choice']">
                (OMGBUY empowers you with comprehensive information – from thousands of product links to genuine QC photos and user evaluations – so you can make the best purchasing decisions)
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import { emitter } from '@/utils/eventBus'
import productsApi from '@/api/products'
import dataManagementApi from '@/api/dataManagement'
import { getSummerNewProductsList } from '@/api/summerNewProducts'
import { getHomeBanners, getFactoryBanners } from '@/api/summerNewProducts'
import EnhancedCarousel from '@/components/EnhancedCarousel.vue'
import {addCollection,removeFavorite} from '@/api/favorites'
import apiClient from '@/services/api'
/* eslint-disable no-unused-vars */
function getPlaceholderImage(name) {
  return `https://via.placeholder.com/300x300?text=${encodeURIComponent(name)}`;
}
/* eslint-enable no-unused-vars */

export default {
  name: 'HomeView',
  components: {
    EnhancedCarousel
  },
  data() {
    return {
      currentSlide: 0,
      products: [],
      bestsellers: [],
      dailyProduct: null, // 每日精选商品
      timeLeft: {
        hours: '00',
        minutes: '00',
        seconds: '00'
      },
      countdown: null,
      faqStates: {
        'what-is-omgbuy': false,
        'omgbuy-purpose': false,
        'leave-review': false,
        'how-to-purchase': false,
        'shipping-time': false,
        'platform-choice': false,
        'what-is-agtfind': false,
        'agtfind-purpose': false
      },
      isLoading: false,
      error: null,
      loadingStates: {
        liking: new Set(),
        collecting: new Set()
      },
      currentCurrency: 'USD',
      currencySymbol: '$',
      exchangeRate: 1,
      onlineUsers: 0,
      shoppingGuides: [], // 购物指南数据
      recommendedProducts: [],
      banners: [],
      isMobile: false,
      factoryBanners: [],
      currentFactorySlide: 0,
      // 添加轮播定时器
      bannerInterval: null,
      factoryInterval: null
    }
  },
  created() {
    // 监听App.vue广播的在线人数事件
    this._onlineUsersHandler = (val) => { this.onlineUsers = val }
    emitter.on('online-users', this._onlineUsersHandler);
    emitter.emit('request-online-users');
    
    // 检测移动设备
    this.checkDevice();
    window.addEventListener('resize', this.onResize);
    
    // Load featured and latest products
    this.fetchFeaturedProducts();
    
    // // 获取每日精选商品
    // this.fetchDailyProduct();
    
    // 初始化倒计时
    this.initCountdown();
    
    // 获取购物指南数据
    this.fetchShoppingGuides();
    
    // Listen for currency change events
    emitter.on('currency-changed', this.updateCurrency);
    
    // Initialize currency information
    this.initCurrency();
    // 获取推荐新品数据
    this.fetchRecommendedProducts();
    this.fetchBanners();
    this.fetchFactoryBanners();
    this.checkProductsCollectionStatus(); // ❌ 移除这行，因为此时数据还没获取到
  },
  mounted() {
    window.addEventListener('resize', this.onResize);
    this.checkDevice();
    
    // 启动工厂轮播图自动播放
    this.startFactoryAutoPlay();
  },
  beforeUnmount() {
    window.removeEventListener('resize', this.onResize);
    
    // 清除工厂轮播图定时器
    if (this.factoryInterval) {
      clearInterval(this.factoryInterval);
    }
    
    emitter.off('currency-changed', this.updateCurrency);
    
    // 清除倒计时定时器
    if (this.countdownTimer) {
      clearInterval(this.countdownTimer);
    }
    emitter.off('online-users', this._onlineUsersHandler);
  },
  computed: {
    // 根据设备类型显示不同数量的推荐商品
    displayedProducts() {
      if (!this.recommendedProducts || this.recommendedProducts.length === 0) {
        return [];
      }
      
      if (this.isMobile) {
        // 移动端：显示14个商品（13个真实商品 + 1个箭头）
        const productsWithoutViewMore = this.recommendedProducts.filter(product => !product.isViewMore);
        // 取前13个商品
        const mobileProducts = productsWithoutViewMore.slice(0, 13);
        // 添加箭头
        mobileProducts.push({
          id: 'view-more-mobile',
          name: '查看更多商品',
          price: '',
          image: require('../assets/omg.png'),
          likes: 0,
          views: 0,
          comments: 0,
          isViewMore: true
        });
        return mobileProducts;
      } else {
        // PC端：显示所有商品（保持原有逻辑）
        return this.recommendedProducts;
      }
    }
  },
  methods: {
    // 删除重复的WebSocket连接代码
    // ws: null,
    // connectWebSocket() {
    //   if (this.ws) {
    //     this.ws.close();
    //   }
    //   this.ws = new window.WebSocket('ws://localhost:8080/ws/online');
    //   this.ws.onopen = () => {
    //     // 连接成功
    //   };
    //   this.ws.onmessage = (event) => {
    //     this.onlineUsers = Number(event.data);
    //   };
    //   this.ws.onclose = () => {
    //     setTimeout(this.connectWebSocket, 2000);
    //   };
    //   this.ws.onerror = () => {
    //     this.ws.close();
    //   };
    // },
    
    // 新增方法：获取产品变体图片
    getProductVariants(product) {
      // 如果产品有variants属性，则使用它
      if (product.variants && product.variants.length > 0) {
        return product.variants;
      }
      
      // 否则生成模拟变体数据（2-3个变体）
      const variantCount = Math.floor(Math.random() * 2) + 2; // 2-3个变体
      const variants = [];
      
      for (let i = 0; i < variantCount; i++) {
        variants.push({
          id: `variant-${i}`,
          image: product.image // 使用相同图片，实际项目中应该使用不同变体图片
        });
      }
      
      return variants;
    },
    
    // 格式化数字显示
    formatViewCount(count) {
      if (!count) return "0";
      if (count < 1000) return count.toString();
      return Math.floor(count / 100) / 10 + 'k';
    },
    
    // 格式化社交统计数据
    formatStatCount(count) {
      if (!count) return "0";
      if (count < 1000) return count.toString();
      return Math.floor(count / 100) / 10 + 'k';
    },

    // 计算总浏览量（真实浏览量 + 虚拟浏览量）
    getTotalViews(product) {
      const realViews = parseInt(product.views || 0);
      const virtualViews = parseInt(product.virtualViews || 0);
      return realViews + virtualViews;
    },
    
    // 计算折扣百分比
    calculateDiscount(currentPrice, originalPrice) {
      return Math.round((1 - currentPrice / originalPrice) * 100);
    },
    
    // 获取每日精选商品
    async fetchDailyProduct() {
      try {
        // 优先使用每日一品接口获取商品
        const dailyResponse = await productsApi.getTodayProduct();
        
        if (dailyResponse.code === 200 && dailyResponse.data && dailyResponse.data.length > 0) {
          // 获取每日一品的商品ID
          const dailyProductId = dailyResponse.data[0].productId;
          
          // 通过商品ID获取商品详情
          const productResponse = await productsApi.getProductDetail(dailyProductId);
          
          if (productResponse.code === 200 && productResponse.data) {
            this.dailyProduct = productResponse.data;
        } else {
            // 如果获取商品详情失败，回退到推荐商品
            await this.fallbackToRecommendedProduct();
          }
        } else {
          // 如果每日一品接口没有返回数据，回退到推荐商品
          await this.fallbackToRecommendedProduct();
        }
      } catch (error) {
        console.error('Failed to fetch daily product:', error);
        // 回退到推荐商品
        await this.fallbackToRecommendedProduct();
      }
    },
    
    // 回退到推荐商品或热门商品
    async fallbackToRecommendedProduct() {
      try {
        // 优先使用推荐接口获取热门商品
        const response = await productsApi.getRecommendProducts('', 1);
        
        if (response.code === 200 && response.data && response.data.length > 0) {
          this.dailyProduct = response.data[0];
        } else {
          // 如果推荐API不可用，尝试使用热门产品API
          const hotResponse = await productsApi.getHotProducts(1);
          if (hotResponse.code === 200 && hotResponse.data && hotResponse.data.length > 0) {
            this.dailyProduct = hotResponse.data[0];
          } else if (this.bestsellers && this.bestsellers.length > 0) {
            // 如果热门API也不可用，使用bestsellers
            this.dailyProduct = this.bestsellers[0];
          }
        }
      } catch (error) {
        console.error('Failed to fetch recommended product:', error);
        // 失败时尝试使用已加载的bestsellers
        if (this.bestsellers && this.bestsellers.length > 0) {
          this.dailyProduct = this.bestsellers[0];
        }
      }
    },
    
    // 初始化倒计时
    initCountdown() {
      // 计算当天剩余时间
      this.updateCountdown();
      
      // 设置定时器，每秒更新倒计时
      this.countdownTimer = setInterval(() => {
        this.updateCountdown();
      }, 1000);
    },
    
    // 更新倒计时显示
    updateCountdown() {
      const now = new Date();
      const endOfDay = new Date(
        now.getFullYear(),
        now.getMonth(),
        now.getDate() + 1,
        0, 0, 0
      );
      
      // 计算剩余时间（毫秒）
      const timeRemaining = endOfDay - now;
      
      if (timeRemaining <= 0) {
        // 如果时间已到，重置倒计时并获取新的每日商品
        this.resetCountdown();
        return;
      }
      
      // 转换为时分秒
      const hours = Math.floor(timeRemaining / (1000 * 60 * 60));
      const minutes = Math.floor((timeRemaining % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((timeRemaining % (1000 * 60)) / 1000);
      
      // 格式化显示
      this.timeLeft = {
        hours: hours.toString().padStart(2, '0'),
        minutes: minutes.toString().padStart(2, '0'),
        seconds: seconds.toString().padStart(2, '0')
      };
    },
    
    // 重置倒计时，获取新的每日商品
    resetCountdown() {
      // 获取新的每日精选商品
      // this.fetchDailyProduct();
      
      // 重新初始化倒计时
      clearInterval(this.countdownTimer);
      this.initCountdown();
    },
    
    async fetchFeaturedProducts() {
      try {
        this.isLoading = true;
        
        // 使用getLatestProducts接口直接获取轮播图产品，不再调用getHotProductList接口
          const latestResponse = await productsApi.getLatestProducts(4);
          if (latestResponse.code === 200 && latestResponse.data) {
            this.products = latestResponse.data;
          } else {
            console.error('Latest products API returned unexpected format:', latestResponse);
        }
        
        // 直接使用热门产品API获取畅销商品列表，不再调用getCurrentProductsList接口
          const hotResponse = await productsApi.getHotProducts(8);
          if (hotResponse.code === 200 && hotResponse.data) {
            this.bestsellers = hotResponse.data;
          } else {
            console.error('Popular products API returned unexpected format:', hotResponse);
        }
        
      } catch (error) {
        console.error('Failed to fetch product data:', error);
        
        // 出错时尝试使用备选方案
        try {
          // 轮播图使用最新产品
          const latestResponse = await productsApi.getLatestProducts(4);
          if (latestResponse.code === 200 && latestResponse.data) {
            this.products = latestResponse.data;
          }
          
          // 畅销商品使用热门产品
          const hotResponse = await productsApi.getHotProducts(8);
          if (hotResponse.code === 200 && hotResponse.data) {
            this.bestsellers = hotResponse.data;
          }
        } catch (fallbackError) {
          console.error('Failed to fetch fallback products:', fallbackError);
        }
      } finally {
        this.isLoading = false;
      }
    },
    goToSlide(index) {
      this.currentSlide = index;
    },
    goToProducts() {
      this.$router.push({
        path: '/products',
        query: {
          categoryId: 20
        }
      })
    },
    goToProductDetail(id, action = 'view') {
      if (!id) {
        console.warn('Product ID is undefined');
        return;
      }
      
      // 处理统计逻辑
      if (action === 'views') {
        // 增加浏览量
        const product = this.recommendedProducts.find(p => p.productId === id);
        if (product) {
          product.views = (product.views || 0) + 1;
        }
      }
      
      this.$router.push(`/product/${id}`);
    },
    toggleFaq(id) {
      this.faqStates[id] = !this.faqStates[id];
    },
    async handleLike(product) {
      if (this.loadingStates.liking.has(product.id)) return;
      
      try {
        this.loadingStates.liking.add(product.id);
        
        // Check if user is logged in
        if (!localStorage.getItem('isLoggedIn')) {
          ElMessage({
            message: 'Please login before liking products',
            type: 'warning',
            duration: 2000
          });
          this.$router.push('/login');
          return;
        }
        
        if (!product.isLiked) {
          // Check if user has already liked this product today
          const checkResponse = await productsApi.checkLikeToday(product.id);
          
          if (checkResponse.code === 200 && checkResponse.data && checkResponse.data.isLikedToday) {
            ElMessage({
              message: 'You have already liked this product today',
              type: 'warning',
              duration: 2000
            });
            return;
          }
          
          // Like product
          await productsApi.likeProduct(product.id);
          product.likes++;
          product.isLiked = true;
          ElMessage({
            message: 'Liked successfully',
            type: 'success',
            duration: 2000
          });
        } else {
          // Unlike product
          await productsApi.unlikeProduct(product.id);
          product.likes--;
          product.isLiked = false;
          ElMessage({
            message: 'Unlike successful',
            type: 'info',
            duration: 2000
          });
        }
      } catch (error) {
        ElMessage({
          message: 'Failed to update like status',
          type: 'error',
          duration: 2000
        });
        console.error('Like error:', error);
      } finally {
        this.loadingStates.liking.delete(product.id);
      }
    },
    formatPrice(price) {
      // 现在后台录入的是人民币价格，需要转换为美元显示
      // exchangeRate 现在表示人民币对美元的汇率，所以需要除法
      return (price / this.exchangeRate).toFixed(2);
    },
    updateCurrency(currencyInfo) {
      this.currentCurrency = currencyInfo.currency;
      this.currencySymbol = currencyInfo.symbol;
      this.exchangeRate = currencyInfo.rate;
    },
    initCurrency() {
      // Try to get currency info from provide/inject
      try {
        const getCurrencyInfo = this.$root.$parent && this.$root.$parent.getCurrencyInfo;
        if (getCurrencyInfo) {
          const currencyInfo = getCurrencyInfo();
          this.updateCurrency(currencyInfo);
        } else {
          // Fallback to local storage
          const savedCurrency = localStorage.getItem('preferredCurrency');
          if (savedCurrency) {
            const currencySymbols = {
              'USD': '$',
              'CNY': '¥',
              'EUR': '€',
              'GBP': '£',
              'JPY': '¥'
            };
            const exchangeRates = {
              'USD': 1,
              'CNY': 7.2,
              'EUR': 0.93,
              'GBP': 0.79,
              'JPY': 150.5
            };
            
            if (currencySymbols[savedCurrency]) {
              this.currentCurrency = savedCurrency;
              this.currencySymbol = currencySymbols[savedCurrency];
              this.exchangeRate = exchangeRates[savedCurrency];
            }
          }
        }
      } catch (e) {
        console.error('Failed to get currency info:', e);
      }
    },
    async fetchShoppingGuides() {
      try {
    const response = await dataManagementApi.getShoppingGuides();
    console.log('API响应:', response); // 检查原始响应
    
    // 尝试多种可能的数据结构路径
    if (response && response.data && response.data.code === 200) {
      this.shoppingGuides = response.data.data;
      console.log('解析后的数据:', this.shoppingGuides);
    } else if (response && response.data && Array.isArray(response.data)) {
      // 直接是数组的情况
      this.shoppingGuides = response.data;
      console.log('解析后的数据(数组):', this.shoppingGuides);
    } else if (response && Array.isArray(response)) {
      // response本身是数组的情况
      this.shoppingGuides = response;
      console.log('解析后的数据(直接数组):', this.shoppingGuides);
    } else {
      console.error('无法解析数据结构:', response);
      
      // 使用硬编码的默认数据
      this.shoppingGuides = [
        {
          id: 1,
          title: "HOW TO SHOP SAFELY",
          context: "AGTFIND is your trusted purchasing service platform...\r\n\r\nVerify seller ratings\r\nUse secure payment\r\nCheck authenticity",
          imagePath: require("@/assets/logo1.png")
        },
        {
          id: 2,
          title: "PRODUCT AUTHENTICATION",
          context: "Learn how to authenticate your products...\r\n\r\nDetailed guides\r\nCommon red flags\r\nQuality procedures",
          imagePath: require("@/assets/logo1.png")
        }
      ];
      console.log('使用默认数据:', this.shoppingGuides);
    }
    
    // 强制更新视图
    this.$forceUpdate();
  } catch (error) {
    console.error('获取购物指南失败:', error);
    // 使用相同的默认数据...
  }
    },
    // 格式化内容文本，将\r\n转换为HTML换行
    formatContext(text) {
      if (!text) return '';
      return text.replace(/\r\n/g, '<br>');
    },
    async fetchRecommendedProducts() {
      try {
        const data = await getSummerNewProductsList();
        // 格式化为页面需要的字段
        this.recommendedProducts = (data || []).map(item => ({
          id: item.id,
          productId: item.productId,
          name: item.productName,
          price: item.price,
          image: item.imageUrl,
          likes: item.likes,
          views: item.views,
          comments: item.comments,
          favorites: item.favorites
        }));
        // 保留"查看更多商品"按钮
        this.recommendedProducts.push({
          id: 'view-more',
          name: '查看更多商品',
          price: '',
          image: require('../assets/omg.png'),
          likes: 0,
          views: 0,
          comments: 0,
          isViewMore: true
        });
      } catch (error) {
        console.error('获取推荐新品失败:', error);
      }
    },
    async fetchBanners() {
      try {
        const res = await getHomeBanners();
        if (res.code === 200 && Array.isArray(res.data)) {
          this.banners = res.data;
        }
      } catch (e) {
        console.error('Failed to fetch banners:', e);
      }
    },
    goToBannerLink(banner) {
      if (!banner || !banner.linkUrl) return;
      if (banner.linkUrl.startsWith('http')) {
        window.open(banner.linkUrl, '_blank');
      } else {
        this.$router.push(banner.linkUrl);
      }
    },
    checkDevice() {
      // 检测是否为移动设备
      this.isMobile = window.innerWidth <= 768;
    },
    onResize() {
      this.checkDevice();
    },
    async fetchFactoryBanners() {
      try {
        // 调用新的API接口获取工厂照片轮播图数据
        const res = await getFactoryBanners();
        if (res.code === 200 && Array.isArray(res.data)) {
          this.factoryBanners = res.data;
        } else {
          console.warn('Factory banners API returned unexpected format:', res);
          // 提供一些默认数据以防API调用失败
          this.factoryBanners = [
            {
              id: 1,
              title: 'Factory Photo 1',
              description: 'Quality manufacturing',
              imageUrl: 'https://via.placeholder.com/1200x500?text=Factory+Photo+1',
              linkUrl: '#'
            },
            {
              id: 2,
              title: 'Factory Photo 2',
              description: 'Production line',
              imageUrl: 'https://via.placeholder.com/1200x500?text=Factory+Photo+2',
              linkUrl: '#'
            },
            {
              id: 3,
              title: 'Factory Photo 3',
              description: 'Quality control',
              imageUrl: 'https://via.placeholder.com/1200x500?text=Factory+Photo+3',
              linkUrl: '#'
            }
          ];
        }
      } catch (error) {
        console.error('Failed to fetch factory banners:', error);
        // 出错时也提供默认数据
        this.factoryBanners = [
          {
            id: 1,
            title: 'Factory Photo 1',
            description: 'Quality manufacturing',
            imageUrl: 'https://via.placeholder.com/1200x500?text=Factory+Photo+1',
            linkUrl: '#'
          },
          {
            id: 2,
            title: 'Factory Photo 2',
            description: 'Production line',
            imageUrl: 'https://via.placeholder.com/1200x500?text=Factory+Photo+2',
            linkUrl: '#'
          }
        ];
      }
    },
    handleFactoryBannerClick(data) {
      // 兼容EnhancedCarousel组件的事件格式
      const banner = data.slide || data;
      // 实现点击工厂照片的逻辑
      if (banner && banner.linkUrl) {
        // 如果有链接，则导航到该链接
        if (banner.linkUrl.startsWith('http')) {
          window.open(banner.linkUrl, '_blank');
        } else {
          this.$router.push(banner.linkUrl);
        }
      }
    },
    viewAllFactoryPhotos() {
      // 实现查看所有工厂照片的逻辑
      this.$router.push('/factory-photos');
    },
    // 新增收藏方法
    async handleCollect(product) {
      // 检查登录状态
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      if (!userInfo.userId && !userInfo.id) {
        ElMessage.warning('请先登录后再收藏商品');
        return;
      }

      // 防止重复点击
      if (this.loadingStates.collecting.has(product.productId)) {
        return;
      }

      this.loadingStates.collecting.add(product.productId);

      try {
        if (product.isCollected) {
          // 取消收藏
          const response = await removeFavorite(product.productId);
          if (response.code === 200) {
            product.isCollected = false;
            ElMessage.success('已取消收藏');
          } else {
            ElMessage.error(response.msg || '取消收藏失败');
          }
        } else {
          // 添加收藏
          const response = await addCollection(product.productId);
          if (response.code === 200) {
            product.isCollected = true;
            ElMessage.success('收藏成功');
          } else {
            ElMessage.error(response.msg || '收藏失败');
          }
        }
      } catch (error) {
        console.error('收藏操作失败:', error);
        ElMessage.error('操作失败，请稍后重试');
      } finally {
        this.loadingStates.collecting.delete(product.productId);
      }
    },
    
    // 批量检查收藏状态
    async checkProductsCollectionStatus() {
  
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      if (!userInfo.userId && !userInfo.id) {
        return; // 未登录用户不检查收藏状态
      }

      try {

        // 获取所有商品ID
        const productIds = this.recommendedProducts
          .filter(product => !product.isViewMore && product.productId)
          .map(product => product.productId);
          console.log(productIds,"12312311323231");
          
          
          
        if (productIds.length === 0) return;

       // 批量检查收藏状态
       const response = await apiClient.post(`/omg/collect/isCollected`, {
          userId: JSON.parse(localStorage.getItem('userInfo')).userId,
          productIds: productIds
        });
    

        if (response.code === 200 && response.data) {
          // 更新每个商品的收藏状态
          this.recommendedProducts.forEach(product => {
            product.isCollected = response.data[product.id] || false;
          });
          
          // 强制更新视图
          this.$forceUpdate();
        }
      } catch (error) {
        console.error('批量检查收藏状态失败:', error);
      }
    },
    
    // 新增：处理轮播图点击事件，兼容EnhancedCarousel组件
    handleBannerClick(data) {
      const banner = data.slide;
      if (!banner || !banner.linkUrl) return;
      if (banner.linkUrl.startsWith('http')) {
        window.open(banner.linkUrl, '_blank');
      } else {
        this.$router.push(banner.linkUrl);
      }
    },
    // 工厂轮播图方法
    prevFactorySlide() {
      if (this.currentFactorySlide > 0) {
        this.currentFactorySlide--;
      } else {
        this.currentFactorySlide = this.factoryBanners.length - 1;
      }
    },
    nextFactorySlide() {
      if (this.currentFactorySlide < this.factoryBanners.length - 1) {
        this.currentFactorySlide++;
      } else {
        this.currentFactorySlide = 0;
      }
    },
    startFactoryAutoPlay() {
      this.factoryInterval = setInterval(() => {
        this.nextFactorySlide();
      }, 3000);
    },
    stopFactoryAutoPlay() {
      clearInterval(this.factoryInterval);
    },
    // 暂停工厂轮播图自动播放
    pauseFactoryAutoSlide() {
      if (this.factoryInterval) {
        clearInterval(this.factoryInterval);
        this.factoryInterval = null;
      }
    },
    // 恢复工厂轮播图自动播放
    resumeFactoryAutoSlide() {
      if (!this.factoryInterval) {
        this.startFactoryAutoPlay();
      }
    },
    // 跳转到指定的工厂轮播图幻灯片
    goToFactorySlide(index) {
      this.currentFactorySlide = index;
    }
  },
}
</script>

<style scoped>
.home {
  padding: 2rem 5rem;
  background: #1e0940; /* 更深的紫色背景 */
  background-image: linear-gradient(125deg, #1e0940 0%, #380d6d 50%, #1e0940 100%);
  min-height: 100vh;
  color: #e0e0e0;
  position: relative;
  overflow: hidden;
}

.intro-section,
.products,
.recommended-products,
.bestseller,
.about,
.faq {
  background: linear-gradient(135deg, rgba(30, 25, 40, 0.6), rgba(20, 15, 30, 0.6));
  position: relative;
  z-index: 1;
  backdrop-filter: blur(5px);
  pointer-events: auto;
}

.intro-section {
  background: linear-gradient(135deg, rgba(36, 36, 40, 0.7), rgba(26, 26, 32, 0.7));
  padding: 3rem 2rem;
  position: relative;
  overflow: hidden;
  border-bottom: 1px solid rgba(120, 70, 200, 0.3);
}

.intro-container {
  max-width: 1400px;
  margin: 0 auto;
  position: relative;
}

.intro-content {
  width: 800px;
  margin: 0 auto;
  text-align: center;
}

.intro-content h1 {
  color: #ffffff;
  font-size: 2.8rem;
  margin-bottom: 1rem;
  font-weight: bold;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.intro-content p {
  color: #e0e0e0;
  font-size: 1.4rem;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.intro-content .sub-text {
  color: #a9a9a9;
  font-size: 1.3rem;
  max-width: 800px;
  margin: 0 auto;
}

/* 优化在线用户计数器样式 - 强化科技感 */
.online-counter {
  position: absolute;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, rgba(15, 15, 30, 0.9), rgba(25, 20, 50, 0.95));
  padding: 2rem;
  border-radius: 20px;
  min-width: 250px;
  text-align: center;
  border: 3px solid rgba(120, 90, 255, 0.8);
  box-shadow: 
    0 10px 30px rgba(0, 0, 0, 0.7), 
    0 0 40px rgba(120, 90, 255, 0.7),
    inset 0 0 20px rgba(120, 90, 255, 0.3);
  backdrop-filter: blur(10px);
  transition: all 0.4s cubic-bezier(0.165, 0.84, 0.44, 1);
  animation: float-counter 4s infinite alternate ease-in-out;
  overflow: hidden;
  z-index: 10;
}

/* 添加外部发光效果 */
.counter-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 20px;
  z-index: -1;
  animation: glow-border 2s infinite alternate;
  pointer-events: none;
}

@keyframes glow-border {
  0% {
    box-shadow: 
      0 0 15px rgba(120, 90, 255, 0.7),
      0 0 30px rgba(120, 90, 255, 0.4),
      0 0 45px rgba(120, 90, 255, 0.2);
  }
  100% {
    box-shadow: 
      0 0 20px rgba(120, 90, 255, 0.9),
      0 0 40px rgba(120, 90, 255, 0.6),
      0 0 60px rgba(120, 90, 255, 0.3);
  }
}

/* 添加扫描线效果 */
.scan-line {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgba(120, 90, 255, 0.3) 15%, 
    rgba(150, 120, 255, 0.9) 50%,
    rgba(120, 90, 255, 0.3) 85%,
    transparent 100%
  );
  box-shadow: 0 0 10px rgba(120, 90, 255, 0.8);
  animation: scan-line 2.5s infinite linear;
  opacity: 0.9;
  z-index: 3;
  pointer-events: none;
}

@keyframes scan-line {
  0% {
    top: 0%;
  }
  100% {
    top: 100%;
  }
}

@keyframes float-counter {
  0% {
    transform: translateY(-50%);
    border-color: rgba(120, 90, 255, 0.8);
    box-shadow: 
      0 10px 30px rgba(0, 0, 0, 0.7), 
      0 0 40px rgba(120, 90, 255, 0.6),
      inset 0 0 20px rgba(120, 90, 255, 0.3);
  }
  50% {
    border-color: rgba(150, 120, 255, 1);
    box-shadow: 
      0 15px 35px rgba(0, 0, 0, 0.8), 
      0 0 50px rgba(120, 90, 255, 0.8),
      inset 0 0 25px rgba(120, 90, 255, 0.4);
  }
  100% {
    transform: translateY(-56%);
    border-color: rgba(120, 90, 255, 0.8);
    box-shadow: 
      0 10px 30px rgba(0, 0, 0, 0.7), 
      0 0 40px rgba(120, 90, 255, 0.6),
      inset 0 0 20px rgba(120, 90, 255, 0.3);
  }
}

.online-counter:hover {
  border-color: rgba(150, 120, 255, 1);
  transform: translateY(-55%) scale(1.05);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.8), 
    0 0 60px rgba(120, 90, 255, 0.9),
    inset 0 0 30px rgba(120, 90, 255, 0.5);
}

.counter-label {
  color: rgba(255, 255, 255, 0.95);
  font-size: 1.2rem;
  margin-bottom: 1.2rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 3px;
  position: relative;
  display: inline-block;
  text-shadow: 
    0 0 5px #fff,
    0 0 10px rgba(120, 90, 255, 0.8),
    0 0 15px rgba(120, 90, 255, 0.5);
}

.counter-label::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80%;
  height: 1px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(120, 90, 255, 0.6), 
    rgba(150, 120, 255, 1), 
    rgba(120, 90, 255, 0.6), 
    transparent
  );
  box-shadow: 0 0 8px rgba(120, 90, 255, 0.8);
}

.counter-label::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50%;
  height: 3px;
  background: linear-gradient(90deg, 
    transparent, 
    rgba(120, 90, 255, 0.4), 
    rgba(150, 120, 255, 0.8), 
    rgba(120, 90, 255, 0.4), 
    transparent
  );
  border-radius: 3px;
  filter: blur(1px);
  box-shadow: 0 0 8px rgba(120, 90, 255, 0.8);
}

.counter-number {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  color: #ffffff;
  font-size: 2.6rem;
  font-weight: bold;
  text-shadow: 
    0 0 10px #fff,
    0 0 20px rgba(120, 90, 255, 0.9),
    0 0 30px rgba(120, 90, 255, 0.5);
  position: relative;
  z-index: 2;
  margin: 0.5rem 0;
}

.counter-number i {
  color: rgba(150, 120, 255, 1);
  font-size: 2.2rem;
  filter: drop-shadow(0 0 10px rgba(120, 90, 255, 1));
  animation: pulse-icon 3s infinite alternate;
  position: relative;
}

.counter-number i::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 180%;
  height: 180%;
  background: radial-gradient(circle, 
    rgba(150, 120, 255, 0.6) 0%, 
    rgba(120, 90, 255, 0.4) 40%, 
    transparent 70%
  );
  border-radius: 50%;
  z-index: -1;
  animation: glow-pulse 3s infinite alternate;
  opacity: 0.9;
  box-shadow: 0 0 20px rgba(120, 90, 255, 0.8);
}

@keyframes glow-pulse {
  0% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(0.8);
    box-shadow: 0 0 15px rgba(120, 90, 255, 0.6);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.3);
    box-shadow: 0 0 30px rgba(120, 90, 255, 0.9);
  }
}

@keyframes pulse-icon {
  0% {
    transform: scale(1);
    opacity: 0.9;
    filter: drop-shadow(0 0 10px rgba(120, 90, 255, 0.8));
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
    filter: drop-shadow(0 0 15px rgba(120, 90, 255, 1));
  }
  100% {
    transform: scale(1.2);
    opacity: 0.9;
    filter: drop-shadow(0 0 10px rgba(120, 90, 255, 0.8));
  }
}

/* 数字闪烁效果增强 */
.counter-number span {
  position: relative;
  display: inline-block;
  overflow: hidden;
  animation: number-flicker 5s infinite;
  filter: drop-shadow(0 0 5px rgba(255, 255, 255, 0.8));
}

@keyframes number-flicker {
  0%, 100% { opacity: 1; }
  92% { opacity: 1; }
  93% { opacity: 0.4; }
  94% { opacity: 1; }
  96% { opacity: 0.6; }
  97% { opacity: 1; }
  98% { opacity: 0.2; }
  99% { opacity: 1; }
}

/* 更新响应式样式 */
@media (max-width: 1400px) {
  .online-counter {
    right: 2rem;
  }
}

@media (max-width: 1200px) {
  .intro-container {
    max-width: 1000px;
    position: relative;
  }

  .online-counter {
    right: 1rem;
  }
}

@media (max-width: 1024px) {
  .intro-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2rem;
  }

  .intro-content {
    width: 100%;
    max-width: 800px;
  }

  .online-counter {
    position: static;
    transform: none;
    margin-top: 1.5rem;
    animation: float-counter-mobile 4s infinite alternate ease-in-out;
    width: 100%;
    max-width: 320px;
  }
  
  @keyframes float-counter-mobile {
    0% {
      transform: translateY(0);
      border-color: rgba(120, 90, 255, 0.8);
      box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.7), 
        0 0 40px rgba(120, 90, 255, 0.6),
        inset 0 0 20px rgba(120, 90, 255, 0.3);
    }
    50% {
      border-color: rgba(150, 120, 255, 1);
      box-shadow: 
        0 15px 35px rgba(0, 0, 0, 0.8), 
        0 0 50px rgba(120, 90, 255, 0.8),
        inset 0 0 25px rgba(120, 90, 255, 0.4);
    }
    100% {
      transform: translateY(-8px);
      border-color: rgba(120, 90, 255, 0.8);
      box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.7), 
        0 0 40px rgba(120, 90, 255, 0.6),
        inset 0 0 20px rgba(120, 90, 255, 0.3);
    }
  }
  
  .online-counter:hover {
    transform: translateY(-10px) scale(1.03);
  }
}

@media (max-width: 768px) {
  .intro-section {
    padding: 2rem 1rem;
  }

  .intro-content {
    max-width: 100%;
  }

  .online-counter {
    width: 100%;
    max-width: 280px;
    padding: 1.5rem;
  }
  
  .counter-label {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
  
  .counter-number {
    font-size: 2.2rem;
  }
  
  .counter-number i {
    font-size: 2rem;
  }
}

@media (max-width: 480px) {
  .online-counter {
    max-width: 240px;
    padding: 1.2rem;
  }
  
  .counter-label {
    font-size: 0.9rem;
    margin-bottom: 0.8rem;
  }
  
  .counter-number {
    font-size: 2rem;
    gap: 0.8rem;
  }
  
  .counter-number i {
    font-size: 1.8rem;
  }
}

.daily-deal {
  padding: 2rem;
  background: linear-gradient(135deg, #242428, #1a1a20);
  margin-bottom: 2rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

.daily-deal-container {
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  gap: 1.5rem;
}

.daily-deal-title {
  text-align: center;
  color: #ffffff;
  font-size: 2.2rem;
  text-transform: uppercase;
  margin-bottom: 0.5rem;
  letter-spacing: 2px;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.daily-product-card {
  display: flex;
  background: linear-gradient(135deg, #242428, #1a1a20);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
  border: 1px solid rgba(80, 80, 100, 0.3);
}

.daily-product-card:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.5);
  background: linear-gradient(135deg, #2d2d35, #22222a);
}

.daily-product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, rgba(255, 255, 255, 0.15), transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  border-radius: 15px;
  z-index: 1;
}

.daily-product-card:hover::before {
  opacity: 1;
}

.product-image-container {
  flex: 1.5;
  overflow: hidden;
  background-color: #1a1a1a;
}

.product-image {
  width: 100%;
  height: 250px;
  overflow: hidden;
  position: relative;
}

.product-image::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: none; /* 移除径向渐变背景 */
  opacity: 0;
  transition: opacity 0.4s ease;
}

.product-image::before {
  content: '';
  position: absolute;
  top: -80px;
  right: -80px;
  width: 160px;
  height: 160px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.7) 30%, rgba(255, 255, 255, 0.3) 60%, rgba(255, 255, 255, 0) 80%);
  border-radius: 50%;
  opacity: 0;
  transform: scale(0.3);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 5;
  pointer-events: none;
  filter: blur(5px);
}

.product-card:hover .product-image::before {
  opacity: 1;
  transform: scale(1);
  animation: pulseGlow 2s infinite alternate;
}

/* 轮播图片右上角光晕效果 */
.slide-image-container::before {
  content: '';
  position: absolute;
  top: -80px;
  right: -80px;
  width: 160px;
  height: 160px;
  background: radial-gradient(circle, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.7) 30%, rgba(255, 255, 255, 0.3) 60%, rgba(255, 255, 255, 0) 80%);
  border-radius: 50%;
  opacity: 0;
  transform: scale(0.3);
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  z-index: 5;
  pointer-events: none;
  filter: blur(5px);
}

.carousel-slide:hover .slide-image-container::before {
  opacity: 1;
  transform: scale(1);
  animation: pulseGlow 2s infinite alternate;
}

.product-card:hover .product-image::after {
  opacity: 0; /* 悬停时不显示 */
}

.product-card:hover .product-image {
  transform: none; /* 移除放大效果 */
  filter: none; /* 移除发光效果 */
}

.product-right-container {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* PC端倒计时区块 */
.countdown-block {
  background: linear-gradient(135deg, #242428, #1a1a20);
  color: #e0e0e0;
  padding: 2rem;
  text-align: center;
  flex: 1;
}

.countdown-title {
  font-size: 1.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  letter-spacing: 1px;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.time-block {
  background: rgba(30, 30, 40, 0.7);
  color: #ffffff;
  border-radius: 8px;
  padding: 0.8rem;
  min-width: 70px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.time {
  font-size: 2rem;
  font-weight: bold;
  color: #ffffff;
  line-height: 1;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.label {
  font-size: 0.8rem;
  color: #a9a9a9;
  margin-top: 0.3rem;
  text-transform: uppercase;
}

.time-separator {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-top: -5px;
}

/* PC端产品信息显示 */
.product-info {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  background: rgba(35, 35, 40, 0.9);
  text-align: center;
  flex: 2;
}

.discount-badge {
  background: linear-gradient(135deg, #242428, #1a1a20);
  color: #e0e0e0;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
  display: inline-block;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.product-id {
  font-size: 1.8rem;
  color: #ffffff;
  margin: 0;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.price-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.8rem;
  margin: 0.5rem 0;
}

.current-price {
  font-size: 1.6rem;
  font-weight: bold;
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.original-price {
  font-size: 1.2rem;
  color: #888;
  text-decoration: line-through;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  padding: 0.8rem 0.5rem;
  margin-top: 0.5rem;
  border-top: 1px solid rgba(138, 43, 226, 0.2);
  background: rgba(0, 0, 0, 0.2);
  border-radius: 8px;
}

.product-stats span {
  display: flex;
  align-items: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.85rem;
  transition: all 0.3s ease;
}

.product-stats i {
  margin-right: 0.4rem;
  font-size: 0.9rem;
  color: rgba(138, 43, 226, 0.7);
  transition: all 0.3s ease;
}

.product-stats span:hover {
  color: #ffffff;
  transform: translateY(-2px);
}

.product-stats span:hover i {
  color: rgba(138, 43, 226, 1);
}

.product-card:hover .product-stats i {
  color: rgba(138, 43, 226, 0.9);
}

.buy-now-btn {
  background: linear-gradient(135deg, #242428, #1a1a20);
  color: #e0e0e0;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 30px;
  font-size: 1.1rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 0.5rem;
  align-self: center;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.buy-now-btn:hover {
  background: linear-gradient(135deg, #2d2d35, #22222a);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.6);
}

/* 移动端样式 - 在媒体查询中覆盖PC端样式 */
@media (max-width: 768px) {
  .daily-deal-container {
    max-width: 350px;
    gap: 1rem;
  }
  
  .daily-deal-title {
    font-size: 1.8rem;
    font-weight: bold;
    text-shadow: none;
  }
  
  /* 移动端卡片变为垂直布局 */
  .daily-product-card {
    flex-direction: column;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #eee;
    max-width: 100%;
  }
  
  .daily-product-card:hover {
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
  }
  
  /* 移动端图片容器固定高度 */
  .product-image-container {
    flex: none;
    height: 200px;
    width: 100%;
  }
  
  .product-image {
    transition: transform 0.3s ease;
  }
  
  /* 移动端右侧容器也改为垂直布局 */
  .product-right-container {
    flex-direction: column;
  }
  
  /* 移动端倒计时区块独立显示 */
  .countdown-block {
    padding: 0.8rem;
  }
  
  .countdown-title {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
  }
  
  .countdown-timer {
    gap: 0.3rem;
  }
  
  .time-block {
    padding: 0.5rem;
    min-width: 50px;
  }
  
  .time {
    font-size: 1.5rem;
  }
  
  .label {
    font-size: 0.7rem;
  }
  
  .time-separator {
    font-size: 1.5rem;
  }
  
  /* 移动端产品信息区域样式 */
  .product-info {
    padding: 1rem;
    gap: 0.8rem;
  }
  
  .product-id {
    font-size: 1.5rem;
  }
  
  .price-info {
    gap: 0.8rem;
    margin: 0;
  }
  
  .current-price {
    font-size: 1.5rem;
  }
  
  .original-price {
    font-size: 1.1rem;
  }
  
  .product-stats {
    gap: 1.2rem;
    margin: 0.5rem 0;
  }
  
  .stat-item {
    gap: 0.3rem;
    font-size: 1rem;
  }
  
  .buy-now-btn {
    padding: 0.8rem;
    font-size: 1rem;
    margin-top: 0.5rem;
  }
}

.products {
  padding: 0rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #242428, #1a1a20);
}

.products h2 {
  text-align: center;
  color: #ffffff;
  font-size: 2.2rem;
  margin-bottom: 0rem;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.carousel-container {
  position: relative;
  width: 100%;
  max-width: 1398px; /* 设置最大宽度为1398px */
  min-width: 400px;  /* 设置最小宽度为400px */
  margin: 0 auto;
  overflow: hidden;
  border-radius: 15px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(80, 80, 100, 0.3);
  background: linear-gradient(135deg, #242428, #1a1a20);
  /* 调整宽高比 */
  aspect-ratio: 21 / 9;
  min-height: 300px;
  max-height: 500px;
}

/* 调整图片容器样式 */
.slide-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #000;
}

.carousel {
  display: flex;
  transition: transform 0.5s ease;
  height: 100%; /* 让高度自适应容器 */
}

.carousel-slide {
  min-width: 100%;
  position: relative;
  height: 100%;
  overflow: hidden;
  cursor: pointer;
  background-color: #1a1a1a;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  border: 1px solid rgba(80, 80, 100, 0.3);
  transform: translateZ(0);
  will-change: transform, box-shadow, border-color;
}

.carousel-slide:hover {
  transform: translateY(-5px); /* 只保留轻微上移效果 */
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5); /* 只保留基础阴影 */
  border-color: rgba(255, 255, 255, 0.5); /* 减弱边框高亮 */
  z-index: 1;
}

.slide-image-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.slide-background {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 改为 contain 确保图片完整显示 */
  object-position: center;
  display: block;
  max-height: 100%;
  background-color: #000; /* 添加黑色背景 */
}

.carousel-slide:hover .slide-background {
  transform: none; /* 移除放大效果 */
  filter: none; /* 移除发光效果 */
}

.product-info-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  background: rgba(35, 35, 40, 0.9);
  backdrop-filter: blur(2px);
  z-index: 2;
  text-align: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.3);
}

.product-info-overlay h3 {
  font-size: 1.8rem;
  color: #ffffff;
  margin-bottom: 0.5rem;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.product-card-price {
  display: flex;
  justify-content: center;
  gap: 1rem;
}

.current-price {
  font-size: 1.6rem;
  font-weight: bold;
  color: #ffffff;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #333333, #222222);
  color: #e0e0e0;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  cursor: pointer;
  transition: all 0.3s ease;
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.carousel-btn:hover {
  background: linear-gradient(135deg, #444444, #333333);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.6);
}

.carousel-btn.prev {
  left: 20px;
}

.carousel-btn.next {
  right: 20px;
}

.carousel-dots {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 0.5rem;
  z-index: 10;
}

.dot {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.dot:hover {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.2);
}

.dot.active {
  background: #ffffff;
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.view-all-btn {
  background: linear-gradient(135deg, #333333, #222222);
  color: #e0e0e0;
  border: none;
  padding: 0.8rem 2rem;
  border-radius: 30px;
  cursor: pointer;
  font-weight: bold;
  transition: all 0.3s ease;
  z-index: 10;
  font-size: 1rem;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.3);
}

.view-all-btn:hover {
  background: linear-gradient(135deg, #444444, #333333);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(255, 255, 255, 0.4);
  border-color: rgba(255, 255, 255, 0.6);
}

/* 在小屏幕设备上稍微调整按钮尺寸和位置 */
@media (max-width: 768px) {
  .carousel-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .carousel-btn.prev {
    left: 10px;
  }
  
  .carousel-btn.next {
    right: 10px;
  }
  
  /* 调整轮播图容器，移除下方多余空间 */
  .carousel-container {
    margin-bottom: 0;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    min-height: 200px; /* 减少最小高度 */
    aspect-ratio: 16/9; /* 更改宽高比 */
    min-width: unset; /* 移动端取消最小宽度限制 */
  }
  
  .carousel {
    height: 100%; /* 使用100%容器高度 */
  }
  
  .carousel-slide {
    height: 100%; /* 使用100%容器高度 */
  }
  
  /* 将指示点放在轮播图内部底部，不占用额外空间 */
  .carousel-dots {
    bottom: 8px; /* 放在轮播图内部底部 */
    gap: 0.3rem;
    padding: 3px 8px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 10px;
  }
  
  /* 调整小圆点尺寸，使其在移动端更紧凑 */
  .dot {
    width: 6px;
    height: 6px;
    margin: 0 2px;
    background: rgba(255, 255, 255, 0.5);
  }
  
  .dot.active {
    background: #ffffff;
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.8);
  }
  
  /* 确保轮播图下方的内容紧接着 */
  .recommended-products {
    margin-top: 0;
    padding-top: 1.5rem;
  }
  
  /* 轮播图部分下方不需要额外间距 */
  .intro-section {
    padding-bottom: 0;
  }
}

.bestseller {
  padding: 2rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #242428, #1a1a20);
}

.bestseller h2 {
  text-align: center;
  margin-bottom: 2rem;
  color: #ffffff;
  font-size: 2.2rem;
  margin-bottom: 2rem;
  font-weight: bold;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.bestseller-container {
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
  display: flex;
  justify-content: center;
}

.bestseller-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rem 1.5rem; /* 上下间距2rem，左右间距1.5rem */
  padding: 1rem; /* 给网格容器添加内边距 */
  width: 100%;
}

.bestseller-card {
  background: white;
  border-radius: 25px;
  overflow: hidden;
  cursor: pointer;
  border: 5px solid #5e2b94;
  position: relative;
  transition: transform 0.3s ease;
  box-shadow: none;
  padding: 0;
}

.product-card:hover {
  transform: none;
  box-shadow: none;
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.product-card:hover::before {
  opacity: 1;
}

.product-image {
  height: 250px; /* 从220px增加到250px，让图片区域更大 */
  width: 100%; /* 恢复为100% */
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; /* 改为透明，消除黑色背景 */
  position: relative;
  padding: 0; /* 移除所有内边距，让图片占满整个容器 */
  border-radius: 0; /* 移除容器圆角 */
  margin: 0; /* 移除负边距，使用宽度扩展 */
  transform: scaleX(1.1); /* 水平方向放大1.2倍，真正扩宽容器 */
  margin-top: -10px; /* 向上延伸，与上边框重合 */
}

.product-image img {
  width: 100%; /* 宽度占满整个容器 */
  height: 100%; /* 高度占满整个容器 */
  object-fit: cover; /* 使用cover让图片填满整个容器 */
  transition: transform 0.5s ease, filter 0.5s ease;
  filter: drop-shadow(0 0 8px rgba(120, 70, 200, 0.3));
  border-radius: 0; /* 移除圆角，变成直角 */
  transform: scale(1.05); /* 稍微放大图片，让它更好地填满扩展的容器 */
}

/* 右上角半球形紫色光晕效果 */
.product-image::after {
  content: '';
  position: absolute;
  top: -100px;
  right: -70px;
  width: 230px;
  height: 230px;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.8) 0%, rgba(138, 43, 226, 0.4) 50%, rgba(138, 43, 226, 0) 80%);
  border-radius: 50%;
  filter: blur(18px);
  opacity: 0.7;
  z-index: 1;
  pointer-events: none;
  transition: all 0.5s ease;
  mix-blend-mode: screen; /* 确保光晕与背景混合，不会完全覆盖图片 */
}

/* 鼠标悬停时光晕变成白色 */
.product-card:hover .product-image::after {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 80%);
  opacity: 0.8;
  filter: blur(20px);
}

.product-details h3 {
  color: #ffffff;
  font-size: 1.2rem;
  margin-bottom: 0.8rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s ease;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

.product-card:hover .product-details h3 {
  color: #ffffff;
  text-shadow: 0 0 15px rgba(255, 255, 255, 0.5);
}

.price {
  color: #c3a3ff;
  font-size: 1.4rem;
  font-weight: bold;
  margin-bottom: 1rem;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
}

.product-card:hover .price {
  text-shadow: 0 0 15px rgba(195, 163, 255, 0.5);
}

.product-stats {
  display: flex;
  gap: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  padding: 0.5rem 0;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.product-stats span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  transition: color 0.3s ease;
}

.product-stats i {
  color: #ffffff;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.product-card:hover .product-stats span {
  color: #ffffff;
}

.product-card:hover .product-stats i {
  transform: scale(1.1);
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

/* Responsive adjustments */
@media (max-width: 1200px) {
  .product-card {
    border-radius: 12px;
  }
  
  .product-image {
    height: 160px;
  }
  
  .bestseller-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
  
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .product-card {
    border-radius: 10px;
  }
  
  .product-image {
    height: 140px;
  }
  
  .product-name {
    font-size: 0.9rem;
  }

  .current-price {
    font-size: 1.1rem;
  }
  
  .bestseller-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .about-container {
    flex-direction: column;
    gap: 2rem;
  }
}

@media (max-width: 480px) {
  .bestseller-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 5px;
    width: 100%;
    box-sizing: border-box;
  }
  
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 5px;
    width: 100%;
    box-sizing: border-box;
  }
  
  .recommended-products, .bestseller {
    padding: 2rem 0.8rem;
  }

  .recommended-products h2, .bestseller h2 {
    font-size: 1.5rem;
    margin-bottom: 1.5rem;
  }
  
  /* For consistent styling in mobile view */
  .view-more-card {
    min-height: 280px;
  }
  
  .custom-arrow {
    width: 80px;
    height: 80px;
  }
  
  .arrow-circle {
    width: 70px;
    height: 70px;
  }
  
  .arrow-icon {
    font-size: 1.5rem;
  }
}

/* Touch device optimization */
@media (hover: none) {
  .product-card {
    transform: none !important;
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.2) !important;
    background: linear-gradient(145deg, #242428, #1a1a20) !important;
    border-radius: 12px !important;
    border: 1px solid rgba(138, 43, 226, 0.2) !important;
  }
  
  /* 确保触摸设备上也能看到光晕效果 */
  .product-card::before,
  .bestseller-card::before {
    opacity: 0.4 !important;
    border-radius: 12px !important;
  }
  
  .product-image::before {
    opacity: 0.2 !important;
    transform: scale(0.5) !important;
    top: -100px !important;
    right: -100px !important;
    width: 200px !important;
    height: 200px !important;
    filter: blur(8px) !important;
  }
  
  /* 调整光晕动画使其更适合移动端 */
  @keyframes mobileSoftGlow {
    0% {
      opacity: 0.2;
      filter: blur(8px);
    }
    100% {
      opacity: 0.4;
      filter: blur(12px);
    }
  }
  
  .product-card:active .product-image::before,
  .bestseller-card:active .product-image::before {
    animation: mobileSoftGlow 1s infinite alternate;
  }
}

.countdown-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  padding: 1.5rem;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(3px);
  z-index: 2;
}

.countdown-title {
  font-size: 1.4rem;
  color: white;
  margin-bottom: 1rem;
  font-weight: 600;
  text-align: center;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

.countdown-timer {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 0.5rem;
}

.time-block {
  background: rgba(255, 255, 255, 0.85);
  border-radius: 8px;
  padding: 0.8rem;
  min-width: 70px;
  text-align: center;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.time {
  font-size: 2.2rem;
  font-weight: bold;
  color: #2E7D32;
  line-height: 1;
  margin-bottom: 0.5rem;
}

.label {
  font-size: 0.9rem;
  color: #558b2f;
  font-weight: 500;
}

.time-separator {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  margin-top: -5px;
}

@media (max-width: 768px) {
  .countdown-overlay {
    padding: 1rem;
  }
  
  .countdown-title {
    font-size: 1.2rem;
    margin-bottom: 0.5rem;
  }
  
  .time-block {
    padding: 0.6rem;
    min-width: 55px;
  }
  
  .time {
    font-size: 1.8rem;
  }
  
  .time-separator {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .countdown-overlay {
    padding: 0.8rem;
  }
  
  .countdown-title {
    font-size: 1rem;
    margin-bottom: 0.4rem;
  }
  
  .time-block {
    padding: 0.5rem;
    min-width: 45px;
  }
  
  .time {
    font-size: 1.5rem;
    margin-bottom: 0.3rem;
  }
  
  .label {
    font-size: 0.7rem;
  }
  
  .time-separator {
    font-size: 1.5rem;
  }
}

/* 超小屏幕设备适配 */
@media (max-width: 480px) {
  /* 更新移动端产品卡片样式 */
  .product-card {
    background: linear-gradient(145deg, #242428, #1a1a20);
    border-radius: 12px;
    padding: 0.5rem;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 10px rgba(138, 43, 226, 0.2);
    border: 1px solid rgba(138, 43, 226, 0.2);
    overflow: hidden;
  }
  
  .product-card::before {
    border-radius: 12px;
  }
  
  .product-image {
    height: 130px;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    overflow: hidden;
    background: #1a1a1a;
    width: 100%;
  }
  
  .product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
  }
  
  .product-details {
    padding: 0.3rem 0.2rem;
  }
  
  .product-details h3 {
    font-size: 0.9rem;
    margin: 0 0 0.3rem 0;
    line-height: 1.2;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .price {
    font-size: 1.1rem;
    margin: 0 0 0.3rem 0;
  }
  
  /* Adjust the grid spacing */
  .products-grid, .bestseller-grid {
    gap: 8px;
  }
  
  .recommended-products, .bestseller {
    padding: 1.5rem 0.6rem;
  }
  
  .recommended-products h2, .bestseller h2 {
    font-size: 1.3rem;
    margin-bottom: 1.2rem;
  }
  
  /* Adjustments for View More card */
  .view-more-card {
    min-height: 210px;
  }
  
  .custom-arrow {
    width: 60px;
    height: 60px;
  }
  
  .arrow-circle {
    width: 50px;
    height: 50px;
  }
  
  .arrow-icon {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  /* More specific styling for product stats on mobile */
  .product-stats {
    padding: 0.3rem 0;
    font-size: 0.65rem;
    gap: 0;
    justify-content: space-between;
    border-top: 1px solid rgba(138, 43, 226, 0.2);
    margin-top: 0.3rem;
    display: flex;
    flex-wrap: nowrap;
  }
  
  .product-stats span {
    display: flex;
    align-items: center;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.6rem;
    padding: 0;
    margin: 0;
    width: 24%;
    justify-content: center;
    text-align: center;
    white-space: nowrap;
  }
  
  .product-stats i {
    font-size: 0.7rem;
    margin-right: 0.1rem;
    color: rgba(138, 43, 226, 0.7);
  }
  
  /* Ensure border-radius consistency */
  .bestseller-card {
    border-radius: 12px;
    padding: 0.5rem;
    overflow: hidden;
  }
  
  .bestseller-card::before {
    border-radius: 12px;
  }
  
  .bestseller-card .product-image {
    border-radius: 8px;
    height: 130px;
  }
  
  .bestseller-card .product-image img {
    border-radius: 8px;
  }
}

/* 欢迎弹窗移动端样式调整 */
@media (max-width: 768px) {
  .welcome-dialog :deep(.el-dialog) {
    width: 90% !important;
    max-width: 320px;
    margin: 0 auto;
  }
  
  .welcome-dialog-content {
    padding: 1rem;
  }
  
  .welcome-logo {
    max-width: 180px;
    margin-bottom: 1rem;
  }
  
  .welcome-message {
    font-size: 0.9rem;
    margin-bottom: 1rem;
  }
  
  .welcome-message h3 {
    font-size: 1.3rem;
    margin-bottom: 0.8rem;
  }
  
  .welcome-points li {
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    line-height: 1.4;
  }
  
  .welcome-promo {
    font-size: 1.1rem;
    margin-top: 0.8rem;
  }
  
  /* 保持其他样式不变 */
  .carousel-btn {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }
  
  .carousel-btn.prev {
    left: 10px;
  }
  
  .carousel-btn.next {
    right: 10px;
  }
  
  .carousel {
    height: 400px;
  }
  
  .carousel-slide {
    height: 400px;
  }
}

.recommended-products {
  padding: 4rem 2rem;
  background: linear-gradient(135deg, #242428, #1a1a20);
}

.recommended-products h2 {
  text-align: center;
  color: #ffffff;
  font-size: 3.2rem;
  margin-bottom: 0.5rem;
  font-weight: bold;
  text-shadow: none;
}

.discount-code {
  text-align: center;
  color: #e0e0e0;
  font-size: 1.1rem;
    margin-bottom: 2rem;
  text-shadow: none;
}

  .products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, 302px);
  gap: 1.5rem;
  max-width: 1700px;
  margin: 0 auto;
  justify-content: center;
}

/* New Trending Product Card Styles */
.product-card {
  background: white;
  border-radius: 25px;
  overflow: hidden;
  cursor: pointer;
  border: 5px solid #5e2b94;
  position: relative;
  transition: none;
  box-shadow: none !important;
  padding: 0;
  filter: none !important;
  width: 302px;
  height: 465px;
}

.product-card:hover {
  transform: none;
  box-shadow: none !important;
  filter: none !important;
  border: 5px solid #5e2b94;
}

.product-card * {
  box-shadow: none !important;
  text-shadow: none !important;
  filter: none !important;
}

.product-main-image {
  width: 100%;
  aspect-ratio: 1 / 1;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: none !important;
}

.favorite-icon {
  font-size: 1.5rem;
  color: #ccc;
  filter: none !important;
}

.stat-container {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #666;
  font-size: 0.9rem;
  filter: none !important;
  box-shadow: none !important;
}

.trending-product-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
}

/* Rocket Icon */
.rocket-icon {
  position: absolute;
  top: 0;
  left: 0;
  width: 40px;
  height: 40px;
  background-color: #ffd700;
  border-radius: 0 0 50% 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
  box-shadow: none;
}

.rocket-icon i {
  color: #333;
  font-size: 1.2rem;
  transform: rotate(45deg);
}

/* Main Product Image */
.product-main-image {
  width: 100%;
  aspect-ratio: 1 / 1;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-main-image img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

/* Product Variants */
.product-variants {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  flex-direction: column;
  gap: 5px;
  z-index: 2;
}

.variant-image {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid white;
  box-shadow: none;
  background: #f5f5f5;
}

.variant-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Price and Views */
.product-price-views {
  padding: 0.5rem 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.price-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.current-price {
  font-size: 2.5rem;
  font-weight: bold;
  color: #5e2b94;
  margin: 0;
  text-shadow: none;
}

.views-badge {
  background: rgba(94,43,148,0.2);
  color: #5e2b94;
  font-size: 0.8rem;
  padding: 0.2rem 0.5rem;
  border-radius: 15px;
  display: inline-block;
  box-shadow: none;
  text-shadow: none;
}

.favorite-icon {
  font-size: 1.5rem;
  color: #ccc;
}

.favorite-icon i.collected {
  color: #e91e63;
}

/* Product Info */
.product-info {
  padding: 0 1rem 0.5rem;
}

.product-name {
  font-size: 1rem;
  color: #333;
  margin: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  text-shadow: none;
}

/* Social Stats */
.social-stats {
  display: flex;
  padding: 0.5rem 1rem;
  border-top: 1px solid #eee;
  justify-content: flex-start;
  gap: 1.5rem;
}

.stat-container {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #666;
  font-size: 0.9rem;
}

.stat-container i {
  font-size: 1.1rem;
}

/* View More Card - keep existing styles but adapt for new grid */
.view-more-card {
  min-height: auto;
  aspect-ratio: 1 / 1.4;
}

/* Responsive styles for trending products */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 992px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
  
  .recommended-products {
    padding: 2rem 1rem;
  }
  
  .recommended-products h2 {
    font-size: 2.2rem;
  }
}

@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
  
  .current-price {
    font-size: 1.8rem;
  }
  
  .variant-image {
    width: 40px;
    height: 40px;
  }
}

/* 修改guides-container样式，减少整体高度和宽度 */
.guides-container {
  display: flex;
  flex-direction: column;
  gap: 20px;  /* 减小卡片间距 */
  margin-bottom: 30px;
  max-width: 1200px; /* 限制最大宽度 */
  margin-left: auto;
  margin-right: auto;
}

/* 修改guide-block样式，缩小卡片尺寸 */
.guide-block {
  background-color: transparent; /* Changed from #18181d to transparent */
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  position: relative;
  z-index: 5;
}

/* 减小卡片高度 */
.guide-inner {
  display: flex;
  flex-direction: row;
  min-height: 250px;  /* 减小最小高度 */
}

/* 调整图片与内容的比例 */
.guide-logo {
  width: 30%;  /* 减小图片区域比例 */
}

.guide-details {
  width: 70%;  /* 增加文字区域比例 */
  padding: 20px;  /* 减小内边距 */
}

/* 减小文字尺寸 */
.guide-title {
  font-size: 1.2rem;
  margin-bottom: 10px;
}

.guide-text {
  font-size: 0.85rem;
  margin-bottom: 15px;
}

/* 调整按钮样式 */
.info-btn {
  display: inline-block;
  margin-top: 1.5rem;
  padding: 8px 16px;
    font-size: 0.9rem;
  min-width: 180px;
  width: fit-content;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  box-sizing: border-box;
  background: linear-gradient(90deg, #9333ea, #4f46e5);
  color: white;
  text-decoration: none;
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(79, 70, 229, 0.4);
  max-width: 235px;
}

.intro-title-glow {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1.5rem;
  color: #d4baff; /* 紫色调文字 */
  text-shadow: 0 0 10px rgba(167, 107, 255, 0.8), 0 0 20px rgba(138, 43, 226, 0.6), 0 0 30px rgba(147, 112, 219, 0.4);
  letter-spacing: 1px;
  text-align: center;
}

.intro-desc-glow {
  margin: 0 auto;
  max-width: 900px;
  display: flex;
    flex-direction: column;
  align-items: center;
  gap: 0.7rem;
}

.intro-tagline {
  font-size: 1.2rem;
  margin-bottom: 1.5rem;
  line-height: 1.5;
  color: #e0d0ff; /* 淡紫色文字 */
  text-shadow: 0 0 10px rgba(167, 107, 255, 0.7), 0 0 15px rgba(138, 43, 226, 0.5);
    text-align: center;
  }

.benefits-list {
  text-align: left;
  list-style-type: none;
  padding: 0;
  margin: 0 auto;
  max-width: 700px;
}

.benefits-list li {
    margin-bottom: 1rem;
  font-size: 1.1rem;
  display: flex;
  align-items: baseline;
  color: #e8e0ff; /* 更淡的紫色文字 */
  text-shadow: 0 0 8px rgba(167, 107, 255, 0.6), 0 0 12px rgba(138, 43, 226, 0.4);
}

.benefit-number {
  font-weight: bold;
  margin-right: 0.5rem;
  color: #b388ff; /* 亮紫色高亮数字 */
  text-shadow: 0 0 8px rgba(167, 107, 255, 0.9), 0 0 15px rgba(138, 43, 226, 0.7);
}

.logo-container {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.site-logo {
  max-width: 260px;
  height: auto;
  margin-bottom: 1rem;
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
  transition: transform 0.3s ease;
}

.site-logo:hover {
  transform: scale(1.05);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .site-logo {
    max-width: 150px;
    margin-bottom: 0.8rem;
    margin-top: 3rem; /* 添加顶部边距，使logo向下移动 */
  }
  
  .intro-tagline {
    font-size: 1.1rem;
    margin-bottom: 1.2rem;
  }
  
  .benefits-list li {
    font-size: 1rem;
    margin-bottom: 0.8rem;
  }
}

@media (max-width: 480px) {
  .site-logo {
    max-width: 120px;
    margin-bottom: 0.5rem;
    margin-top: 4rem; /* 在更小的屏幕上增加更多的顶部边距 */
  }
  
  .intro-tagline {
    font-size: 1rem;
    margin-bottom: 1rem;
  }
  
  .benefits-list li {
    font-size: 0.9rem;
    margin-bottom: 0.7rem;
  }
}

/* 在移动端隐藏在线用户飞船计数器 */
.hide-on-mobile {
  display: none !important;
}

@media (min-width: 769px) {
  .hide-on-mobile {
    display: block !important;
  }
}

.anchor-point {
  display: block;
  position: relative;
  top: -100px;
  visibility: hidden;
}

/* Factory Photos Section */
.factory-photos {
  position: relative;
  padding: 2rem;
  margin: 2rem 0;
  text-align: center;
}

.factory-photos h2 {
  font-size: 2rem;
  margin-bottom: 2rem;
  text-transform: uppercase;
  color: var(--primary-color);
  font-weight: 600;
}

@media (max-width: 768px) {
  .factory-photos {
    padding: 1rem;
    margin: 1rem 0;
  }
  
  .factory-photos h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }
}

/* ======================== 产品网格样式 ======================== */
.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 2rem 1.5rem; /* 上下间距2rem，左右间距1.5rem */
  padding: 1rem; /* 给网格容器添加内边距 */
  width: 100%;
  max-width: 1400px;
  margin: 2rem auto;
  position: relative;
  z-index: 0; /* 将产品网格z-index降到最低 */
}

/* ======================== 产品卡片样式 ======================== */
.product-card {
  background: linear-gradient(135deg, #2a1f3b, #1e1a2e);
  border-radius: 15px;
  overflow: hidden;
  border: 2px solid rgba(195, 163, 255, 0.3);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  cursor: pointer;
  position: relative;
  z-index: 1;
  animation: float 6s ease-in-out infinite;
}

.product-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.8),
    0 0 30px 5px rgba(255, 255, 255, 0.8),
    0 0 50px 10px rgba(255, 255, 255, 0.6),
    0 0 70px 15px rgba(255, 255, 255, 0.4),
    0 0 90px 20px rgba(128, 102, 204, 0.3);
  z-index: 100;
}

/* 产品卡片发光效果 */
.product-card::after {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 20px;
  background: transparent;
  border: 3px solid rgba(255, 255, 255, 0.8);
  box-shadow: 
    0 0 20px 5px rgba(255, 255, 255, 0.8),
    0 0 40px 10px rgba(128, 102, 204, 0.7),
    0 0 60px 15px rgba(128, 102, 204, 0.5),
    0 0 80px 20px rgba(128, 102, 204, 0.3);
  opacity: 0;
  z-index: -1;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.product-card:hover::after {
  opacity: 1;
  animation: strongGlow 1.5s infinite alternate;
}

@keyframes strongGlow {
  0% {
    box-shadow: 
      0 0 20px 5px rgba(255, 255, 255, 0.8),
      0 0 40px 10px rgba(128, 102, 204, 0.7),
      0 0 60px 15px rgba(128, 102, 204, 0.5),
      0 0 80px 20px rgba(128, 102, 204, 0.3);
    border-color: rgba(255, 255, 255, 0.8);
  }
  100% {
    box-shadow: 
      0 0 30px 10px rgba(255, 255, 255, 0.9),
      0 0 60px 15px rgba(128, 102, 204, 0.8),
      0 0 90px 20px rgba(128, 102, 204, 0.6),
      0 0 120px 25px rgba(128, 102, 204, 0.4);
    border-color: rgba(255, 255, 255, 1);
  }
}

/* View More卡片样式 */
.view-more-card {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border: 2px solid rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 350px;
}

.view-more-simple {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: white;
}

.custom-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  position: relative;
}

.arrow-circle {
  width: 60px;
  height: 60px;
  border: 3px solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.1);
  transition: all 0.3s ease;
}

.arrow-icon {
  font-size: 1.5rem;
  color: white;
  transition: transform 0.3s ease;
}

.view-more-card:hover .arrow-circle {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.view-more-card:hover .arrow-icon {
  transform: translateX(5px);
}

/* 产品图片样式 */
.product-image {
  position: relative;
  height: 250px;
  overflow: hidden;
  background: linear-gradient(135deg, #2a1f3b, #1e1a2e);
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-card:hover .product-image img {
  transform: scale(1.1);
}

/* 产品详情样式（HomeView使用的类名） */
.product-details {
  padding: 1.2rem;
  background: linear-gradient(135deg, #2a1f3b, #1e1a2e);
}

.product-details h3 {
  color: white;
  font-size: 1.2rem; /* 从1.1rem改为1.2rem，与价格保持一致 */
  font-weight: 600;
  margin: 0 0 0.8rem 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 价格样式 */
.price {
  color: #bb86fc;
  font-size: 1.2rem; /* 从1.3rem改为1.2rem，与商品名称保持一致 */
  font-weight: bold;
  margin: 0 0 1rem 0;
  text-shadow: 0 0 10px rgba(187, 134, 252, 0.8);
}

/* 产品统计样式 */
.product-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.5rem;
}

.product-stats span {
  display: flex;
  align-items: center;
  gap: 0.3rem;
  color: #c3a3ff;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0.2rem 0.4rem;
  border-radius: 15px;
}

.product-stats span:hover {
  background: rgba(195, 163, 255, 0.2);
  transform: scale(1.1);
}

.product-stats span i {
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

/* 悬浮动画 */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-5px); }
}

.product-card:nth-child(2n) {
  animation-delay: -2s;
}

.product-card:nth-child(3n) {
  animation-delay: -4s;
}

/* ======================== 响应式设计 ======================== */

/* 大屏幕 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 1.2rem;
  }
  
  .product-image {
    height: 220px;
  }
}

/* 平板 */
@media (max-width: 992px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }
  
  .product-image {
    height: 200px;
  }
  
  .product-details {
    padding: 1rem;
  }
}

/* 手机横屏/小平板 */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    padding: 1rem 0.5rem;
  }
  
  .product-card {
    border-radius: 12px;
  }
  
  .product-image {
    height: 180px;
  }
  
  .product-details {
    padding: 0.8rem;
  }
  
  .product-details h3 {
    font-size: 1rem;
  }
  
  .price {
    font-size: 1.1rem;
  }
  
  .product-stats span {
    font-size: 0.8rem;
    gap: 0.2rem;
  }
  
  .product-stats span i {
    font-size: 0.7rem;
  }
}

/* 手机竖屏 */
@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
    padding: 0 10px;
  }
  
  .product-card {
    border-radius: 8px;
    background: #1c1c22;
  }
  
  .product-image {
    height: 0;
    padding-bottom: 100%;
    position: relative;
  }
  
  .product-image img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
  
  .product-details {
    padding: 0.5rem;
    min-height: 90px;
    display: flex;
    flex-direction: column;
    background: #1c1c22;
  }
  
  .product-details h3 {
    font-size: 0.9rem;
    margin: 0.3rem 0;
    line-height: 1.3;
    max-height: 2.6rem;
    overflow: hidden;
  }
  
  .price {
    font-size: 1rem;
    margin-bottom: 0.5rem;
  }
  
  .product-stats {
    gap: 0.3rem;
    margin-top: auto;
  }
  
  .product-stats span {
    font-size: 0.7rem;
    padding: 0.1rem 0.2rem;
    flex: 1;
    justify-content: center;
    text-align: center;
  }
  
  .product-stats span i {
    font-size: 0.6rem;
  }
  
  .view-more-card {
    min-height: auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .custom-arrow {
    width: 60px;
    height: 60px;
  }
  
  .arrow-circle {
    width: 40px;
    height: 40px;
  }
  
  .arrow-icon {
    font-size: 1.2rem;
  }
  
  /* 移动端发光效果调整 */
  .product-card:hover {
    transform: translateY(-5px) scale(1.03);
    box-shadow: 
      0 10px 20px rgba(0, 0, 0, 0.8),
      0 0 20px 5px rgba(255, 255, 255, 0.8),
      0 0 40px 10px rgba(255, 255, 255, 0.6),
      0 0 60px 15px rgba(128, 102, 204, 0.4);
  }
}

/* 超小屏幕 */
@media (max-width: 374px) {
  .product-card {
    margin: 0 2px;
  }
  
  .product-details {
    padding: 0.4rem;
    min-height: 80px;
  }
  
  .product-details h3 {
    font-size: 0.85rem;
    margin: 0.2rem 0;
  }
  
  .price {
    font-size: 0.9rem;
  }
  
  .product-stats span {
    font-size: 0.65rem;
    gap: 0.1rem;
  }
  
  .product-stats span i {
    font-size: 0.55rem;
  }
}

/* BESTSELLER区域样式 - 完全复制ProductsView.vue的商品卡片样式 */

/* Products Grid */
.products-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 2rem 1.5rem; /* 上下间距2rem，左右间距1.5rem */
  padding: 1rem; /* 给网格容器添加内边距 */
  width: 100%;
  max-width: 1400px;
  margin: 2rem auto;
  position: relative;
  z-index: 0; /* 将产品网格z-index降到最低 */
}

.product-card {
  background: #1a1a20;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  display: flex;
  flex-direction: column;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  width: 302px;
  max-width: 100%;
  height: 465px;
  cursor: pointer;
  border: 3px solid #8a2be2 !important;
  transform-origin: center;
  z-index: 0; /* 将产品卡片z-index降到最低 */
}

/* 添加紫色光晕效果 */
.product-card::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(135deg, #8a2be2, #4b0082);
  border-radius: 18px;
  z-index: -5; /* 进一步降低z-index值 */
  opacity: 0.8;
  filter: blur(8px);
  transition: all 0.4s ease;
}

.product-card:hover::before {
  opacity: 1;
  filter: blur(12px);
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
}

.product-card:hover {
  transform: translateY(-10px) scale(1.05);
  box-shadow: 0 20px 30px rgba(0, 0, 0, 0.6), 0 0 30px rgba(255, 255, 255, 0.5);
  border-color: white !important;
  background: linear-gradient(135deg, #2d2d35, #22222a);
  z-index: 0; /* 将产品卡片悬停时的z-index也设为最低 */
  animation: float 3s ease-in-out infinite;
}

/* 悬停时光晕变成白色 */
.product-card:hover::before {
  opacity: 1;
  filter: blur(12px);
  background: linear-gradient(135deg, #ffffff, #e0e0e0);
}

/* 添加卡片悬浮动画 */
@keyframes float {
  0% {
    transform: translateY(-10px) scale(1.05);
  }
  50% {
    transform: translateY(-12px) scale(1.05);
  }
  100% {
    transform: translateY(-10px) scale(1.05);
  }
}

/* 添加点击效果 */
.product-card:active {
  transform: translateY(-5px) scale(1.02);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.5), 0 0 15px rgba(120, 70, 200, 0.4);
  transition: all 0.1s ease;
  animation: none;
}

/* 增强价格显示效果 */
@keyframes priceGlow {
  0% {
    text-shadow: 0 0 12px rgba(195, 163, 255, 0.6);
  }
  50% {
    text-shadow: 0 0 18px rgba(195, 163, 255, 0.8), 0 0 30px rgba(195, 163, 255, 0.4);
  }
  100% {
    text-shadow: 0 0 12px rgba(195, 163, 255, 0.6);
  }
}

.product-card:hover .current-price {
  color: #d4b5ff;
  text-shadow: 0 0 12px rgba(195, 163, 255, 0.6);
  transform: scale(1.05);
  animation: priceGlow 2s infinite;
}

/* 为统计图标添加动画效果 */
@keyframes iconPulse {
  0% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1.1);
  }
}

.stat-item:hover i {
  animation: iconPulse 1s infinite;
  color: #d4b5ff;
}

/* 收藏按钮特殊效果 */
@keyframes heartBeat {
  0% {
    transform: scale(1.1);
  }
  14% {
    transform: scale(1.3);
  }
  28% {
    transform: scale(1.1);
  }
  42% {
    transform: scale(1.3);
  }
  70% {
    transform: scale(1.1);
  }
}

.stat-item.collected:hover i {
  animation: heartBeat 1.5s infinite;
  color: #ff8dd8 !important;
}

/* Mystical glow effect */
.product-card::after {
  content: '';
  position: absolute;
  top: -1px;
  left: -1px;
  right: -1px;
  bottom: -1px;
  background: linear-gradient(135deg, rgba(120, 70, 200, 0), rgba(120, 70, 200, 0));
  border-radius: 15px;
  z-index: -5; /* 进一步降低z-index值 */
  transition: all 0.4s ease;
  opacity: 0;
}

.product-card:hover::after {
  background: linear-gradient(135deg, rgba(120, 70, 200, 0.7), rgba(187, 134, 252, 0.7), rgba(120, 70, 200, 0.7));
  box-shadow: 0 0 15px rgba(120, 70, 200, 0.5);
  opacity: 1;
  animation: borderGlow 2s infinite alternate;
}

@keyframes borderGlow {
  0% {
    box-shadow: 0 0 15px rgba(120, 70, 200, 0.5);
  }
  100% {
    box-shadow: 0 0 25px rgba(187, 134, 252, 0.8);
  }
}

.product-image {
  height: 250px; /* 从220px增加到250px，让图片区域更大 */
  width: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent; /* 改为透明，消除黑色背景 */
  position: relative;
  padding: 0; /* 移除所有内边距，让图片占满整个容器 */
  border-radius: 0; /* 移除容器圆角 */
  margin: 0; /* 移除负边距，使用宽度扩展 */
  transform: scaleX(1.1); /* 水平方向放大1.2倍，真正扩宽容器 */
  margin-top: -10px; /* 向上延伸，与上边框重合 */
}

/* 右上角半球形紫色光晕效果 */
.product-image::after {
  content: '';
  position: absolute;
  top: -100px;
  right: -70px;
  width: 230px;
  height: 230px;
  background: radial-gradient(circle, rgba(138, 43, 226, 0.8) 0%, rgba(138, 43, 226, 0.4) 50%, rgba(138, 43, 226, 0) 80%);
  border-radius: 50%;
  filter: blur(18px);
  opacity: 0.7;
  z-index: 1;
  pointer-events: none;
  transition: all 0.5s ease;
  mix-blend-mode: screen; /* 确保光晕与背景混合，不会完全覆盖图片 */
}

/* 鼠标悬停时光晕变成白色 */
.product-card:hover .product-image::after {
  background: radial-gradient(circle, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0) 80%);
  opacity: 0.8;
  filter: blur(20px);
}

.product-image img {
  width: 100%; /* 宽度占满整个容器 */
  height: 100%; /* 高度占满整个容器 */
  object-fit: fill; /* 使用cover让图片填满整个容器 */
  transition: transform 0.5s ease, filter 0.5s ease;
  filter: drop-shadow(0 0 8px rgba(120, 70, 200, 0.3));
  border-radius: 0; /* 移除圆角，变成直角 */
}

.product-card:hover .product-image img {
  transform: scale(1.05); /* 悬停时稍微放大，避免过度放大 */
  filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.7));
}

.product-info {
  padding: 8px 12px; /* 从12px减少到8px上下，12px左右，压缩文字区域 */
  background: rgba(35, 35, 40, 0.9);
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: all 0.3s ease;
}

.product-name {
  color: #e0e0e0;
  font-size: 1.2rem; /* 从1rem减小到0.9rem */
  margin-bottom: -8px; /* 从10px减少到6px，压缩间距 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 100%;
  line-height: 1.1; /* 从1.2减少行高，更紧凑 */
  transition: all 0.3s ease;
  text-align: left; /* 确保商品名称左对齐 */
}

.product-card:hover .product-name {
  color: #ffffff;
}

.price-container {
  display: flex;
  align-items: baseline;
  gap: 8px;
  margin-bottom: 6px; /* 从10px减少到6px，压缩间距 */
  transition: all 0.3s ease;
  text-align: left; /* 确保价格左对齐 */
}

.product-card:hover .price-container {
  transform: translateY(-2px);
}

.current-price {
  color: #c3a3ff;
  font-size: 2.2rem; /* 从1.2rem减小到1.1rem */
  font-weight: bold;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
  transition: all 0.3s ease;
}

.product-card:hover .current-price {
  color: #ffffff;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.original-price {
  color: #888;
  font-size: 0.8rem; /* 从0.9rem减小到0.8rem */
  text-decoration: line-through;
  transition: all 0.3s ease;
}

.product-card:hover .original-price {
  color: #aaa;
}

.product-stats {
  display: flex;
  justify-content: space-between;
  color: #a9a9a9;
  font-size: 0.75rem; /* 从0.85rem减小到0.75rem */
  margin-top: -20px;
  padding-top: 6px; /* 从10px减少到6px，压缩顶部间距 */
  border-top: none; /* 移除上边框 */
  transition: all 0.3s ease;
  width: 0px;
}

.product-card:hover .product-stats {
  background: rgba(40, 40, 50, 0.9);
  border-top: none; /* 悬停时也不显示上边框 */
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  cursor: pointer;
  padding: 5px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.stat-item:hover {
  background-color: rgba(120, 70, 200, 0.3);
  color: #d4b5ff;
  transform: translateY(-2px);
}

.product-card:hover .stat-item {
  color: #c0c0c0;
}

.stat-item.liked {
  color: #c3a3ff;
}

.product-card:hover .stat-item.liked {
  color: #d4b5ff;
}

.stat-item.collected {
  color: #ff71ce !important;
}

.stat-item.collected i {
  color: #ff71ce !important;
}

.product-card:hover .stat-item.collected {
  color: #ff8dd8 !important;
}

.product-card:hover .stat-item.collected i {
  color: #ff8dd8 !important;
}

.stat-item i {
  font-size: 1rem;
  transition: all 0.3s ease;
}

.product-card:hover .stat-item i {
  transform: scale(1.1);
}

/* View More Card 样式 */
.view-more-card {
  background: linear-gradient(135deg, #242428, #1a1a20) !important;
  border: 2px dashed rgba(195, 163, 255, 0.5) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 350px !important;
  transition: all 0.3s ease !important;
}

.view-more-card:hover {
  border-color: rgba(195, 163, 255, 0.8) !important;
  background: linear-gradient(135deg, #2d2d35, #22222a) !important;
  transform: translateY(-5px) !important;
}

.view-more-simple {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #c3a3ff;
  font-size: 1.2rem;
  font-weight: bold;
}

.custom-arrow {
  font-size: 2rem;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

.arrow-circle {
  width: 60px;
  height: 60px;
  border: 2px solid #c3a3ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.custom-arrow svg {
  width: 24px;
  height: 24px;
  fill: #c3a3ff;
}

.custom-arrow svg path {
  transition: all 0.3s ease;
}

.view-more-card:hover .custom-arrow {
  transform: translateX(5px);
}

.view-more-card:hover .arrow-circle {
  border-color: #ffffff;
  background: rgba(195, 163, 255, 0.1);
}

.view-more-card:hover .custom-arrow svg path {
  fill: #ffffff;
}

/* 响应式设计 */

/* 大屏幕 */
@media (max-width: 1200px) {
  .products-grid {
    grid-template-columns: repeat(3, 1fr) !important;
    gap: 1.5rem !important;
  }

  .product-image {
    height: 200px !important;
  }
}

/* 平板设备 */
@media (max-width: 768px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 1rem !important;
  }

  .product-card {
    border-radius: 12px !important;
  }

  .product-image {
    height: 160px !important;
    padding: 3px 2px !important;
  }

  .product-info {
    padding: 5px 6px !important;
    background: rgba(35, 35, 40, 0.9);
    flex: 1;
    display: flex;
    flex-direction: column;
    transition: all 0.3s ease;
  }

  .product-name {
    color: #e0e0e0;
    font-size: 0.8rem !important;
    margin-bottom: 3px !important;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
    max-width: 100%;
    line-height: 1.1 !important;
    transition: all 0.3s ease;
  }

  .price-container {
    margin-bottom: 3px !important;
  }

  .current-price {
    font-size: 0.85rem !important;
  }

  .original-price {
    font-size: 0.65rem !important;
  }

  .product-stats {
    font-size: 0.65rem !important;
    padding-top: 3px !important;
  }

  .stat-item i {
    font-size: 0.75rem !important;
  }

  .stat-item {
    padding: 8px;
  }
  
  .product-card {
    border-radius: 10px !important;
  }
}

/* 超小屏幕 */
@media (max-width: 480px) {
  .products-grid {
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0 !important;
    padding: 0 !important;
    width: 100% !important;
    margin-left: auto !important;
    margin-right: auto !important;
    box-sizing: border-box !important;
    left: 0 !important;
  }

  .product-card {
    border-radius: 8px !important;
    overflow: hidden !important;
    background: #1c1c22 !important;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3) !important;
    border: none !important;
    width: 100% !important;
    box-sizing: border-box !important;
    margin: 0 !important;
  }

  .product-image {
    height: 0 !important;
    padding-bottom: 100% !important;
    border-radius: 8px 8px 0 0 !important;
    position: relative !important;
    width: 100% !important;
    overflow: hidden !important;
  }

  .product-image img {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
  }

  .product-info {
    padding: 0.5rem !important;
    background: #1c1c22 !important;
    min-height: 90px !important;
    display: flex !important;
    flex-direction: column !important;
  }

  .product-name {
    font-size: 0.9rem !important;
    margin: 0.3rem 0 !important;
    color: #fff !important;
    white-space: nowrap !important;
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    width: 100% !important;
    max-width: 100% !important;
    display: block !important;
    padding: 0 !important;
    line-height: 1.2 !important;
  }

  .current-price {
    font-size: 1.2rem !important;
    font-weight: bold !important;
    color: #a855f7 !important;
    margin-bottom: 0.3rem !important;
  }

  .original-price {
    text-decoration: line-through !important;
    color: #666 !important;
    font-size: 0.8rem !important;
    margin-left: 0.5rem !important;
  }

  .product-stats {
    display: flex !important;
    justify-content: flex-start !important;
    gap: 0.6rem !important;
    color: #999 !important;
    font-size: 0.8rem !important;
    padding: 0.3rem 0 0 0 !important;
    border-top: 1px solid rgba(255, 255, 255, 0.1) !important;
  }

  .product-stats span {
    display: flex !important;
    align-items: center !important;
    gap: 0.2rem !important;
    font-size: 0.75rem !important;
  }

  .product-stats i {
    font-size: 0.9rem !important;
    color: #ccc !important;
  }
}

/* 触摸优化 */
@media (hover: none) {
  .products-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    width: 100% !important;
  }
  
  .product-card {
    transform: none !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
    background: linear-gradient(135deg, #242428, #1a1a20) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(80, 80, 100, 0.3) !important;
    width: 100% !important;
  }

  .product-stats .stat-item {
    min-width: 35px !important;
  }
}

/* 移动端底部空间 */
@media (max-width: 768px) {
  .products-grid {
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 0.8rem !important;
    width: 100% !important;
  }
}

/* ======================== 商品卡片强力发光效果 (来自ProductsView.vue) ======================== */
.product-card {
  position: relative !important;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
  z-index: 1 !important;
}

/* 悬停时提升卡片层级，确保光晕不被其他元素遮挡 */
.product-card:hover {
  z-index: 100 !important;
  transform: translateY(-10px) scale(1.05) !important;
}

/* 强力光晕外框 */
.product-card::after {
  content: '' !important;
  position: absolute !important;
  top: -5px !important;
  left: -5px !important;
  right: -5px !important;
  bottom: -5px !important;
  border-radius: 20px !important;
  background: transparent !important;
  border: 3px solid rgba(255, 255, 255, 0.8) !important;
  box-shadow: 
    0 0 20px 5px rgba(255, 255, 255, 0.8),
    0 0 40px 10px rgba(128, 102, 204, 0.7),
    0 0 60px 15px rgba(128, 102, 204, 0.5),
    0 0 80px 20px rgba(128, 102, 204, 0.3) !important;
  opacity: 0 !important;
  z-index: -1 !important;
  transition: opacity 0.3s ease !important;
  pointer-events: none !important;
}

/* 悬停时显示强力光晕 */
.product-card:hover::after {
  opacity: 1 !important;
  animation: strongGlow 1.5s infinite alternate !important;
}

/* 卡片底部强力阴影 */
.product-card:hover {
  box-shadow: 
    0 20px 40px rgba(0, 0, 0, 0.8),
    0 0 30px 5px rgba(255, 255, 255, 0.8),
    0 0 50px 10px rgba(255, 255, 255, 0.6),
    0 0 70px 15px rgba(255, 255, 255, 0.4),
    0 0 90px 20px rgba(128, 102, 204, 0.3) !important;
}

/* 强力光晕动画 */
@keyframes strongGlow {
  0% {
    box-shadow: 
      0 0 20px 5px rgba(255, 255, 255, 0.8),
      0 0 40px 10px rgba(128, 102, 204, 0.7),
      0 0 60px 15px rgba(128, 102, 204, 0.5),
      0 0 80px 20px rgba(128, 102, 204, 0.3) !important;
    border-color: rgba(255, 255, 255, 0.8) !important;
  }
  100% {
    box-shadow: 
      0 0 30px 10px rgba(255, 255, 255, 0.9),
      0 0 60px 15px rgba(128, 102, 204, 0.8),
      0 0 90px 20px rgba(128, 102, 204, 0.6),
      0 0 120px 25px rgba(128, 102, 204, 0.4) !important;
    border-color: rgba(255, 255, 255, 1) !important;
  }
}

/* 确保移动端也有效果 */
@media (max-width: 767px) {
  .product-card:hover::after {
    opacity: 1 !important;
    animation: strongGlow 1.5s infinite alternate !important;
  }
  
  .product-card:hover {
    transform: translateY(-5px) scale(1.03) !important;
    box-shadow: 
      0 10px 20px rgba(0, 0, 0, 0.8),
      0 0 20px 5px rgba(255, 255, 255, 0.8),
      0 0 40px 10px rgba(255, 255, 255, 0.6),
      0 0 60px 15px rgba(128, 102, 204, 0.4) !important;
  }
}

/* 首页移动端轮播图特定样式 */
@media (max-width: 768px) {
  .mobile-carousel {
    width: 400px !important;
    max-width: 400px !important;
    margin: 0 auto !important;
  }
  
  .mobile-carousel .enhanced-carousel-section {
    width: 400px !important;
    max-width: 400px !important;
    margin: 0 auto !important;
  }
  
  .mobile-carousel .enhanced-carousel {
    width: 400px !important;
    height: 150px !important;
  }
  
  .mobile-carousel .carousel-container {
    width: 400px !important;
    height: 150px !important;
  }
  
  .mobile-carousel .carousel-slide {
    width: 400px !important;
    height: 150px !important;
  }
  
  .mobile-carousel .carousel-image {
    width: 400px !important;
    height: 150px !important;
    object-fit: cover !important;
  }
  
  .mobile-carousel .carousel-indicators {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .mobile-carousel {
    width: 400px !important;
    max-width: 400px !important;
    margin: 0 auto !important;
  }
  
  .mobile-carousel .enhanced-carousel-section {
    width: 400px !important;
    max-width: 400px !important;
    margin: 0 auto !important;
  }
  
  .mobile-carousel .enhanced-carousel {
    width: 400px !important;
    height: 150px !important;
  }
  
  .mobile-carousel .carousel-container {
    width: 400px !important;
    height: 150px !important;
  }
  
  .mobile-carousel .carousel-slide {
    width: 400px !important;
    height: 150px !important;
  }
  
  .mobile-carousel .carousel-image {
    width: 400px !important;
    height: 150px !important;
    object-fit: cover !important;
  }
  
  /* 小屏幕也隐藏左右切换按钮 */
  .mobile-carousel .carousel-control {
    display: none !important;
  }
  
  .mobile-carousel .carousel-control.prev {
    display: none !important;
  }
  
  .mobile-carousel .carousel-control.next {
    display: none !important;
  }
}

/* 星星样式 */
.starry-background {
  display: none;
}

.star {
  display: none;
}

.nebula {
  display: none;
}

/* 星星动画 */
@keyframes starTwinkle {
  0%, 100% {
    opacity: 0;
  }
}

@keyframes starMovement {
  0%, 100% {
    transform: none;
  }
}

@keyframes nebula-move {
  0%, 100% {
    transform: none;
  }
}

/* BESTSELLER区域全屏宽度覆盖样式 */
@media screen and (max-width: 768px) {
  section.recommended-products {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    overflow-x: hidden !important;
  }
  
  section.recommended-products div.products-grid {
    width: 100% !important;
    max-width: 100% !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }
  
  section.recommended-products div.product-card {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    box-sizing: border-box !important;
  }
}

@media screen and (max-width: 480px) {
  section.recommended-products {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    overflow-x: hidden !important;
  }
  
  section.recommended-products div.products-grid {
    width: 100% !important;
    max-width: 100% !important;
    grid-template-columns: 1fr 1fr !important;
    gap: 0 !important;
    padding: 0 !important;
    margin: 0 !important;
    box-sizing: border-box !important;
  }
}

/* 轮播图全屏宽度样式 */
@media screen and (max-width: 768px) {
  /* 主轮播图区域 */
  section.products {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    overflow-x: hidden !important;
  }
  
  section.products .carousel-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    border-radius: 0 !important;
    height: 250px !important;
    aspect-ratio: unset !important;
    min-height: unset !important;
    max-height: unset !important;
  }
  
  section.products .carousel-slide {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    height: 100% !important;
  }
  
  section.products .carousel {
    width: 100% !important;
    height: 100% !important;
  }
  
  section.products .slide-image-container {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: #000 !important;
  }
  
  section.products .slide-background {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    object-position: center center !important;
    display: block !important;
  }
  
  /* 工厂照片轮播图区域 */
  section.factory-photos {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 1rem !important;
    margin-top: -2rem !important;
    overflow-x: hidden !important;
  }
  
  section.factory-photos .carousel-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    border-radius: 0 !important;
    height: 250px !important;
    aspect-ratio: unset !important;
    min-height: unset !important;
    max-height: unset !important;
  }
  
  section.factory-photos .carousel-slide {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    height: 100% !important;
  }
  
  section.factory-photos .carousel {
    width: 100% !important;
    height: 100% !important;
  }
  
  section.factory-photos .slide-image-container {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: #000 !important;
  }
  
  section.factory-photos .slide-background {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    object-position: center center !important;
    display: block !important;
  }
  
  section.factory-photos h2 {
    margin: 0 auto 1rem auto !important;
    padding: 0 1rem !important;
    text-align: center !important;
  }
}

@media screen and (max-width: 480px) {
  /* 主轮播图区域 */
  section.products {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    overflow-x: hidden !important;
  }
  
  section.products .carousel-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    border-radius: 0 !important;
    height: 250px !important;
    aspect-ratio: unset !important;
    min-height: unset !important;
    max-height: unset !important;
  }
  
  section.products .carousel-slide {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    height: 100% !important;
  }
  
  section.products .carousel {
    width: 100% !important;
    height: 100% !important;
  }
  
  section.products .slide-image-container {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: #000 !important;
  }
  
  section.products .slide-background {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    object-position: center center !important;
    display: block !important;
  }
  
  section.factory-photos {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 1rem !important;
    margin-top: -2rem !important;
    overflow-x: hidden !important;
  }
  
  section.factory-photos .carousel-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
    border-radius: 0 !important;
    height: 250px !important;
    aspect-ratio: unset !important;
    min-height: unset !important;
    max-height: unset !important;
  }
  
  section.factory-photos .carousel-slide {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    border-radius: 0 !important;
    height: 100% !important;
  }
  
  section.factory-photos .carousel {
    width: 100% !important;
    height: 100% !important;
  }
  
  section.factory-photos .slide-image-container {
    width: 100% !important;
    height: 100% !important;
    position: relative !important;
    overflow: hidden !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background: #000 !important;
  }
  
  section.factory-photos .slide-background {
    max-width: 100% !important;
    max-height: 100% !important;
    width: auto !important;
    height: auto !important;
    object-fit: contain !important;
    object-position: center center !important;
    display: block !important;
  }
  
  section.factory-photos h2 {
    margin: 0 auto 1rem auto !important;
    padding: 0 1rem !important;
    text-align: center !important;
  }
}

@media screen and (max-width: 768px) {
  section.intro-section {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
    overflow-x: hidden !important;
  }
  
  .intro-content {
    width: 100% !important;
    max-width: 100% !important;
    padding: 1rem !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    text-align: center !important;
  }
  
  .logo-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto 1rem auto !important;
  }
  
  .site-logo {
    max-width: 180px !important;
    margin: 0 auto !important;
  }
  
  .intro-title-glow {
    font-size: 1.8rem !important;
    margin-bottom: 1rem !important;
    text-align: center !important;
  }
  
  .intro-desc-glow {
    width: 100% !important;
    max-width: 100% !important;
  }
  
  .intro-tagline {
    font-size: 1rem !important;
    margin-bottom: 1rem !important;
    text-align: center !important;
    padding: 0 0.5rem !important;
  }
  
  .benefits-list {
    width: 100% !important;
    max-width: 100% !important;
    padding-left: 0 !important;
    margin-left: 0 !important;
  }
  
  .benefits-list li {
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important;
    text-align: left !important;
    padding-left: 1.5rem !important;
    position: relative !important;
  }
}

@media screen and (max-width: 480px) {
  section.intro-section {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
    overflow-x: hidden !important;
  }
  
  .intro-content {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0.5rem !important;
    margin: 0 !important;
    box-sizing: border-box !important;
    text-align: center !important;
  }
  
  .logo-container {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 auto 0.5rem auto !important;
  }
  
  .site-logo {
    max-width: 150px !important;
    margin: 0 auto !important;
  }
  
  .intro-title-glow {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
  }
  
  .intro-tagline {
    font-size: 0.9rem !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
    padding: 0 0.5rem !important;
  }
  
  .benefits-list li {
    font-size: 0.8rem !important;
    margin-bottom: 0.4rem !important;
    text-align: left !important;
    padding-left: 1.2rem !important;
    position: relative !important;
  }
}

@media screen and (max-width: 768px) {
  section.faq {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 1rem !important;
    padding-right: 1rem !important;
    overflow-x: hidden !important;
  }
  
  section.faq .faq-items {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }
}

@media screen and (max-width: 480px) {
  section.faq {
    width: 100vw !important;
    max-width: 100vw !important;
    position: relative !important;
    left: 50% !important;
    right: 50% !important;
    margin-left: -50vw !important;
    margin-right: -50vw !important;
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
    overflow-x: hidden !important;
  }
  
  section.faq .faq-items {
    width: 100% !important;
    max-width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
    box-sizing: border-box !important;
  }
}

.mobile-carousel .carousel-control.next {
  display: none !important;
}
</style>




