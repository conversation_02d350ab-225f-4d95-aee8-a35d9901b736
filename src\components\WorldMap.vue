<template>
  <div class="world-map-container">
    <div class="map-content">
      <div class="map-wrapper">
        <div v-if="loading" class="loading-overlay">
          <div class="loading-spinner"></div>
          <div class="loading-text">加载世界地图数据...</div>
        </div>
        <div v-else-if="mapError" class="error-overlay">
          <div class="error-icon">!</div>
          <div class="error-text">无法加载世界地图</div>
        </div>
        <v-chart 
          v-else 
          class="chart" 
          :option="chartOption" 
          :autoresize="false"
          ref="chart" 
        />
        
        <!-- 添加实时更新控制按钮 -->
        <!-- <div class="update-control">
          <button 
            @click="toggleDataUpdate" 
            :class="{'active': dataUpdateActive}"
            class="update-button"
          >
            {{ dataUpdateActive ? '暂停实时更新' : '开始实时更新' }}
          </button>
        </div> -->
      </div>
      
      <div class="stats-cards">
        <div class="stat-card">
          <div class="stat-number animated">{{ stats.spreadsheets }}</div>
          <div class="stat-label">Spreadsheet</div>
        </div>
        <div class="stat-card">
          <div class="stat-number animated">{{ stats.rows }}</div>
          <div class="stat-label">Rows</div>
        </div>
        <div class="stat-card">
          <div class="stat-number animated">{{ stats.products }}</div>
          <div class="stat-label">Products</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { use } from 'echarts/core'
import { CanvasRenderer } from 'echarts/renderers'
import { MapChart } from 'echarts/charts'
import { VisualMapComponent, TooltipComponent, GeoComponent } from 'echarts/components'
import VChart from 'vue-echarts'
import * as echarts from 'echarts/core'

// 导入本地世界地图数据
import worldMapData from '@/assets/world.json'

use([
  CanvasRenderer,
  MapChart,
  VisualMapComponent,
  TooltipComponent,
  GeoComponent
])

export default {
  name: 'WorldMap',
  components: {
    VChart
  },
  props: {
    // 可以通过props传入统计数据
    statsData: {
      type: Object,
      default: () => ({
        spreadsheets: 264,
        rows: 21367,
        products: 1247
      })
    },
    // 可以通过props传入国家数据
    countryData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      loading: true,
      mapError: false,
      mapLoaded: false,
      chartOption: {},
      themeObserver: null, // 主题变化观察器
      stats: {
        spreadsheets: this.statsData.spreadsheets,
        rows: this.statsData.rows,
        products: this.statsData.products
      },
      // 默认国家数据 - 如果没有提供数据，将使用随机生成的数据
      defaultCountryData: [
        { name: 'United States', value: 85 },
        { name: 'China', value: 70 },
        { name: 'Canada', value: 60 },
        { name: 'Russia', value: 55 },
        { name: 'Brazil', value: 50 },
        { name: 'Australia', value: 65 },
        { name: 'India', value: 75 },
        { name: 'United Kingdom', value: 80 },
        { name: 'Germany', value: 78 },
        { name: 'France', value: 72 },
        { name: 'Japan', value: 68 },
        { name: 'South Africa', value: 45 },
        { name: 'Mexico', value: 55 },
        { name: 'Spain', value: 62 },
        { name: 'Italy', value: 60 },
        { name: 'Saudi Arabia', value: 40 },
        { name: 'South Korea', value: 65 },
        { name: 'Indonesia', value: 35 },
        { name: 'Turkey', value: 50 },
        { name: 'Argentina', value: 30 }
      ],
      // 欧洲国家列表 - 确保这些国家都有数据
      europeanCountries: [
        'Albania', 'Andorra', 'Austria', 'Belarus', 'Belgium', 'Bosnia and Herzegovina',
        'Bulgaria', 'Croatia', 'Czech Republic', 'Denmark', 'Estonia', 'Finland',
        'France', 'Germany', 'Greece', 'Hungary', 'Iceland', 'Ireland', 'Italy',
        'Latvia', 'Liechtenstein', 'Lithuania', 'Luxembourg', 'Malta', 'Moldova',
        'Monaco', 'Montenegro', 'Netherlands', 'North Macedonia', 'Norway', 'Poland',
        'Portugal', 'Romania', 'Russia', 'San Marino', 'Serbia', 'Slovakia', 'Slovenia',
        'Spain', 'Sweden', 'Switzerland', 'Ukraine', 'United Kingdom', 'Vatican City'
      ],
      // 主题颜色
      isDarkMode: document.documentElement.getAttribute('data-theme') === 'dark',
      localCountryData: [],
      resizeObserver: null,
      resizeTimeout: null,
      // 添加定时器属性
      dataUpdateTimer: null,
      // 数据更新间隔（毫秒）
      updateInterval: 5000,
      // 控制实时更新开关
      dataUpdateActive: true
    }
  },
  watch: {
    // 监听statsData的变化
    statsData: {
      handler(newStatsData) {
        if (newStatsData) {
          this.stats.spreadsheets = newStatsData.spreadsheets;
          this.stats.rows = newStatsData.rows;
          this.stats.products = newStatsData.products;
          console.log('WorldMap - statsData更新:', newStatsData);
        }
      },
      deep: true,
      immediate: true
    }
  },
  mounted() {
    // 初始化本地数据副本 - 使用包含欧洲国家的数据
    this.localCountryData = this.countryData.length > 0 
      ? [...this.countryData] 
      : [] // 空数组，让generateRandomData方法初始化包含欧洲国家的数据
      
    this.loadWorldMap()
    
    // 监听主题变化
    window.addEventListener('storage', this.handleThemeChange)
    
    // 使用事件总线监听主题变化
    if (window.emitter) {
      window.emitter.on('theme-changed', this.handleThemeChange)
    }

    // 添加MutationObserver监听data-theme属性变化
    this.themeObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'attributes' && mutation.attributeName === 'data-theme') {
          this.handleThemeChange()
        }
      })
    })

    // 开始观察document.documentElement的属性变化
    this.themeObserver.observe(document.documentElement, {
      attributes: true,
      attributeFilter: ['data-theme']
    })
    
    // 确保地图在DOM完全渲染后初始化
    this.$nextTick(() => {
      // 使用自定义的防抖方式监听容器大小变化
      this.initResizeObserver()
      
      // 添加窗口大小变化监听
      window.addEventListener('resize', this.handleWindowResize)
      
      // 地图初始化后手动触发一次resize
      setTimeout(() => {
        if (this.$refs.chart && !this.loading && !this.mapError) {
          this.$refs.chart.resize()
          // 确保移动端正确渲染
          this.ensureMobileRendering()
        }
      }, 300)
      
      // 启动定时器，定期更新数据
      this.startDataUpdateTimer()
    })
  },
  beforeUnmount() {
    window.removeEventListener('storage', this.handleThemeChange)

    // 移除事件总线监听
    if (window.emitter) {
      window.emitter.off('theme-changed', this.handleThemeChange)
    }

    // 移除主题观察器
    if (this.themeObserver) {
      this.themeObserver.disconnect()
    }

    // 移除窗口大小变化监听
    window.removeEventListener('resize', this.handleWindowResize)
    
    // 清除定时器
    if (this.resizeTimeout) {
      clearTimeout(this.resizeTimeout)
    }
    
    // 断开ResizeObserver
    if (this.resizeObserver) {
      this.resizeObserver.disconnect()
      this.resizeObserver = null
    }
    
    // 清除数据更新定时器
    this.stopDataUpdateTimer()
  },
  methods: {
    async loadWorldMap() {
      this.loading = true
      this.mapError = false
      
      try {
        // 注册地图数据
        // 将Natural Earth GeoJSON数据格式转换为ECharts需要的格式
        // 注册地图数据
        // 将Natural Earth GeoJSON数据格式转换为ECharts需要的格式
        const mapData = this.processNaturalEarthData(worldMapData)
        echarts.registerMap('world', mapData)
        
        // 确保有欧洲国家数据
        if (this.localCountryData.length === 0) {
          this.localCountryData = this.initializeWithEuropeanCountries()
        }
        
        // 设置图表选项
        this.chartOption = this.getChartOptions(this.localCountryData)
        this.mapLoaded = true
        
        // 确保图表在数据加载后更新尺寸
        this.$nextTick(() => {
          if (this.$refs.chart) {
            setTimeout(() => {
              this.$refs.chart.resize()
              
              // 检测移动设备并调整选项
              const isMobile = window.innerWidth <= 768
              if (this.$refs.chart.chart) {
                const chartInstance = this.$refs.chart.chart
                
                // 根据主题设置tooltip样式
                const option = chartInstance.getOption()
                if (option.tooltip) {
                  option.tooltip.backgroundColor = this.isDarkMode ? 'rgba(32, 6, 60, 0.9)' : 'rgba(255, 255, 255, 0.95)'
                  option.tooltip.borderColor = '#FF5500' // 橙色边框
                  option.tooltip.borderWidth = 2
                  option.tooltip.textStyle = { color: this.isDarkMode ? '#ffffff' : '#333333' }
                  option.tooltip.extraCssText = `box-shadow: 0 3px 15px ${this.isDarkMode ? 'rgba(147, 51, 234, 0.5)' : 'rgba(0, 0, 0, 0.2)'}; border-radius: 8px; padding: 12px;`
                  option.tooltip.formatter = function(params) {
                    // 自定义提示框内容，使数值更加突出
                    return `<div style="font-weight: bold;">${params.name}</div>
                           <div style="color:#FF5500; font-size: 18px; font-weight: bold; margin-top: 5px;">${params.value}</div>`;
                  }

                  // 立即应用更新的tooltip样式
                  chartInstance.setOption({
                    tooltip: option.tooltip
                  })
                }
                
                if (isMobile) {
                  // 为移动设备设置更小的缩放和适当的中心点
                  chartInstance.setOption({
                    geo: [{
                      zoom: 0.8,
                      center: [0, 30]
                    }]
                  })
                } else {
                  // PC端保持原有配置
                  option.geo[0].zoom = 1.2
                }
                
                // 所有设备上都保持缩放和平移功能
                option.geo[0].roam = true
                
                // 根据主题设置tooltip样式
                if (option.tooltip) {
                  option.tooltip.backgroundColor = this.isDarkMode ? 'rgba(32, 6, 60, 0.9)' : 'rgba(255, 255, 255, 0.95)'
                  option.tooltip.borderColor = '#FF5500' // 橙色边框
                  option.tooltip.borderWidth = 2
                  option.tooltip.textStyle = { color: this.isDarkMode ? '#ffffff' : '#333333' }
                  option.tooltip.extraCssText = `box-shadow: 0 3px 15px ${this.isDarkMode ? 'rgba(147, 51, 234, 0.5)' : 'rgba(0, 0, 0, 0.2)'}; border-radius: 8px; padding: 12px;`
                  option.tooltip.formatter = function(params) {
                    // 自定义提示框内容，使数值更加突出
                    return `<div style="font-weight: bold;">${params.name}</div>
                           <div style="color:#FF5500; font-size: 18px; font-weight: bold; margin-top: 5px;">${params.value}</div>`;
                  }
                }
                
                // 更新配置
                chartInstance.setOption(option)
              }
            }, 200)
          }
        })
      } catch (error) {
        console.error('Error loading world map:', error)
        this.mapError = true
      } finally {
        this.loading = false
      }
    },
    
    // 处理窗口大小变化
    handleWindowResize() {
      this.debouncedResize()
      // 在窗口大小变化时也检查移动端渲染
      this.ensureMobileRendering()
    },
    
    // 初始化ResizeObserver
    initResizeObserver() {
      // 确保在浏览器环境中
      if (typeof window !== 'undefined' && window.ResizeObserver) {
        const mapWrapper = this.$el.querySelector('.map-wrapper')
        
        if (mapWrapper) {
          this.resizeObserver = new ResizeObserver(() => {
            this.debouncedResize()
          })
          
          this.resizeObserver.observe(mapWrapper)
        }
      }
    },
    
    // 防抖处理resize
    debouncedResize() {
      if (this.resizeTimeout) {
        clearTimeout(this.resizeTimeout)
      }
      
      this.resizeTimeout = setTimeout(() => {
        if (this.$refs.chart && this.mapLoaded) {
          this.$refs.chart.resize()
        }
      }, 200)
    },
    
    // 处理Natural Earth数据为ECharts需要的格式
    processNaturalEarthData(geoJson) {
      // 确保数据格式正确
      if (!geoJson || !geoJson.features) {
        console.error('无效的GeoJSON数据格式')
        return geoJson
      }
      
      try {
        // 为每个国家添加名称映射，确保与我们的国家数据匹配
        const features = geoJson.features.map(feature => {
          // 确保feature有properties和name属性
          if (feature.properties) {
            // 使用Natural Earth的国家名称
            const name = feature.properties.NAME || feature.properties.name || feature.properties.ADMIN || ''
            
            // 添加name属性，如果已存在则保留
            feature.properties.name = name
          }
          return feature
        })
        
        // 返回处理后的GeoJSON
        return {
          ...geoJson,
          features
        }
      } catch (error) {
        console.error('处理地图数据时出错:', error)
        return geoJson
      }
    },
    
    getChartOptions(data) {
      // 根据当前主题确定颜色
      const backgroundColor = this.isDarkMode ? 'transparent' : 'transparent'
      const textColor = this.isDarkMode ? '#ffffff' : '#333333'
      const borderColor = this.isDarkMode ? 'rgba(255, 255, 255, 0.2)' : 'rgba(0, 0, 0, 0.2)'
      const areaColor = this.isDarkMode ? '#1a1a2a' : '#f3f3f3'
      const emphasisAreaColor = this.isDarkMode ? '#2a2a3a' : '#e0e0e0'
      
      // 紫色渐变主题
      const purpleTheme = {
        colorMin: this.isDarkMode ? '#4b0082' : '#e6d6f9',
        colorMax: this.isDarkMode ? '#9370db' : '#9333ea'
      }
      
      return {
        backgroundColor: backgroundColor,
        tooltip: {
          trigger: 'item',
          formatter: function(params) {
            // 自定义提示框内容，使数值更加突出
            return `<div style="font-weight: bold;">${params.name}</div>
                   <div style="color:#FF5500; font-size: 18px; font-weight: bold; margin-top: 5px;">${params.value}</div>`;
          },
          backgroundColor: this.isDarkMode ? 'rgba(32, 6, 60, 0.9)' : 'rgba(255, 255, 255, 0.95)', // 根据主题调整背景色
          borderColor: '#FF5500', // 橙色边框
          borderWidth: 2,
          textStyle: {
            color: this.isDarkMode ? '#ffffff' : '#333333' // 根据主题调整文字颜色
          },
          extraCssText: `box-shadow: 0 3px 15px ${this.isDarkMode ? 'rgba(147, 51, 234, 0.5)' : 'rgba(0, 0, 0, 0.2)'}; border-radius: 8px; padding: 12px;`,
          confine: true,
          enterable: true
        },
        visualMap: {
          min: 0,
          max: 100,
          text: ['High', 'Low'],
          realtime: false,
          calculable: true,
          inRange: {
            color: [purpleTheme.colorMin, purpleTheme.colorMax]
          },
          textStyle: {
            color: textColor
          },
          left: 'right',
          top: 'bottom'
        },
        geo: {
          map: 'world',
          roam: true, // 启用缩放和平移
          zoom: 1.2, // 初始缩放级别
          scaleLimit: {
            min: 1,
            max: 5
          },
          emphasis: {
            label: {
              show: false
            },
            itemStyle: {
              areaColor: emphasisAreaColor
            }
          },
          itemStyle: {
            areaColor: areaColor,
            borderColor: borderColor,
            borderWidth: 0.5
          },
          silent: false // 启用鼠标事件
        },
        series: [
          {
            name: 'Country Data',
            type: 'map',
            geoIndex: 0,
            data: data.map(item => ({
              name: item.name,
              value: item.value
            }))
          }
        ]
      }
    },
    
    handleThemeChange(event) {
      // 更新主题状态
      if (event && event.isDarkMode !== undefined) {
        this.isDarkMode = event.isDarkMode
      } else {
        this.isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark'
      }

      // 如果地图已加载，更新图表选项
      if (this.mapLoaded && this.$refs.chart && this.$refs.chart.chart) {
        const chartInstance = this.$refs.chart.chart

        // 重新生成完整的图表选项
        const newOptions = this.getChartOptions(this.localCountryData)

        // 完全重新设置图表选项以确保主题变化生效
        chartInstance.setOption(newOptions, true) // 第二个参数true表示不合并，完全替换

        // 手动触发一次重绘
        this.$nextTick(() => {
          chartInstance.resize()
        })
      }
    },
    
    // 提供一个公共方法来更新数据
    updateData(newData) {
      if (newData && Array.isArray(newData) && newData.length > 0) {
        // 不直接修改props，而是使用本地数据副本
        this.localCountryData = [...newData]
        if (this.mapLoaded) {
          this.chartOption = this.getChartOptions(this.localCountryData)
          // 手动触发一次重绘
          this.$nextTick(() => {
            this.debouncedResize()
          })
        }
      }
    },
    
    // 更新统计数据
    updateStats(newStats) {
      if (newStats) {
        this.stats = {
          ...this.stats,
          ...newStats
        }
      }
    },
    
    // 移动设备专用初始化函数，确保在小屏幕上正确显示
    ensureMobileRendering() {
      // 检查是否是移动设备
      const isMobile = window.innerWidth <= 768
      
      if (this.$refs.chart && this.mapLoaded) {
        // 重设图表尺寸
        this.$refs.chart.resize()
        
        // 调整地图缩放级别和中心位置
        const chartInstance = this.$refs.chart.chart
        if (chartInstance) {
          const option = chartInstance.getOption()
          
          if (isMobile) {
            // 移动设备上的特殊设置
            // 设置地图缩放比例较小，确保移动设备上可以看到完整地图
            option.geo[0].zoom = 0.8
            
            // 增加更多的交互配置
            option.geo[0].silent = false
            option.geo[0].nameProperty = 'name'
            option.geo[0].aspectScale = 0.75 // 调整地图比例
            
            // 减小地图拖动阻尼，使其更流畅
            if (!option.geo[0].roamDetail) {
              option.geo[0].roamDetail = {
                friction: 0.3, // 摩擦系数，较小表示拖动更加流畅
                damping: 0.5  // 阻尼，较小表示惯性较大
              }
            }
            
            // 添加额外的触摸事件监听（确保地图可拖动）
            this.enhanceTouchInteraction(chartInstance)
          } else {
            // PC端保持原有配置
            option.geo[0].zoom = 1.2
          }
          
          // 所有设备上都保持缩放和平移功能
          option.geo[0].roam = true
          
          // 根据主题设置tooltip样式
          if (option.tooltip) {
            option.tooltip.backgroundColor = this.isDarkMode ? 'rgba(32, 6, 60, 0.9)' : 'rgba(255, 255, 255, 0.95)'
            option.tooltip.borderColor = '#FF5500' // 橙色边框
            option.tooltip.borderWidth = 2
            option.tooltip.textStyle = { color: this.isDarkMode ? '#ffffff' : '#333333' }
            option.tooltip.extraCssText = `box-shadow: 0 3px 15px ${this.isDarkMode ? 'rgba(147, 51, 234, 0.5)' : 'rgba(0, 0, 0, 0.2)'}; border-radius: 8px; padding: 12px;`
            option.tooltip.formatter = function(params) {
              // 自定义提示框内容，使数值更加突出
              return `<div style="font-weight: bold;">${params.name}</div>
                     <div style="color:#FF5500; font-size: 18px; font-weight: bold; margin-top: 5px;">${params.value}</div>`;
            }
          }
          
          // 更新配置
          chartInstance.setOption(option)
        }
      }
    },
    
    // 增强移动设备触摸交互
    enhanceTouchInteraction(chartInstance) {
      const chartDom = chartInstance.getDom()
      
      // 阻止地图区域的默认触摸行为，避免页面滚动干扰地图拖动
      chartDom.addEventListener('touchstart', (e) => {
        if (e.touches.length === 1) {
          e.preventDefault() // 阻止单指触摸的默认行为
        }
      }, { passive: false })
    },

         // 启动数据更新定时器
     startDataUpdateTimer() {
       if (this.dataUpdateTimer) {
         clearInterval(this.dataUpdateTimer)
       }
       
       // 如果状态不是活跃的，不启动定时器
       if (!this.dataUpdateActive) {
         return
       }
       
       this.dataUpdateTimer = setInterval(() => {
         // 创建随机数据
         const newData = this.generateRandomData()
         
         // 应用图表动画效果
         if (this.$refs.chart && this.$refs.chart.chart) {
           const chartInstance = this.$refs.chart.chart
           chartInstance.setOption({
             series: [{
               data: newData,
               // 添加动画效果
               animationDuration: 1000,
               animationEasing: 'cubicOut',
               animationDelay: function () {
                 return Math.random() * 200
               }
             }]
           })
         } else {
           this.updateData(newData)
         }
       }, this.updateInterval)
     },

    // 停止数据更新定时器
    stopDataUpdateTimer() {
      if (this.dataUpdateTimer) {
        clearInterval(this.dataUpdateTimer)
        this.dataUpdateTimer = null
      }
    },

         // 生成随机数据
     generateRandomData() {
       // 如果本地数据为空，则初始化包含欧洲国家的数据
       if (this.localCountryData.length === 0) {
         return this.initializeWithEuropeanCountries()
       }
       
       const randomData = []
       const currentData = [...this.localCountryData]
       
       // 随机选择8-12个国家进行数据更新
       const changeCount = Math.floor(Math.random() * 5) + 8
       const changeIndices = []
       
       // 选择要更改的国家索引
       while (changeIndices.length < changeCount) {
         const idx = Math.floor(Math.random() * currentData.length)
         if (!changeIndices.includes(idx)) {
           changeIndices.push(idx)
         }
       }
       
       // 生成新的数据集
       for (let i = 0; i < currentData.length; i++) {
         // 如果当前索引在需要更改的列表中，则生成新值
         if (changeIndices.includes(i)) {
           // 根据当前值生成一个合理的变化量
           const currentValue = currentData[i].value || 50
           const change = Math.floor(Math.random() * 30) - 10 // -10到20之间的变化
           const newValue = Math.max(5, Math.min(100, currentValue + change)) // 限制在5-100之间
           
           randomData.push({
             name: currentData[i].name,
             value: newValue
           })
         } else {
           // 保持原来的值不变
           randomData.push({
             name: currentData[i].name,
             value: currentData[i].value
           })
         }
       }
       
       // 更新本地数据副本
       this.localCountryData = [...randomData]
       
       // 同时更新统计卡片数据
       this.updateRandomStats()
       
       return randomData
     },
     
     // 初始化包含欧洲国家的数据
     initializeWithEuropeanCountries() {
       const initialData = []
       
       // 首先添加默认国家数据
       this.defaultCountryData.forEach(country => {
         initialData.push({...country})
       })
       
       // 然后添加所有欧洲国家（如果尚未包含）
       this.europeanCountries.forEach(countryName => {
         // 检查是否已经在默认数据中
         const exists = initialData.some(item => item.name === countryName)
         
         if (!exists) {
           // 为欧洲国家生成随机值
           initialData.push({
             name: countryName,
             value: Math.floor(Math.random() * 60) + 20 // 20-80之间的随机值
           })
         }
       })
       
       // 更新本地数据副本
       this.localCountryData = [...initialData]
       
       return initialData
     },
      
      // 更新统计卡片的随机数据
      updateRandomStats() {
        // 根据当前值生成随机变化，确保变化在合理范围内
        // 电子表格数量保持不变
        const rows = this.stats.rows + Math.floor(Math.random() * 300) - 50
        const products = this.stats.products + Math.floor(Math.random() * 20) - 5
        
        // 确保数据不会出现负值
        // this.stats.spreadsheets 保持不变
        this.stats.rows = Math.max(100, rows)
        this.stats.products = Math.max(50, products)
      },

      // 切换实时更新开关
      toggleDataUpdate() {
        this.dataUpdateActive = !this.dataUpdateActive
        if (this.dataUpdateActive) {
          this.startDataUpdateTimer()
        } else {
          this.stopDataUpdateTimer()
        }
      }
  }
}
</script>

<style scoped>
.world-map-container {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  margin-bottom: 2rem;
  position: relative;
  box-sizing: border-box;
}

.map-content {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: stretch;
  gap: 2rem;
  box-sizing: border-box;
  margin: 0 auto;
}

.map-wrapper {
  flex: 4;
  height: 400px;
  position: relative;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  background-color: var(--card-bg, transparent);
  border: 1px solid var(--border-color, rgba(147, 51, 234, 0.2));
}

.chart {
  width: 100%;
  height: 100%;
  min-height: 100%;
}

.loading-overlay, .error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: var(--card-bg, rgba(30, 9, 64, 0.8));
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(147, 51, 234, 0.3);
  border-radius: 50%;
  border-top: 4px solid var(--primary-color, #9333ea);
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text, .error-text {
  color: var(--text-color, #ffffff);
  font-size: 1rem;
}

.error-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(255, 0, 0, 0.2);
  color: #ff6b6b;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 10px;
}

.stats-cards {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  gap: 1.5rem;
  flex: 1;
}

.stat-card {
  background: linear-gradient(135deg, var(--primary-color, #9333ea), var(--secondary-color, #c084fc));
  color: white;
  padding: 1rem 1.8rem;
  border-radius: 12px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(147, 51, 234, 0.3);
  min-width: 150px;
  width: 180px;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.3));
  border-radius: 14px;
  z-index: -1;
  opacity: 0.6;
  filter: blur(4px);
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(147, 51, 234, 0.5);
}

.stat-number {
  font-size: 2.8rem;
  font-weight: 700;
  margin-bottom: 0.3rem;
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
}

/* 添加数字变化的动画 */
.stat-number.animated {
  transition: all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* 实时更新控制按钮样式 */
.update-control {
  position: absolute;
  bottom: 10px;
  right: 10px;
  z-index: 50;
}

.update-button {
  background: rgba(147, 51, 234, 0.7);
  color: white;
  border: none;
  border-radius: 20px;
  padding: 6px 12px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.2);
}

.update-button:hover {
  background: rgba(147, 51, 234, 0.9);
  transform: translateY(-2px);
  box-shadow: 0 5px 12px rgba(0, 0, 0, 0.3);
}

.update-button.active {
  background: rgba(220, 38, 38, 0.7);
}

.update-button.active:hover {
  background: rgba(220, 38, 38, 0.9);
}

@media (max-width: 992px) {
  .map-content {
    flex-direction: column;
  }
  
  .stats-cards {
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: center;
    width: 100%;
    gap: 1.5rem;
    margin-top: 1.5rem;
  }
  
  .stat-card {
    width: 180px;
  }
}

@media (max-width: 768px) {
  .world-map-container {
    width: 100%;
    padding: 0;
    margin: 0 auto;
    max-width: 100%;
    overflow: visible;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
  }

  .map-content {
    width: 100%;
    margin: 0 auto;
    padding: 0;
    box-sizing: border-box;
    max-width: 100%;
    flex-direction: column;
    align-items: center;
  }

  .map-wrapper {
    height: 300px;
    min-height: 300px;
    width: 100%;
    overflow: visible;
    margin: 0 auto;
    display: flex;
    justify-content: center;
  }
  
  .chart {
    width: 100% !important;
    height: 300px !important;
    min-height: 300px;
  }
  
  .stats-cards {
    flex-direction: row;
    flex-wrap: nowrap;
    gap: 0.5rem;
    margin-top: 1rem;
    justify-content: space-between;
    width: 100%;
  }
  
  .stat-card {
    flex: 1;
    width: auto;
    padding: 0.8rem 0.5rem;
    margin: 0;
    min-width: 0;
  }
  
  .stat-number {
    font-size: 1.8rem;
  }
  
  .stat-label {
    font-size: 0.9rem;
  }

  .update-button {
    font-size: 0.8rem;
    padding: 4px 8px;
  }
}

@media (max-width: 576px) {
  .world-map-container {
    margin-bottom: 1rem;
  }
  
  .map-content {
    padding: 0 5px;
  }
  
  .map-wrapper {
    height: 250px;
    min-height: 250px;
  }
  
  .chart {
    height: 250px !important;
    min-height: 250px;
  }
  
  .stats-cards {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    gap: 0.4rem;
    width: 100%;
    padding: 0 0.2rem;
  }
  
  .stat-card {
    flex: 1;
    width: auto;
    padding: 0.6rem 0.3rem;
    margin: 0;
    border-radius: 8px;
  }
  
  .stat-number {
    font-size: 1.6rem;
    margin-bottom: 0.2rem;
  }
  
  .stat-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 400px) {
  .map-wrapper {
    height: 220px;
    min-height: 220px;
  }
  
  .chart {
    height: 220px !important;
    min-height: 220px;
  }
  
  .stats-cards {
    flex-direction: row;
    align-items: center;
    gap: 0.3rem;
    padding: 0 0.1rem;
  }
  
  .stat-card {
    flex: 1;
    padding: 0.5rem 0.2rem;
    border-radius: 6px;
  }
  
  .stat-number {
    font-size: 1.3rem;
    margin-bottom: 0.1rem;
  }
  
  .stat-label {
    font-size: 0.7rem;
  }
}
</style> 