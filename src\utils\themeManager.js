import { emitter } from '@/utils/eventBus';

// 主题变量定义
const themes = {
  dark: {
    primaryBg: 'linear-gradient(126.54deg, rgba(35, 15, 72, 1) 0%, rgba(23, 10, 48, 1) 31.26%, rgba(7, 12, 31, 1) 70.66%, rgba(37, 15, 74, 1) 100%)',
    navBg: 'rgba(77, 48, 105, 1)',
    textColor: '#ffffff',
    headingColor: '#ffffff',
    cardBg: 'rgba(30, 9, 64, 0.8)',
    borderColor: 'rgba(147, 51, 234, 0.2)'
  },
  light: {
    primaryBg: 'linear-gradient(131.67deg, rgba(240, 213, 252, 1) 0%, rgba(252, 247, 255, 1) 31.26%, rgba(249, 245, 252, 1) 70.66%, rgba(240, 212, 252, 1) 100%)',
    navBg: 'rgba(193, 151, 209, 1)',
    textColor: '#333333',
    headingColor: '#230F48',
    cardBg: '#ffffff',
    borderColor: 'rgba(76, 175, 80, 0.15)'
  }
};

// 定义需要特别处理的页面路径
const specialPages = {
  'products': {
    selectors: ['.products', '.product-page', '.products-view', '.products-grid', '.product-card', '.product-info', '.product-name', '.filters-section', '.filter-container', '.product-image']
  },
  'product': {
    selectors: ['.product-detail', '.product-detail-view', '.product-detail-card', '.product-info', '.product-specs', '.product-description']
  }
};

const ThemeManager = {
  // 应用主题到所有页面
  applyTheme(isDarkMode) {
    const theme = isDarkMode ? 'dark' : 'light';
    
    // 设置文档根元素的数据属性
    document.documentElement.setAttribute('data-theme', theme);
    
    // 为根元素添加/移除类
    if (isDarkMode) {
      document.documentElement.classList.add('dark-theme');
    } else {
      document.documentElement.classList.remove('dark-theme');
    }
    
    // 应用CSS变量
    const currentTheme = themes[theme];
    for (const [key, value] of Object.entries(currentTheme)) {
      document.documentElement.style.setProperty(`--${key}`, value);
    }
    
    // 广播主题变化事件
    emitter.emit('theme-changed', { isDarkMode, theme: currentTheme });
    
    // 确保所有路由页面都能接收到主题变化
    this.applyThemeToAllPages(isDarkMode);
    
    // 延迟处理特定页面，确保DOM已完全加载
    setTimeout(() => {
      this.handleSpecialPages(isDarkMode);
    }, 100);
  },
  
  // 特别处理页面元素
  applyThemeToAllPages(isDarkMode) {
    // 获取所有页面根元素
    const pageContainers = document.querySelectorAll('.page-container, .view-container, .route-view, .product-page, .product-detail, .product-container, .product-list, .product-grid');
    pageContainers.forEach(container => {
      // 设置背景和文本颜色
      if (isDarkMode) {
        container.style.background = themes.dark.primaryBg;
        container.style.color = themes.dark.textColor;
      } else {
        container.style.background = themes.light.primaryBg;
        container.style.color = themes.light.textColor;
      }
    });
    
    // 处理卡片元素
    const cardElements = document.querySelectorAll('.card, .product-card, .info-card, .el-card, .product-item, .product-detail-card');
    cardElements.forEach(card => {
      if (isDarkMode) {
        card.style.background = themes.dark.cardBg;
        card.style.color = themes.dark.textColor;
        card.style.borderColor = themes.dark.borderColor;
      } else {
        card.style.background = themes.light.cardBg;
        card.style.color = themes.light.textColor;
        card.style.borderColor = themes.light.borderColor;
      }
    });
    
    // 处理文本元素
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    headings.forEach(heading => {
      heading.style.color = isDarkMode ? themes.dark.headingColor : themes.light.headingColor;
    });
    
    // 特别处理产品页面
    this.handleProductPages(isDarkMode);
  },
  
  // 特别处理产品页面
  handleProductPages(isDarkMode) {
    // 找到所有产品页面相关元素
    const productPages = document.querySelectorAll('.product-page, .products, .product-list-view, .product-grid-view');
    productPages.forEach(page => {
      page.style.background = isDarkMode ? themes.dark.primaryBg : themes.light.primaryBg;
      page.style.color = isDarkMode ? themes.dark.textColor : themes.light.textColor;
    });
    
    // 处理产品筛选区域
    const filterSections = document.querySelectorAll('.filter-section, .filter-container, .product-filters');
    filterSections.forEach(section => {
      section.style.background = isDarkMode ? themes.dark.cardBg : themes.light.cardBg;
      section.style.color = isDarkMode ? themes.dark.textColor : themes.light.textColor;
    });
    
    // 处理产品卡片
    const productCards = document.querySelectorAll('.product-card, .product-item, .product-container');
    productCards.forEach(card => {
      card.style.background = isDarkMode ? themes.dark.cardBg : themes.light.cardBg;
      card.style.color = isDarkMode ? themes.dark.textColor : themes.light.textColor;
      card.style.borderColor = isDarkMode ? themes.dark.borderColor : themes.light.borderColor;
      
      // 查找并处理卡片内的标题
      const cardHeadings = card.querySelectorAll('h1, h2, h3, h4, h5, h6, .product-title, .product-name');
      cardHeadings.forEach(heading => {
        heading.style.color = isDarkMode ? themes.dark.headingColor : themes.light.headingColor;
      });
      
      // 查找并处理卡片内的段落文本
      const cardTexts = card.querySelectorAll('p, .product-description, .product-price');
      cardTexts.forEach(text => {
        text.style.color = isDarkMode ? themes.dark.textColor : themes.light.textColor;
      });
    });
    
    // 处理分页控件
    const paginationElements = document.querySelectorAll('.pagination, .page-controls');
    paginationElements.forEach(pagination => {
      pagination.style.background = 'transparent';
      
      // 分页按钮
      const buttons = pagination.querySelectorAll('button, .page-button, .page-link');
      buttons.forEach(button => {
        button.style.background = isDarkMode ? themes.dark.cardBg : themes.light.cardBg;
        button.style.color = isDarkMode ? themes.dark.textColor : themes.light.textColor;
        button.style.borderColor = isDarkMode ? themes.dark.borderColor : themes.light.borderColor;
      });
    });
  },
  
  // 处理特定页面的样式
  handleSpecialPages(isDarkMode) {
    // 获取当前URL
    const currentPath = window.location.pathname;
    
    // 检查当前路径是否包含特殊页面关键字
    for (const [pageKey, pageConfig] of Object.entries(specialPages)) {
      if (currentPath.includes(pageKey)) {
        // 应用特定页面的样式
        this.applyThemeToSpecificSelectors(pageConfig.selectors, isDarkMode);
      }
    }
    
    // 特别处理产品页面
    if (currentPath.includes('products') || currentPath.includes('product/')) {
      // 强制设置产品页面根元素背景
      const productsElement = document.querySelector('.products');
      if (productsElement) {
        productsElement.style.background = isDarkMode ? themes.dark.primaryBg + ' !important' : themes.light.primaryBg + ' !important';
        
        // 设置所有子元素的背景
        this.forceStyleAllChildren(productsElement, isDarkMode);
      }
    }
  },
  
  // 应用主题到特定选择器
  applyThemeToSpecificSelectors(selectors, isDarkMode) {
    selectors.forEach(selector => {
      const elements = document.querySelectorAll(selector);
      elements.forEach(element => {
        // 设置基本样式
        element.style.background = isDarkMode ? themes.dark.cardBg : themes.light.cardBg;
        element.style.color = isDarkMode ? themes.dark.textColor : themes.light.textColor;
        
        // 如果是卡片类元素，还需设置边框和阴影
        if (selector.includes('card') || selector.includes('container')) {
          element.style.borderColor = isDarkMode ? themes.dark.borderColor : themes.light.borderColor;
        }
        
        // 如果是标题类元素
        if (selector.includes('title') || selector.includes('name')) {
          element.style.color = isDarkMode ? themes.dark.headingColor : themes.light.headingColor;
        }
      });
    });
  },
  
  // 强制设置所有子元素的样式
  forceStyleAllChildren(parentElement, isDarkMode) {
    // 获取所有子元素
    const children = parentElement.querySelectorAll('*');
    
    children.forEach(child => {
      // 获取计算后的样式
      const computedStyle = window.getComputedStyle(child);
      
      // 如果元素有背景色且不是透明的，则强制应用主题背景
      if (computedStyle.backgroundColor && 
          computedStyle.backgroundColor !== 'transparent' && 
          computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)') {
        
        // 检查元素类型，决定应用哪种背景
        if (child.classList.contains('product-card') || 
            child.classList.contains('filter-container') || 
            child.classList.contains('info-card')) {
          // 卡片类元素
          child.style.background = isDarkMode ? themes.dark.cardBg : themes.light.cardBg;
        } else if (child.classList.contains('product-image')) {
          // 产品图片容器
          child.style.background = 'transparent';
        } else {
          // 普通元素
          child.style.background = 'transparent';
        }
      }
      
      // 强制应用文本颜色
      if (child.tagName === 'H1' || child.tagName === 'H2' || 
          child.tagName === 'H3' || child.tagName === 'H4' || 
          child.tagName === 'H5' || child.tagName === 'H6' || 
          child.classList.contains('product-name') || 
          child.classList.contains('product-title')) {
        // 标题元素
        child.style.color = isDarkMode ? themes.dark.headingColor : themes.light.headingColor;
      } else {
        // 普通文本元素
        child.style.color = isDarkMode ? themes.dark.textColor : themes.light.textColor;
      }
    });
  }
};

// 添加路由变化监听
if (typeof window !== 'undefined') {
  // 保存上一个URL
  let lastUrl = window.location.href;
  
  // 创建URL变化检测器
  const observer = new MutationObserver(() => {
    if (window.location.href !== lastUrl) {
      lastUrl = window.location.href;
      
      // 延迟执行，确保DOM已更新
      setTimeout(() => {
        // 获取当前主题
        const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark';
        
        // 重新应用主题
        ThemeManager.applyTheme(isDarkMode);
      }, 100);
    }
  });
  
  // 开始观察document的子树变化
  observer.observe(document, { subtree: true, childList: true });
  
  // 监听主题变化事件
  emitter.on('theme-changed', (data) => {
    if (data && typeof data.isDarkMode !== 'undefined') {
      // 延迟执行，确保页面已完全加载
      setTimeout(() => {
        ThemeManager.handleSpecialPages(data.isDarkMode);
      }, 200);
    }
  });
}

export default ThemeManager; 