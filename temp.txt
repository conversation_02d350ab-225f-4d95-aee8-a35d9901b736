computed: {
  showRelatedProducts() {
    // 最多展示3个商品，剩下1个位置给"更多"
    return this.relatedProducts.slice(0, 3);
  },
  relatedEmptyCount() {
    // 补空白卡片数量
    return 4 - this.showRelatedProducts.length;
  },
  qcImageGroups() {
    // 将QC图片按每组6张进行分组
    const groups = [];
    for (let i = 0; i < this.qcImages.length; i += 6) {
      groups.push(this.qcImages.slice(i, i + 6));
    }
    return groups;
  }
}, 