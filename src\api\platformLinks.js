import apiClient from '@/services/api';

/**
 * 通用API响应处理程序
 * 提供标准化的错误处理和响应格式
 */
const handleApiResponse = async (apiCall) => {
  try {
    const response = await apiCall();

    // 检查response是否为undefined或null
    if (!response) {
      console.error('API响应为空');
      return {
        code: 500,
        msg: 'API响应为空',
        data: null
      };
    }

    // 检查response.data是否存在，兼容不同的API响应格式
    if (response.data !== undefined) {
      return {
        code: response.data.code || 200,
        msg: response.data.msg || 'Success',
        data: response.data.data || response.data
      };
    } else {
      // 直接使用response作为数据，适用于拦截器已经提取了data的情况
      return {
        code: response.code || 200,
        msg: response.msg || 'Success',
        data: response.data || response
      };
    }
  } catch (error) {
    console.error('API请求错误:', error);
    return {
      code: error.response?.data?.code || error.response?.status || 500,
      msg: error.response?.data?.msg || error.message || '服务器错误',
      data: null
    };
  }
};

/**
 * 平台链接相关API接口
 */
const platformLinksApi = {
  /**
   * 获取所有购买平台链接
   * @returns {Promise} 平台链接列表
   */
  getOPlatformLinks() {
    return handleApiResponse(() => apiClient.get('/omg-platform-links/getOPlatformLinks'));
  }
};

export default platformLinksApi; 