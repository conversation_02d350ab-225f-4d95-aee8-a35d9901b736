import inviteCodeApi from '../api/inviteCode'

/**
 * 邀请码工具类
 * 提供邀请码相关的工具函数
 */
export class InviteCodeUtils {
  static STORAGE_KEY = 'omg_invite_code'
  static VISITED_KEY = 'omg_invite_visited'
  static EXPIRE_DAYS = 7 // 邀请码有效期7天

  /**
   * 从URL中获取邀请码参数
   * @returns {string|null} 邀请码或null
   */
  static getInviteCodeFromUrl() {
    const urlParams = new URLSearchParams(window.location.search)
    return urlParams.get('invite_code') || urlParams.get('inviteCode')
  }

  /**
   * 验证邀请码格式
   * @param {string} inviteCode 邀请码
   * @returns {boolean} 是否有效
   */
  static validateInviteCodeFormat(inviteCode) {
    if (!inviteCode || typeof inviteCode !== 'string') {
      return false
    }
    // 基本格式验证：长度3-50，只包含字母数字
    return /^[A-Za-z0-9]{3,50}$/.test(inviteCode)
  }

  /**
   * 验证邀请码有效性（调用后端API）
   * @param {string} inviteCode 邀请码
   * @returns {Promise<boolean>} 是否有效
   */
  static async validateInviteCode(inviteCode) {
    try {
      const response = await inviteCodeApi.validateInviteCode(inviteCode)
      console.log('邀请码验证API响应:', response)

      // 兼容多种响应格式
      if (response.data) {
        // 格式1: { data: { success: true } }
        if (response.data.success === true) return true
        // 格式2: { data: { code: 200 } }
        if (response.data.code === 200) return true
        // 格式3: { data: true }
        if (response.data === true) return true
      }

      // 格式4: { success: true }
      if (response.success === true) return true
      // 格式5: { code: 200 }
      if (response.code === 200) return true

      console.warn('邀请码验证失败，响应格式:', response)
      return false
    } catch (error) {
      console.error('验证邀请码失败:', error)
      return false
    }
  }

  /**
   * 记录访问统计
   * @param {string} inviteCode 邀请码
   * @returns {Promise<boolean>} 是否记录成功
   */
  static async recordVisit(inviteCode) {
    try {
      // 检查是否已经记录过访问（防重复）
      const visitKey = `${this.VISITED_KEY}_${inviteCode}`
      const lastVisit = localStorage.getItem(visitKey)
      const now = Date.now()

      // 10分钟内不重复记录
      if (lastVisit && (now - parseInt(lastVisit)) < 10 * 60 * 1000) {
        console.log('10分钟内已记录访问，跳过重复统计')
        return true
      }

      const response = await inviteCodeApi.recordVisit(inviteCode)

      if (response.data && response.data.success) {
        console.log('邀请码访问记录成功')
        // 记录访问时间，防止重复统计
        localStorage.setItem(visitKey, now.toString())
        return true
      } else {
        console.warn('邀请码访问记录失败:', response.data?.message)
        return false
      }
    } catch (error) {
      console.error('记录邀请码访问失败:', error)
      return false
    }
  }

  /**
   * 存储邀请码到本地存储
   * @param {string} inviteCode 邀请码
   */
  static storeInviteCode(inviteCode) {
    try {
      const inviteData = {
        code: inviteCode,
        timestamp: Date.now(),
        url: window.location.href
      }
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(inviteData))
      console.log('邀请码已存储:', inviteCode)
    } catch (error) {
      console.error('存储邀请码失败:', error)
    }
  }

  /**
   * 获取存储的邀请码
   * @returns {string|null} 邀请码或null
   */
  static getStoredInviteCode() {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        const inviteData = JSON.parse(stored)
        // 检查是否过期
        const expireTime = this.EXPIRE_DAYS * 24 * 60 * 60 * 1000
        if (Date.now() - inviteData.timestamp < expireTime) {
          return inviteData.code
        } else {
          // 过期则清除
          this.clearStoredInviteCode()
        }
      }
      return null
    } catch (error) {
      console.error('获取存储的邀请码失败:', error)
      return null
    }
  }

  /**
   * 清除存储的邀请码
   */
  static clearStoredInviteCode() {
    try {
      localStorage.removeItem(this.STORAGE_KEY)
      console.log('已清除存储的邀请码')
    } catch (error) {
      console.error('清除邀请码失败:', error)
    }
  }

  /**
   * 清理URL中的邀请码参数
   */
  static cleanUrlParams() {
    try {
      const url = new URL(window.location)
      url.searchParams.delete('invite_code')
      url.searchParams.delete('inviteCode')

      // 使用 replaceState 避免页面刷新
      window.history.replaceState({}, document.title, url.toString())
    } catch (error) {
      console.error('清理URL参数失败:', error)
    }
  }

  /**
   * 处理邀请码完整流程
   * @param {string} inviteCode 邀请码
   * @returns {Promise<boolean>} 处理是否成功
   */
  static async processInviteCode(inviteCode) {
    try {
      // 验证邀请码格式
      if (!this.validateInviteCodeFormat(inviteCode)) {
        console.warn('邀请码格式无效:', inviteCode)
        return false
      }

      // 验证邀请码有效性
      const isValid = await this.validateInviteCode(inviteCode)
      if (!isValid) {
        console.warn('邀请码无效或已失效:', inviteCode)
        return false
      }

      // 存储邀请码
      this.storeInviteCode(inviteCode)

      // 记录访问
      await this.recordVisit(inviteCode)

      // 清理URL参数（可选）
      this.cleanUrlParams()

      return true
    } catch (error) {
      console.error('处理邀请码失败:', error)
      return false
    }
  }

  /**
   * 记录注册统计
   * @param {string} inviteCode 邀请码
   * @param {number} userId 用户ID
   * @returns {Promise<boolean>} 是否记录成功
   */
  static async recordRegistration(inviteCode, userId) {
    try {
      const response = await inviteCodeApi.recordRegistration(inviteCode, userId)

      if (response.data && response.data.success) {
        console.log('邀请码注册记录成功')
        return true
      } else {
        console.warn('邀请码注册记录失败:', response.data?.message)
        return false
      }
    } catch (error) {
      console.error('记录邀请码注册失败:', error)
      return false
    }
  }
}

/**
 * Vue组合式函数：使用邀请码
 * @returns {Object} 邀请码相关的方法和状态
 */
export function useInviteCode() {
  const processUrlInviteCode = async () => {
    const inviteCode = InviteCodeUtils.getInviteCodeFromUrl()
    if (inviteCode) {
      console.log('检测到邀请码:', inviteCode)
      return await InviteCodeUtils.processInviteCode(inviteCode)
    }
    return false
  }

  const getStoredInviteCode = () => {
    return InviteCodeUtils.getStoredInviteCode()
  }

  const recordRegistration = async (userId) => {
    const inviteCode = InviteCodeUtils.getStoredInviteCode()

    console.log('📝 记录邀请码注册统计:');
    console.log('- 邀请码:', inviteCode);
    console.log('- 用户ID:', userId);

    if (!inviteCode) {
      console.log('❌ 没有找到邀请码，无法记录注册统计');
      return false
    }

    if (!userId) {
      console.log('❌ 没有用户ID，无法记录注册统计');
      return false
    }

    console.log('✅ 邀请码和用户ID都存在，开始记录注册统计');
    return await InviteCodeUtils.recordRegistration(inviteCode, userId)
  }

  const clearInviteCode = () => {
    InviteCodeUtils.clearStoredInviteCode()
  }

  const hasValidInviteCode = () => {
    const storedInviteCode = InviteCodeUtils.getStoredInviteCode()

    // 也检查URL中的邀请码作为备选
    const urlParams = new URLSearchParams(window.location.search)
    const urlInviteCode = urlParams.get('invite_code') || urlParams.get('inviteCode')

    const inviteCode = storedInviteCode || urlInviteCode

    console.log('🔍 检查邀请码有效性:');
    console.log('- 存储的邀请码:', storedInviteCode);
    console.log('- URL中的邀请码:', urlInviteCode);
    console.log('- 最终邀请码:', inviteCode);
    console.log('- 是否有效:', !!inviteCode);

    return !!inviteCode
  }

  return {
    processUrlInviteCode,
    getStoredInviteCode,
    recordRegistration,
    clearInviteCode,
    hasValidInviteCode
  }
}
