<template>
  <div class="howto-page cosmic-bg" ref="howtoPage">
    <div class="howto-header">
      <el-row justify="center">
        <el-col :span="16">
          <h1>{{ $t('howTo.title') }}</h1>
          <p>{{ $t('howTo.subtitle') }}</p>
          
          <!-- 教程类型切换按钮 -->
          <div class="tutorial-type-switcher">
            <el-radio-group v-model="tutorialType" size="large">
              <el-radio-button label="image">{{ $t('howTo.imageTutorial') }}</el-radio-button>
              <el-radio-button label="video">{{ $t('howTo.videoTutorial') }}</el-radio-button>
            </el-radio-group>
          </div>
          
          <el-progress 
            :percentage="currentProgress" 
            :format="format" 
            class="progress-indicator"
            stroke-width="8"
            :color="customColors"
          />
        </el-col>
      </el-row>
    </div>

    <!-- 图文教程 -->
    <div v-if="tutorialType === 'image'" class="steps-container">
      <el-timeline>
        <!-- Step 1 -->
        <el-timeline-item
          :timestamp="$t('howTo.step') + ' 1'"
          placement="top"
          :hollow="true"
          type="success"
          size="large"
          ref="step1"
        >
          <el-card class="step-card">
            <template #header>
              <div class="step-header">
                <div class="step-number">
                  <el-tag type="success" size="large" effect="dark">1</el-tag>
                </div>
                <h2>{{ $t('howTo.step1Title') }}</h2>
              </div>
            </template>
            <div class="step-content">
              <p>{{ $t('howTo.step1Content') }}</p>
              <div class="step-image">
                <el-image 
                  :src="stepImages[0]"
                  fit="cover"
                  :preview-src-list="stepImages"
                  :initial-index="0"
                  hide-on-click-modal
                >
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
              <el-alert
                :title="$t('howTo.tip')"
                type="success"
                :description="$t('howTo.tipContent')"
                :closable="false"
                show-icon
                class="step-tip"
              />
            </div>
          </el-card>
        </el-timeline-item>

        <!-- Step 2 -->
        <el-timeline-item
          :timestamp="$t('howTo.step') + ' 2'"
          placement="top"
          :hollow="true"
          type="success"
          size="large"
          ref="step2"
        >
          <el-card class="step-card">
            <template #header>
              <div class="step-header">
                <div class="step-number">
                  <el-tag type="success" size="large" effect="dark">2</el-tag>
                </div>
                <h2>{{ $t('howTo.step2Title') }}</h2>
              </div>
            </template>
            <div class="step-content">
              <p>{{ $t('howTo.step2Content') }}</p>
              <div class="step-image">
                <el-image 
                  :src="stepImages[1]"
                  fit="cover"
                  :preview-src-list="stepImages"
                  :initial-index="1"
                  hide-on-click-modal
                >
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </div>
          </el-card>
        </el-timeline-item>

        <!-- Step 3 -->
        <el-timeline-item
          timestamp="Step 3"
          placement="top"
          :hollow="true"
          type="success"
          size="large"
          ref="step3"
        >
          <el-card class="step-card">
            <template #header>
              <div class="step-header">
                <div class="step-number">
                  <el-tag type="success" size="large" effect="dark">3</el-tag>
                </div>
                <h2>Select Platform</h2>
              </div>
            </template>
            <div class="step-content">
              <p>Choose one of our recommended purchasing platforms to complete your order.</p>
              <div class="step-image">
                <el-image 
                  :src="stepImages[2]"
                  fit="cover"
                  :preview-src-list="stepImages"
                  :initial-index="2"
                  hide-on-click-modal
                >
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </div>
          </el-card>
        </el-timeline-item>

        <!-- Step 4 -->
        <el-timeline-item
          timestamp="Step 4"
          placement="top"
          :hollow="true"
          type="success"
          size="large"
          ref="step4"
        >
          <el-card class="step-card">
            <template #header>
              <div class="step-header">
                <div class="step-number">
                  <el-tag type="success" size="large" effect="dark">4</el-tag>
                </div>
                <h2>Choose Style and Size</h2>
              </div>
            </template>
            <div class="step-content">
              <p>On the platform's checkout page, select your preferred style and size, then proceed to payment.</p>
              <div class="step-image">
                <el-image 
                  :src="stepImages[3]"
                  fit="cover"
                  :preview-src-list="stepImages"
                  :initial-index="3"
                  hide-on-click-modal
                >
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </div>
          </el-card>
        </el-timeline-item>

        <!-- Step 5 -->
        <el-timeline-item
          timestamp="Step 5"
          placement="top"
          :hollow="true"
          type="success"
          size="large"
          ref="step5"
        >
          <el-card class="step-card">
            <template #header>
              <div class="step-header">
                <div class="step-number">
                  <el-tag type="success" size="large" effect="dark">5</el-tag>
                </div>
                <h2>Complete Payment</h2>
              </div>
            </template>
            <div class="step-content">
              <p>Review your order and complete the payment process.</p>
              <div class="step-image">
                <el-image 
                  :src="stepImages[4]"
                  fit="cover"
                  :preview-src-list="stepImages"
                  :initial-index="4"
                  hide-on-click-modal
                >
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </div>
          </el-card>
        </el-timeline-item>

        <!-- Step 6 -->
        <el-timeline-item
          timestamp="Step 6"
          placement="top"
          :hollow="true"
          type="success"
          size="large"
          ref="step6"
        >
          <el-card class="step-card">
            <template #header>
              <div class="step-header">
                <div class="step-number">
                  <el-tag type="success" size="large" effect="dark">6</el-tag>
                </div>
                <h2>Wait for QC and Shipping</h2>
              </div>
            </template>
            <div class="step-content">
              <p>After receiving the QC photos, confirm your shipping address, choose shipping method, make payment, and wait for your package to arrive.</p>
              <div class="step-image">
                <el-image 
                  :src="stepImages[5]"
                  fit="cover"
                  :preview-src-list="stepImages"
                  :initial-index="5"
                  hide-on-click-modal
                >
                  <template #placeholder>
                    <div class="image-placeholder">
                      <el-icon><Picture /></el-icon>
                    </div>
                  </template>
                </el-image>
              </div>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 视频教程 -->
    <div v-else class="video-tutorial">
      <div class="video-container">
        <h2>Video Tutorial</h2>
        <p>Watch our comprehensive video guide on how to make purchases</p>
        <div class="video-player">
          <img src="https://via.placeholder.com/1200x675?text=Video+Tutorial" alt="Video Tutorial" class="video-thumbnail" />
          <div class="play-button">
            <el-icon><VideoPlay /></el-icon>
          </div>
        </div>
        <div class="video-description">
          <h3>Complete Guide to Purchasing</h3>
          <p>This video covers all steps from choosing a product to receiving your package.</p>
          <ul>
            <li>Product selection tips</li>
            <li>Quality control process</li>
            <li>Platform selection guide</li>
            <li>Payment methods</li>
            <li>Shipping options</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { 
  ElImage, 
  ElTimeline, 
  ElTimelineItem,
  ElCard,
  ElTag,
  ElAlert,
  ElIcon,
  ElProgress,
  ElRow,
  ElCol,
  ElRadioGroup,
  ElRadioButton
} from 'element-plus'
import { 
  Picture,
  VideoPlay
} from '@element-plus/icons-vue'
import { ref, onMounted, onBeforeUnmount, computed } from 'vue'

export default {
  name: 'HowToView',
  components: {
    ElImage,
    ElTimeline,
    ElTimelineItem,
    ElCard,
    ElTag,
    ElAlert,
    ElIcon,
    ElProgress,
    ElRow,
    ElCol,
    ElRadioGroup,
    ElRadioButton,
    Picture,
    VideoPlay
  },
  setup() {
    const howtoPage = ref(null)
    const step1 = ref(null)
    const step2 = ref(null)
    const step3 = ref(null)
    const step4 = ref(null)
    const step5 = ref(null)
    const step6 = ref(null)
    
    const currentStep = ref(1)
    const tutorialType = ref('image')

    const customColors = [
      { color: '#7b88ff', percentage: 20 },
      { color: '#8e7aff', percentage: 40 },
      { color: '#a080ff', percentage: 60 },
      { color: '#b485ff', percentage: 80 },
      { color: '#c4a4ff', percentage: 100 }
    ]

    const currentProgress = computed(() => {
      return currentStep.value * 16.67
    })

    const format = (percentage) => {
      return `Step ${Math.ceil(percentage / 16.67)} of 6`
    }

    const stepImages = [
      'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/6%E6%9C%8827%E6%97%A5.gif',
      'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/6%E6%9C%8827%E6%97%A5.gif',
      'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/6%E6%9C%8827%E6%97%A5.gif',
      'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/6%E6%9C%8827%E6%97%A5.gif',
      'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/6%E6%9C%8827%E6%97%A5.gif',
      'https://agtfind.oss-eu-central-1.aliyuncs.com/ylkj-uploads/6%E6%9C%8827%E6%97%A5.gif'
    ]

    // 监听滚动更新当前步骤
    const handleScroll = () => {
      if (tutorialType.value !== 'image') return
      
      const stepRefs = [step1.value, step2.value, step3.value, step4.value, step5.value, step6.value]
      const scrollPosition = window.scrollY + window.innerHeight / 3
      
      let visibleStep = 1
      for (let i = 0; i < stepRefs.length; i++) {
        if (!stepRefs[i]) continue
        const el = stepRefs[i].$el
        if (!el) continue
        
        const rect = el.getBoundingClientRect()
        const topPosition = rect.top + window.scrollY
        
        if (scrollPosition >= topPosition) {
          visibleStep = i + 1
        }
      }
      
      currentStep.value = visibleStep
    }

    onMounted(() => {
      window.addEventListener('scroll', handleScroll)
      handleScroll() // 初始化
    })

    onBeforeUnmount(() => {
      window.removeEventListener('scroll', handleScroll)
    })

    return {
      customColors,
      format,
      stepImages,
      howtoPage,
      step1,
      step2,
      step3,
      step4,
      step5,
      step6,
      currentStep,
      currentProgress,
      tutorialType
    }
  }
}
</script>

<style scoped>
/* 宇宙主题背景 */
.cosmic-bg {
  background-color: #1e0940;
  background-image: linear-gradient(125deg, #1e0940 0%, #380d6d 50%, #1e0940 100%);
  color: #fff;
  min-height: 100vh;
  padding: 2rem 5rem;
  position: relative;
  overflow: hidden;
}

.howto-header {
  text-align: center;
  margin: 0 auto 4rem;
  max-width: 800px;
  position: relative;
  z-index: 10;
}

/* 宇宙风格标题 */
.title-container {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
}

.cosmic-title {
  color: white;
  text-align: center;
  font-size: 2.5rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin: 0;
  padding: 0.5rem 3rem;
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border-radius: 50px;
  display: inline-block;
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.5);
  position: relative;
  z-index: 1;
  border: 2px solid rgba(195, 163, 255, 0.5);
  animation: titleGlow 3s infinite alternate;
}

.cosmic-title::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(135deg, rgba(120, 70, 200, 0.7), rgba(187, 134, 252, 0.7));
  border-radius: 50px;
  z-index: -1;
  opacity: 0.5;
  filter: blur(8px);
}

.subtitle {
  font-size: 1.2rem;
  color: #bb86fc;
  margin-bottom: 2rem;
  text-shadow: 0 0 10px rgba(187, 134, 252, 0.5);
}

/* 切换按钮样式 - 宇宙主题 */
.tutorial-type-switcher {
  margin: 2rem 0;
}

:deep(.el-radio-group) {
  --el-radio-text-color: #bb86fc;
}

:deep(.el-radio-button__inner) {
  background: rgba(29, 23, 51, 0.8);
  color: #bb86fc;
  border-color: #6a42c1;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  z-index: 1;
}

:deep(.el-radio-button:first-child .el-radio-button__inner) {
  border-left-color: #6a42c1;
}

:deep(.el-radio-button__original-radio:checked+.el-radio-button__inner) {
  background: linear-gradient(135deg, #4b2c99, #6a42c1);
  border-color: #6a42c1;
  box-shadow: -1px 0 0 0 #6a42c1;
  color: white;
}

:deep(.el-radio-button__inner:hover) {
  color: white;
  background: linear-gradient(135deg, #3a225f, #4b2c99);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

:deep(.el-radio-button__inner)::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #bb86fc, #3a1f79);
  z-index: -1;
  filter: blur(10px);
  opacity: 0;
  transition: opacity 0.3s;
}

:deep(.el-radio-button__inner:hover)::before,
:deep(.el-radio-button__original-radio:checked+.el-radio-button__inner)::before {
  opacity: 1;
}

:deep(.el-radio-button:not(:first-child)::before) {
  background-color: #6a42c1;
}

/* 宇宙主题进度条样式 */
.progress-indicator {
  margin-top: 2rem;
  position: relative;
  padding: 10px 0;
}

.progress-indicator::before {
  content: '';
  position: absolute;
  top: -10px;
  left: -20px;
  right: -20px;
  bottom: -10px;
  background: radial-gradient(ellipse at center, rgba(123, 136, 255, 0.1) 0%, transparent 70%);
  border-radius: 20px;
  z-index: -1;
  filter: blur(10px);
}

:deep(.el-progress-bar__outer) {
  background-color: rgba(20, 15, 45, 0.4) !important;
  border-radius: 12px !important;
  height: 12px !important;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5), 0 0 20px rgba(123, 136, 255, 0.2) !important;
  border: 1px solid rgba(123, 136, 255, 0.2) !important;
  overflow: hidden;
  backdrop-filter: blur(5px);
}

:deep(.el-progress-bar__inner) {
  border-radius: 12px !important;
  position: relative;
  overflow: hidden;
  background: linear-gradient(90deg, #7b88ff, #a080ff, #c4a4ff) !important;
  box-shadow: 0 0 15px rgba(123, 136, 255, 0.6) !important;
  animation: cosmicPulse 3s infinite alternate;
}

:deep(.el-progress-bar__inner)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 50%, rgba(255, 255, 255, 0.4) 0%, transparent 50%);
  z-index: 2;
}

:deep(.el-progress-bar__inner)::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.7), transparent);
  animation: shimmer 2.5s infinite ease-in-out;
  z-index: 1;
}

:deep(.el-progress__text) {
  background: linear-gradient(135deg, #a080ff 10%, #7b88ff 45%, #c4a4ff 90%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent !important;
  font-weight: bold;
  font-size: 1.1rem !important;
  letter-spacing: 0.5px;
  text-shadow: 0 0 10px rgba(123, 136, 255, 0.6);
  margin-left: 8px;
  position: relative;
  transform: translateZ(0);
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes cosmicPulse {
  0% { opacity: 0.85; }
  100% { opacity: 1; }
}

@keyframes titleGlow {
  0% { box-shadow: 0 0 10px rgba(187, 134, 252, 0.5); }
  100% { box-shadow: 0 0 25px rgba(187, 134, 252, 0.8); }
}

.steps-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 3rem;
  position: relative;
  z-index: 1;
}

.step-card {
  display: flex;
  gap: 2rem;
  background: rgba(29, 23, 51, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(187, 134, 252, 0.3);
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.3);
}

.step-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(187, 134, 252, 0.1), rgba(120, 70, 200, 0.1));
  z-index: -1;
}

.step-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(187, 134, 252, 0.4);
  border-color: rgba(187, 134, 252, 0.5);
  background: linear-gradient(135deg, rgba(41, 31, 77, 0.9), rgba(29, 23, 51, 0.9));
}

.step-number {
  width: 65px;
  height: 65px;
  background: linear-gradient(135deg, #7b88ff, #a080ff);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.8rem;
  font-weight: bold;
  flex-shrink: 0;
  color: white;
  box-shadow: 0 0 20px rgba(123, 136, 255, 0.6);
  position: relative;
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 2px solid rgba(255, 255, 255, 0.1);
  transform: translateZ(0);
}

.step-card:hover .step-number {
  transform: scale(1.05) translateZ(0);
  box-shadow: 0 0 30px rgba(123, 136, 255, 0.8);
}

.step-number::before {
  content: '';
  position: absolute;
  inset: 0;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
  border-radius: 50%;
  opacity: 0.6;
  z-index: 1;
}

.step-number::after {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  border-radius: 50%;
  background: linear-gradient(135deg, #7b88ff, #a080ff);
  z-index: -1;
  opacity: 0.6;
  filter: blur(8px);
  animation: pulseGlow 3s infinite alternate;
}

@keyframes pulseGlow {
  0% { opacity: 0.4; filter: blur(8px); }
  100% { opacity: 0.8; filter: blur(12px); }
}

.step-content {
  flex: 1;
}

.step-content h2 {
  background: linear-gradient(135deg, #a080ff 10%, #7b88ff 45%, #c4a4ff 90%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 1rem;
  font-size: 1.5rem;
  text-shadow: 0 2px 15px rgba(160, 128, 255, 0.3);
  font-weight: 600;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
}

.step-content p {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(225, 225, 252, 0.95) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  margin-bottom: 1.5rem;
  line-height: 1.7;
  font-size: 1.05rem;
  letter-spacing: 0.3px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  font-weight: 400;
  position: relative;
  padding-left: 5px;
  opacity: 0.9;
  transition: all 0.3s ease;
}

.step-card:hover .step-content p {
  opacity: 1;
  transform: translateY(-1px);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.4);
}

.step-image {
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  border: 1px solid rgba(123, 136, 255, 0.4);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3), 0 0 15px rgba(123, 136, 255, 0.2);
  position: relative;
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform: translateZ(0);
}

.step-card:hover .step-image {
  transform: translateY(-3px) translateZ(0);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4), 0 0 20px rgba(123, 136, 255, 0.3);
  border-color: rgba(123, 136, 255, 0.6);
}

.step-image::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(45deg, #7b88ff, #a080ff);
  z-index: -1;
  border-radius: 15px;
  filter: blur(10px);
  opacity: 0.6;
  transition: all 0.5s ease;
}

.step-card:hover .step-image::before {
  opacity: 0.8;
  filter: blur(15px);
}

.step-image :deep(.el-image) {
  width: 100%;
  height: 400px;
  transition: all 0.5s ease;
  position: relative;
  z-index: 1;
}

.step-card:hover .step-image :deep(.el-image) {
  transform: scale(1.03);
  filter: brightness(1.1);
}

.help-section,
.help-header,
.help-actions,
.faq-question,
.faq-answer {
  display: none;
}

.step-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  border-bottom: 1px solid rgba(187, 134, 252, 0.3);
  padding-bottom: 1rem;
  margin-bottom: 1.5rem;
}

.step-header h2 {
  background: linear-gradient(135deg, #a080ff 10%, #7b88ff 45%, #c4a4ff 90%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 15px rgba(160, 128, 255, 0.3);
  font-weight: 600;
  letter-spacing: 0.5px;
  position: relative;
  margin: 0;
  padding: 5px 0;
  transition: all 0.3s ease;
}

.step-card:hover .step-header h2 {
  text-shadow: 0 2px 20px rgba(160, 128, 255, 0.5);
  transform: translateY(-1px);
}

.image-placeholder {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 400px;
  background: rgba(41, 31, 77, 0.5);
  color: #bb86fc;
  position: relative;
  overflow: hidden;
}

.image-placeholder::after {
  content: '';
  position: absolute;
  width: 200%;
  height: 200%;
  top: -50%;
  left: -50%;
  background: radial-gradient(circle, rgba(187, 134, 252, 0.1) 1px, transparent 6px);
  background-size: 30px 30px;
  animation: rotate 120s linear infinite;
}

.image-placeholder .el-icon {
  font-size: 3rem;
  filter: drop-shadow(0 0 10px rgba(187, 134, 252, 0.5));
}

.step-tip {
  margin-top: 1.5rem;
  background: linear-gradient(135deg, rgba(41, 31, 77, 0.7), rgba(53, 38, 103, 0.7)) !important;
  border: 1px solid rgba(187, 134, 252, 0.4) !important;
  color: #e1e1fc !important;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 0 0 10px rgba(123, 136, 255, 0.2) !important;
  border-radius: 8px !important;
  backdrop-filter: blur(8px) !important;
  transition: all 0.3s ease;
}

.step-card:hover .step-tip {
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.25), 0 0 15px rgba(123, 136, 255, 0.25) !important;
  border-color: rgba(187, 134, 252, 0.5) !important;
  transform: translateY(-2px);
}

.step-tip :deep(.el-alert__title) {
  background: linear-gradient(135deg, #a080ff 10%, #7b88ff 90%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent !important;
  font-weight: bold;
  text-shadow: 0 1px 5px rgba(160, 128, 255, 0.4);
  letter-spacing: 0.5px;
  font-size: 1.05rem;
}

.step-tip :deep(.el-alert__description) {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(225, 225, 252, 0.95) 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent !important;
  line-height: 1.6;
  letter-spacing: 0.3px;
  padding-top: 3px;
  font-size: 1rem;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 视频教程样式 - 宇宙主题 */
.video-tutorial {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 0;
  position: relative;
  z-index: 1;
}

.video-container {
  background: rgba(29, 23, 51, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  box-shadow: 0 8px 25px rgba(120, 70, 200, 0.3);
  border: 1px solid rgba(187, 134, 252, 0.3);
  position: relative;
  overflow: hidden;
}

.video-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(187, 134, 252, 0.1), rgba(120, 70, 200, 0.1));
  z-index: -1;
}

.video-container h2 {
  color: #bb86fc;
  margin-bottom: 1rem;
  font-size: 1.8rem;
  text-align: center;
  text-shadow: 0 0 10px rgba(187, 134, 252, 0.5);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.video-container > p {
  color: #e1e1fc;
  margin-bottom: 2rem;
  text-align: center;
}

.video-player {
  position: relative;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 2rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: all 0.5s ease;
  border: 1px solid rgba(187, 134, 252, 0.3);
}

.video-player::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #bb86fc, #3a1f79);
  z-index: -1;
  border-radius: 14px;
  filter: blur(8px);
  opacity: 0.7;
}

.video-player:hover {
  transform: translateY(-5px) scale(1.01);
  box-shadow: 0 15px 40px rgba(120, 70, 200, 0.4);
}

.video-thumbnail {
  width: 100%;
  display: block;
  transition: all 0.5s ease;
  filter: brightness(0.8) contrast(1.1);
}

.video-player:hover .video-thumbnail {
  filter: brightness(0.9) contrast(1.2);
  transform: scale(1.05);
}

.play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #6a42c1, #3a1f79);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2.5rem;
  box-shadow: 0 0 25px rgba(187, 134, 252, 0.6);
  transition: all 0.5s ease;
  z-index: 2;
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.play-button::before {
  content: '';
  position: absolute;
  top: -4px;
  left: -4px;
  right: -4px;
  bottom: -4px;
  background: linear-gradient(135deg, rgba(187, 134, 252, 0.8), rgba(120, 70, 200, 0.8));
  border-radius: 50%;
  z-index: -1;
  filter: blur(8px);
  opacity: 0.7;
}

.video-player:hover .play-button {
  background: linear-gradient(135deg, #7b4fe6, #4b2c99);
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 0 30px rgba(187, 134, 252, 0.8);
}

.video-description {
  background: rgba(41, 31, 77, 0.7);
  padding: 2rem;
  border-radius: 12px;
  border: 1px solid rgba(187, 134, 252, 0.3);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.video-description h3 {
  color: #bb86fc;
  margin-bottom: 1rem;
  font-size: 1.4rem;
  text-shadow: 0 0 10px rgba(187, 134, 252, 0.5);
}

.video-description p {
  color: #e1e1fc;
  margin-bottom: 1rem;
  line-height: 1.6;
}

.video-description ul {
  color: #e1e1fc;
  padding-left: 1.5rem;
}

.video-description li {
  margin-bottom: 0.8rem;
  position: relative;
}

.video-description li::before {
  content: '✧';
  position: absolute;
  left: -1.2rem;
  color: #bb86fc;
  text-shadow: 0 0 5px rgba(187, 134, 252, 0.5);
}

/* 时间轴样式 */
:deep(.el-timeline-item__node--normal) {
  background-color: #6a42c1;
  border: 2px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 0 15px rgba(187, 134, 252, 0.5);
}

:deep(.el-timeline-item__tail) {
  border-left-color: rgba(187, 134, 252, 0.5);
  border-left-width: 2px;
}

:deep(.el-card) {
  border: 1px solid rgba(187, 134, 252, 0.3);
  background: rgba(29, 23, 51, 0.8);
  box-shadow: 0 8px 25px rgba(120, 70, 200, 0.3);
  backdrop-filter: blur(10px);
}

:deep(.el-card__header) {
  border-bottom: 1px solid rgba(187, 134, 252, 0.3);
  background: linear-gradient(135deg, rgba(41, 31, 77, 0.9), rgba(29, 23, 51, 0.9));
}

:deep(.el-tag--success) {
  background-color: #6a42c1;
  border-color: #6a42c1;
  color: white;
}

:deep(.el-timeline-item__timestamp) {
  color: #bb86fc;
  font-weight: 600;
  text-shadow: 0 0 10px rgba(187, 134, 252, 0.5);
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .cosmic-bg {
    padding: 2rem;
  }

  .step-card {
    flex-direction: column;
  }

  .step-number {
    margin-bottom: 1rem;
  }
  
  .cosmic-title {
    font-size: 2.2rem;
    padding: 0.5rem 2.5rem;
  }
}

@media (max-width: 768px) {
  .cosmic-bg {
    padding: 1rem;
    background-size: 275px 275px, 175px 175px, 125px 125px, 75px 75px;
  }

  .howto-header {
    padding: 1rem 0;
    margin-bottom: 2rem;
  }

  .cosmic-title {
    font-size: 1.8rem;
    padding: 0.5rem 2rem;
  }

  .subtitle {
    font-size: 1rem;
  }
  
  .step-image :deep(.el-image) {
    height: 250px;
  }
  
  .image-placeholder {
    height: 250px;
  }
  
  .video-player {
    margin-bottom: 1.5rem;
  }
  
  .play-button {
    width: 60px;
    height: 60px;
    font-size: 2rem;
  }
  
  :deep(.el-timeline-item__timestamp) {
    font-size: 1rem;
  }
  
  .step-content h2, .video-container h2 {
    font-size: 1.4rem;
  }

  :deep(.el-timeline-item__wrapper) {
    padding-left: 18px;
  }

  :deep(.el-timeline-item__node) {
    left: 0;
  }

  :deep(.el-timeline-item__tail) {
    left: 4px;
  }

  :deep(.el-timeline-item__timestamp) {
    font-size: 12px;
  }

  .step-card {
    padding: 1rem;
  }

  .step-header {
    gap: 0.5rem;
  }

  .step-header h2 {
    font-size: 1.2rem;
  }

  .step-content p {
    font-size: 0.9rem;
  }
  }

@media (max-width: 480px) {
  .step-image :deep(.el-image) {
    height: 200px;
  }

  .step-tip {
    margin-top: 0.5rem;
  }
  
  .play-button {
    width: 60px;
    height: 60px;
    font-size: 1.8rem;
  }
  
  .video-description {
    padding: 1.5rem;
  }
  
  .video-description h3 {
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .howto-page {
    padding: 0.8rem;
  }

  .howto-header h1 {
    font-size: 1.5rem;
  }

  .howto-header p {
    font-size: 0.9rem;
  }

  :deep(.el-progress) {
    margin-top: 1rem;
  }

  :deep(.el-card__header) {
    padding: 10px;
  }

  :deep(.el-card__body) {
    padding: 10px;
  }

  .step-header h2 {
    font-size: 1.1rem;
  }

  .step-content p {
    font-size: 0.85rem;
    margin-bottom: 1rem;
  }

  .step-image :deep(.el-image) {
    height: 180px;
  }

  :deep(.el-timeline-item__content) {
    font-size: 0.9rem;
  }
  
  .play-button {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }
}

@media (hover: none) {
  :deep(.el-image:hover) {
    transform: none;
  }

  :deep(.el-card:hover) {
    transform: none;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
  
  .video-player:hover {
    transform: none;
  }
  
  .video-player:hover .play-button {
    transform: translate(-50%, -50%);
  }
}
</style> 