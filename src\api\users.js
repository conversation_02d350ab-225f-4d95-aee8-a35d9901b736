import apiClient from '@/services/api';

// 用户相关API
// 直接导出具名函数，使其与import { loginApi } from '@/api/users' 兼容
export function getTableData() {
  return apiClient.get('/api/home/<USER>');
}

// 用户登录 - 改为使用邮箱登录
export function loginApi(params) {
  // 确保使用email而不是username作为参数
  const loginParams = {
    email: params.email,
    password: params.password,
    rememberMe: params.rememberMe
  };

  return apiClient.get('/users/doLogin', {
    params: loginParams
  });
}

// 发送用户登录邮箱验证码 - 简化登录流程，不再需要这个函数，但保留它以避免其他代码可能的引用错误
export function sendLoginVerificationCode(email) {
  console.warn('sendLoginVerificationCode has been deprecated in simplified login flow', { email });
  return Promise.resolve({ code: 200, message: 'This function is deprecated' });
}

// 发送用户注册邮箱验证码 - 简化注册流程，不再需要这个函数，但保留它以避免其他代码可能的引用错误
export function sendVerificationCode(email) {
  console.warn('sendVerificationCode has been deprecated in simplified registration flow', { email });
  return Promise.resolve({ code: 200, message: 'This function is deprecated' });
}

// 验证邮箱验证码 - 简化注册流程，不再需要这个函数，但保留它以避免其他代码可能的引用错误
export function verifyEmailCode(data) {
  console.warn('verifyEmailCode has been deprecated in simplified registration flow', { data });
  return Promise.resolve({ code: 200, message: 'This function is deprecated' });
}

// 用户注册 - 简化后只传递基本信息
export function register(data) {
  // 默认头像URL
  const defaultAvatarUrl = 'https://omgbuy.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/29/20250728-182903_compressed_20250729144751A001.png';

  // 发送基本用户信息：用户名、邮箱、密码和默认头像
  const registerParams = {
    username: data.username,
    email: data.email,
    password: data.password,
    avatarUrl: defaultAvatarUrl
  };

  // 直接调用注册接口，不再需要邮箱验证
  return apiClient.post('/users/register', registerParams);
}

// 发送邮件
export function sendEmail(data) {
  return apiClient.post('/users/sendEmail', data);
}

// 发送反馈邮件
export function sendFeedbackEmail(data) {
  return apiClient.post('/users/sendEmail/feedback', data);
}

// 发送营销邮件
export function sendMarketingEmail(data) {
  return apiClient.post('/users/sendEmail/marketing', data);
}

// 发送欢迎邮件
export function sendWelcomeEmail(userId) {
  return apiClient.post(`/users/${userId}/sendWelcome`);
}

// 发送订单确认邮件
export function sendOrderConfirmationEmail(orderId, data) {
  return apiClient.post(`/orders/${orderId}/sendConfirmation`, data);
}

// 发送密码重置邮件
export function sendPasswordResetEmail(email) {
  return apiClient.post('/users/resetPassword', { email });
}

// 示例1：使用params对象方式的GET请求（推荐）
export function getUsersByParams(roleId, status, keyword) {
  return apiClient.get('/users/list', {
    params: {
      roleId,
      status,
      keyword
    }
  });
}

// 示例2：分页查询用户列表
export function getUsersWithPagination(pageNum = 1, pageSize = 10, filters = {}) {
  return apiClient.get('/users/page', {
    params: {
      pageNum,
      pageSize,
      ...filters // 展开其他过滤条件，如角色、状态等
    }
  });
}

// 根据邮箱获取用户信息
// 获取用户信息（根据邮箱）
export function getUserByEmail(email) {
  return apiClient.get('/users/getEmailUser', {
    params: { email }
  });
}

// 更新用户信息
// 根据邮箱修改用户信息
export function changeUserInformation(userData) {
  console.log('调用changeUserInformation, 数据:', userData);
  return apiClient.post('/users/ChangeUserInformation', userData);
}
// 更新密码
export function updatePassword(data) {
  return apiClient.post('/users/changePassword', data);
}

// 上传头像
/**
 * 上传图片文件
 * @param {FormData} formData - 包含图片文件的FormData对象
 * @returns {Promise} 请求结果的Promise
 */
export function uploadImage(formData) {
  return apiClient.post('/users/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

// 保留默认导出以兼容可能的其他导入方式
export default {
  getTableData,
  loginApi,
  sendLoginVerificationCode, // 已弃用但保留引用
  sendVerificationCode, // 已弃用但保留引用
  verifyEmailCode, // 已弃用但保留引用
  register,
  sendEmail,
  sendFeedbackEmail,
  sendMarketingEmail,
  sendWelcomeEmail,
  sendOrderConfirmationEmail,
  sendPasswordResetEmail,
  getUsersByParams,
  getUsersWithPagination,
  getUserByEmail,
  changeUserInformation,
  updatePassword,
  uploadImage
};











