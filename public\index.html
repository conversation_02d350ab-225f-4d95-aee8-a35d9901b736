<!DOCTYPE html>
<html lang="">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0,viewport-fit=cover">
  <link rel="icon" href="<%= BASE_URL %>logo.png">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
  <!-- 添加 Material Design Icons 用于更多图标选择 -->
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.4.47/css/materialdesignicons.min.css">
  <title>
    <%= htmlWebpackPlugin.options.title %>
  </title>
  <style>
    /* 移除OMGBUY下方的紫色横线 */
    .nav-logo,
    .logo,
    .logo-container {
      border-bottom: none !important;
    }

    .nav-logo::after,
    .logo::after,
    .logo-container::after,
    .main-nav::after {
      display: none !important;
      content: none !important;
      border-bottom: none !important;
    }

    .main-nav {
      border-bottom: none !important;
    }

    /* 全局滚动条样式 */
    ::-webkit-scrollbar {
      width: 10px;
      height: 10px;
    }

    /* 浅色主题滚动条 - 淡紫色 #C197D1 */
    html[data-theme="light"] ::-webkit-scrollbar-track,
    ::-webkit-scrollbar-track {
      background: rgba(245, 240, 250, 0.8);
      border-radius: 5px;
    }

    html[data-theme="light"] ::-webkit-scrollbar-thumb,
    ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #C197D1, #A77CB4);
      border-radius: 5px;
      border: 2px solid rgba(245, 240, 250, 0.8);
      transition: all 0.3s ease;
    }

    html[data-theme="light"] ::-webkit-scrollbar-thumb:hover,
    ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #D2ACDF, #C197D1);
      box-shadow: 0 0 10px rgba(193, 151, 209, 0.5);
    }

    /* Firefox滚动条 - 浅色主题 */
    html[data-theme="light"],
    html {
      scrollbar-width: thin;
      scrollbar-color: #C197D1 rgba(245, 240, 250, 0.8);
    }

    /* 深色主题滚动条 - 明亮紫色 */
    html[data-theme="dark"] ::-webkit-scrollbar-track {
      background: rgba(30, 20, 40, 0.8);
    }

    html[data-theme="dark"] ::-webkit-scrollbar-thumb {
      background: linear-gradient(135deg, #a855f7, #8b5cf6);
      border: 2px solid rgba(30, 20, 40, 0.8);
    }

    html[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
      background: linear-gradient(135deg, #c084fc, #a78bfa);
      box-shadow: 0 0 10px rgba(168, 85, 247, 0.7);
    }

    html[data-theme="dark"] {
      scrollbar-color: #a855f7 rgba(30, 20, 40, 0.8);
    }

    /* 移动端滚动条 */
    @media (max-width: 768px) {
      ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
      }
    }
  </style>
</head>

<body>
  <noscript>
    <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled.
        Please enable it to continue.</strong>
  </noscript>
  <div id="app"></div>
  <!-- built files will be auto injected -->

  <!-- 邀请码处理脚本 -->
  <script>
    // 设置API基础URL供邀请码脚本使用
    window.VUE_APP_API_BASE_URL = '<%= process.env.VUE_APP_API_BASE_URL || "http://localhost:8080" %>';
  </script>
  <script src="/js/invite-code.js"></script>
  <script>
    // 确保滚动条样式能够正确应用
    function applyScrollbarTheme() {
      const darkMode = localStorage.getItem('darkMode') === 'true';
      document.documentElement.setAttribute('data-theme', darkMode ? 'dark' : 'light');
    }

    // 初始应用
    applyScrollbarTheme();

    // 监听主题变化
    window.addEventListener('storage', function (e) {
      if (e.key === 'darkMode') {
        applyScrollbarTheme();
      }
    });
  </script>
</body>

</html>