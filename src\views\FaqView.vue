<template>
  <div class="faq-page">
    <StarryBackground />
    <div class="faq-content-wrapper">
      <div class="faq-header">
        <h1>Frequently Asked Questions</h1>
        <p>Find answers to common questions about our services and platform</p>
      </div>

      <div class="faq-content">
        <div class="faq-list">
          <!-- 问题 1 -->
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq('leave-review')">
              <h3>What is OMGBUY?</h3>
              <span class="toggle-icon">{{ faqStates['leave-review'] ? '−' : '+' }}</span>
            </div>
            <div class="faq-answer" v-if="faqStates['leave-review']">
              <p>Ready to share the truth? Log in to leave your <strong>real review</strong> <span class="check-mark">✓</span>. Your <strong>valuable feedback</strong>—good or bad—directly helps fellow AGTFIND users discover the best products. Join us in building a transparent community!</p>
            </div>
          </div>


          <!-- 问题 2 -->
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq('file-complaint')">
              <h3>How do I file a complaint about a seller or product?</h3>
              <span class="toggle-icon">{{ faqStates['file-complaint'] ? '−' : '+' }}</span>
            </div>
            <div class="faq-answer" v-if="faqStates['file-complaint']">
              <p>If you receive a product with QC has obvious low quality or differences, Qing immediately contact our platform customer service, we will contact the merchant for you to return the product for a refund and off the shelves, to protect your rights and interests.</p>
              <div class="button-container">
                <a href="https://telegram.welcometopanda.com/get_next_link" target="_blank" class="faq-action-btn">Contact Now</a>
              </div>
            </div>
          </div>

          <!-- 问题 3 -->
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq('how-to-purchase')">
              <h3>How do I purchase products from the spreadsheet?</h3>
              <span class="toggle-icon">{{ faqStates['how-to-purchase'] ? '−' : '+' }}</span>
            </div>
            <div class="faq-answer" v-if="faqStates['how-to-purchase']">
              <p>We provide graphic tutorials and video tutorials to help you learn how to use the agent platform.</p>
              <div class="button-container">
                <router-link to="/how-to" class="faq-action-btn">View Tutorials</router-link>
              </div>
            </div>
          </div>

          <!-- 问题 4 -->
          <div class="faq-item">
            <div class="faq-question" @click="toggleFaq('whatsapp-support')">
              <h3>WhatsApp Customer Service</h3>
              <span class="toggle-icon">{{ faqStates['whatsapp-support'] ? '−' : '+' }}</span>
            </div>
            <div class="faq-answer" v-if="faqStates['whatsapp-support']">
              <p>Contact our WhatsApp customer service for immediate assistance.</p>
              <div class="button-container">
                <a href="https://telegram.welcometopanda.com/get_next_link" target="_blank" class="faq-action-btn">Contact Now</a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import StarryBackground from '@/components/StarryBackground.vue'

export default {
  name: 'FaqView',
  components: {
    StarryBackground
  },
  setup() {
    const faqStates = ref({
      'leave-review': false,
      'file-complaint': false,
      'how-to-purchase': false,
      'whatsapp-support': false
    })

    const toggleFaq = (id) => {
      faqStates.value[id] = !faqStates.value[id]
    }

    return {
      faqStates,
      toggleFaq
    }
  }
}
</script>

<style scoped>
.faq-page {
  position: relative;
  min-height: 100vh;
  color: #e0e0e0;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.faq-content-wrapper {
  position: relative;
  z-index: 10;
  padding: 2rem 5rem;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
}

.faq-header {
  text-align: center;
  margin-bottom: 2.5rem;
  margin-top: 2.5rem;
  z-index: 2;
}

.faq-header h1 {
  font-size: 2.8rem;
  color: #c3a3ff;
  margin-bottom: 1rem;
  text-shadow: 0 0 24px #c3a3ff99, 0 0 60px #a080ff44;
  font-weight: 800;
  letter-spacing: 2px;
}

.faq-header p {
  font-size: 1.15rem;
  color: #bdb6d6;
  max-width: 700px;
  margin: 0 auto;
  text-shadow: 0 0 8px #2d1e4a33;
}

.faq-content {
  width: 100%;
  display: flex;
  justify-content: center;
  padding: 2rem 0 4rem 0;
  z-index: 2;
}

.faq-list {
  width: 100%;
  max-width: 600px;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.faq-item {
  background: linear-gradient(135deg, rgba(41, 31, 77, 0.92), rgba(26, 26, 32, 0.92));
  backdrop-filter: blur(14px);
  border-radius: 22px;
  overflow: hidden;
  box-shadow: 0 8px 32px 0 #7b88ff33, 0 1.5px 8px #a080ff33;
  border: 1.5px solid #a080ff44;
  transition: box-shadow 0.3s, border-color 0.3s, transform 0.25s;
}

.faq-item:hover {
  transform: translateY(-4px) scale(1.015);
  box-shadow: 0 16px 40px #a080ff55, 0 0 32px #c3a3ff33;
  border-color: #bb86fc99;
}

.faq-question {
  background: linear-gradient(135deg, rgba(45, 45, 53, 0.97), rgba(34, 34, 42, 0.97));
  padding: 1.6rem 2rem 1.6rem 2rem;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(120, 70, 200, 0.18);
  user-select: none;
}

.faq-question h3 {
  color: #c3a3ff;
  margin: 0;
  font-size: 1.25rem;
  font-weight: 700;
  letter-spacing: 1px;
}

.toggle-icon {
  color: #c3a3ff;
  font-size: 2rem;
  font-weight: bold;
  transition: transform 0.3s cubic-bezier(.4,2,.6,1), color 0.3s;
  margin-left: 1.2rem;
}

.faq-item:hover .toggle-icon {
  color: #fff;
  text-shadow: 0 0 8px #bb86fc;
}

.faq-answer {
  padding: 0 2rem;
  background: rgba(35, 35, 40, 0.82);
  color: #e0e0e0;
  line-height: 1.7;
  backdrop-filter: blur(6px);
  max-height: 800px;
  opacity: 1;
  transition: max-height 0.45s cubic-bezier(.4,2,.6,1), opacity 0.35s;
  overflow: hidden;
  animation: fadeInFaq 0.5s;
}

@keyframes fadeInFaq {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: none; }
}

.faq-answer p {
  margin-top: 1.2rem;
  margin-bottom: 1.2rem;
}

.button-container {
  margin-top: 1.2rem;
  text-align: center;
  margin-bottom: 2rem;
}

.faq-action-btn {
  display: inline-block;
  background: linear-gradient(135deg, #7b88ff, #c4a4ff);
  color: #fff;
  border: none;
  border-radius: 30px;
  padding: 0.9rem 2.2rem;
  font-weight: 700;
  font-size: 1.08rem;
  cursor: pointer;
  text-decoration: none;
  transition: background 0.3s, box-shadow 0.3s, transform 0.2s;
  box-shadow: 0 4px 16px #a080ff33, 0 0 20px #c3a3ff22;
  position: relative;
}

.faq-action-btn:hover {
  background: linear-gradient(135deg, #c4a4ff, #7b88ff);
  box-shadow: 0 8px 32px #a080ff55, 0 0 32px #c3a3ff33;
  transform: translateY(-2px) scale(1.04);
}

.check-mark {
  color: #c3a3ff;
  font-weight: bold;
  font-size: 1.1em;
}

@media (max-width: 900px) {
  .faq-list {
    max-width: 98vw;
    padding: 0 1vw;
  }
  .faq-header h1 {
    font-size: 2.1rem;
  }
}
@media (max-width: 600px) {
  .faq-header {
    margin-top: 1.2rem;
    margin-bottom: 1.2rem;
  }
  .faq-header h1 {
    font-size: 1.3rem;
  }
  .faq-header p {
    font-size: 0.98rem;
  }
  .faq-content {
    padding: 0.5rem 0 2rem 0;
  }
  .faq-list {
    gap: 1.1rem;
    padding: 0 0.2rem;
  }
  .faq-item {
    border-radius: 12px;
  }
  .faq-question {
    padding: 1rem 1.1rem;
  }
  .faq-answer {
    padding: 0 1.1rem;
  }
  .faq-action-btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.95rem;
  }
}
</style>