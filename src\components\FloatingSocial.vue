<template>
  <div class="floating-social" :class="{ 'is-open': isOpen }">
    <!-- <div class="social-toggle" @click="toggleMenu">
      <i class="fas fa-share-alt"></i>
    </div> -->
    <div class="social-menu">
      <a href="https://www.instagram.com/agtfind" target="_blank" class="social-item instagram">
        <i class="fab fa-instagram"></i>
        <span>{{ $t('social.instagram') }}</span>
      </a>
      <a href="https://t.me/agtfind" target="_blank" class="social-item telegram">
        <i class="fab fa-telegram"></i>
        <span>{{ $t('social.telegram') }}</span>
      </a>
      <a href="https://discord.gg/agtfind" target="_blank" class="social-item discord">
        <i class="fab fa-discord"></i>
        <span>{{ $t('social.discord') }}</span>
      </a>
      <a href="https://www.reddit.com/r/agtfind" target="_blank" class="social-item reddit">
        <i class="fab fa-reddit"></i>
        <span>{{ $t('social.reddit') }}</span>
      </a>
      <a href="https://twitter.com/agtfind" target="_blank" class="social-item twitter">
        <i class="fab fa-twitter"></i>
        <span>{{ $t('social.twitter') }}</span>
      </a>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FloatingSocial',
  data() {
    return {
      isOpen: false
    }
  },
  methods: {
    toggleMenu() {
      this.isOpen = !this.isOpen;
    }
  }
}
</script>

<style scoped>
.floating-social {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.social-toggle {
  width: 50px;
  height: 50px;
  background: #4CAF50;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
}

.social-toggle i {
  color: white;
  font-size: 1.5rem;
}

.social-toggle:hover {
  transform: scale(1.1);
}

.social-menu {
  position: absolute;
  bottom: 60px;
  right: 0;
  background: white;
  border-radius: 10px;
  padding: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transform: translateY(20px);
  visibility: hidden;
  transition: all 0.3s ease;
}

.is-open .social-menu {
  opacity: 1;
  transform: translateY(0);
  visibility: visible;
}

.social-item {
  display: flex;
  align-items: center;
  padding: 10px 15px;
  color: #333;
  text-decoration: none;
  transition: background-color 0.3s ease;
  border-radius: 5px;
  margin: 5px 0;
  min-width: 150px;
}

.social-item i {
  margin-right: 10px;
  font-size: 1.2rem;
}

.social-item:hover {
  background-color: #f5f5f5;
}

.social-item.instagram:hover {
  color: #E1306C;
}

.social-item.telegram:hover {
  color: #0088cc;
}

.social-item.discord:hover {
  color: #7289DA;
}

.social-item.reddit:hover {
  color: #FF4500;
}

.social-item.twitter:hover {
  color: #1DA1F2;
}

@media (max-width: 768px) {
  .floating-social {
    right: 10px;
    bottom: 10px;
  }

  .social-toggle {
    width: 45px;
    height: 45px;
  }

  .social-menu {
    right: 0;
  }
}
</style>