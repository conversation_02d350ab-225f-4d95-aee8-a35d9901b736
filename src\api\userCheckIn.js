import apiClient from '@/services/api';

/**
 * 用户签到相关API服务
 */
const userCheckInApi = {
    /**
     * 执行每日签到
     * @param {string|number} userId - 用户ID
     * @returns {Promise} - API响应的Promise对象
     */
    doCheckIn(userId) {
        return apiClient.get('/userCheckIns/doCheckIn', {
            params: { userId }
        });
    },

    /**
     * 检查用户今日是否已签到
     * @param {string|number} userId - 用户ID
     * @returns {Promise} - API响应的Promise对象
     */
    checkTodayStatus(userId) {
        return apiClient.get('/userCheckIns/checkStatus', {
            params: { userId }
        });
    },

    /**
     * 获取用户签到历史记录
     * @param {string|number} userId - 用户ID
     * @param {number} page - 页码
     * @param {number} size - 每页记录数
     * @returns {Promise} - API响应的Promise对象
     */
    getCheckInHistory(userId, page = 1, size = 10) {
        return apiClient.get('/userCheckIns/history', {
            params: { userId, page, size }
        });
    },

    /**
     * 获取用户积分信息
     * @param {string|number} userId - 用户ID
     * @returns {Promise} - API响应的Promise对象
     */
    getUserPoints(userId) {
        return apiClient.get(`/omg/userPoints/${userId}`);
    },

    /**
     * 获取用户积分历史记录
     * @param {string|number} userId - 用户ID
     * @param {number} page - 页码，可选
     * @param {number} size - 每页记录数，可选
     * @returns {Promise} - API响应的Promise对象
     */
    getPointsHistory(userId, page, size) {
        const params = { userId };

        if (page) params.page = page;
        if (size) params.size = size;

        return apiClient.get('/pointTransactions/getPointTransactions', { params });
    },

    /**
     * 获取积分商城商品列表
     * @returns {Promise} - API响应的Promise对象
     */
    getMarketplaceProducts() {
        return apiClient.get('/pointMallGoods/list');
    }
};

export default userCheckInApi; 