import apiClient from '@/services/api';

/**
 * 批量检查点赞状态
 * @param {number|string} userId 用户ID
 * @param {Array} sellerIds 商家ID数组
 * @returns {Promise}
 */
export function checkSellerLikeStatus(userId, sellerIds) {
    return apiClient.post('/sellerLike/check', sellerIds, {
        params: { userId }
    });
}

/**
 * 点赞接口
 * @param {number|string} userId 用户ID
 * @param {number|string} sellerId 商家ID
 * @returns {Promise}
 */
export function addSellerLike(userId, sellerId) {
    return apiClient.post('/sellerLike/add', null, {
        params: { userId, sellerId }
    });
}

/**
 * 取消点赞接口
 * @param {number|string} userId 用户ID
 * @param {number|string} sellerId 商家ID
 * @returns {Promise}
 */
export function cancelSellerLike(userId, sellerId) {
    return apiClient.post('/sellerLike/cancel', null, {
        params: { userId, sellerId }
    });
} 