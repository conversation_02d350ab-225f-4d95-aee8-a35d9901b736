const { defineConfig } = require('@vue/cli-service')

module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: '/client/',
  outputDir: 'client',  // 确保与 Nginx 的 root 路径匹配
  assetsDir: 'static',

  // Moved outside of devServer
  devServer: {
    port: '8083',
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        timeout: 60000, // 设置代理超时时间为60秒
        pathRewrite: {
          '^/api': ''  // Rewrite path, remove the /api prefix
        }
      }
    }
  }
})
