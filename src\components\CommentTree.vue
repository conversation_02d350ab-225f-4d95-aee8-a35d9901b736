<template>
  <div class="comment-replies" :class="'level-' + nestingLevel">
    <!-- 折叠/展开按钮 -->
    <button class="toggle-replies-btn" @click="toggleCollapse(parentComment)">
      {{ parentComment.isCollapsed ? $t('comment.expand') + comments.length + $t('comment.replies') : $t('comment.collapse') }}
    </button>
    
    <!-- 嵌套回复列表 -->
    <div v-if="!parentComment.isCollapsed" class="nested-comments-list">
      <div v-for="reply in comments" :key="reply.id" class="comment-item reply-item" :class="'level-' + nestingLevel">
        <div class="comment-user-info">
          <img :src="reply.userAvatar" :alt="$t('comment.userAvatar')" class="comment-avatar">
          <div class="comment-user-meta">
            <div class="comment-username">
              {{ reply.userName }}
            </div>
            <div class="comment-time">{{ formatDate(reply.createdAt) }}</div>
          </div>
        </div>
        <div class="comment-text">{{ reply.content }}</div>
        
        <!-- 回复图片展示区 -->
        <div v-if="reply.images && reply.images.length > 0" class="comment-images">
          <div v-for="(image, imgIndex) in reply.images" :key="imgIndex" class="comment-image-item">
            <img :src="image" @click="previewImage(image)" class="comment-image" />
          </div>
        </div>
        
        <div class="comment-actions">
          <button class="action-btn" :class="{ 'liked': reply.isLiked }" @click="likeComment(reply)">
            <i class="fas fa-thumbs-up"></i> {{ reply.likesCount || 0 }}
          </button>
          <button class="action-btn" @click="replyToComment(reply)">
            <i class="fas fa-reply"></i> {{ $t('comment.reply') }}
          </button>
          <!-- 删除按钮，仅对用户自己的回复显示 -->
          <button 
            v-if="isCurrentUserComment(reply)" 
            class="action-btn delete-btn" 
            @click.stop="confirmDeleteComment(reply)"
          >
            <i class="fas fa-trash"></i> {{ $t('comment.delete') }}
          </button>
        </div>
        
        <!-- 递归渲染更深层级的回复 -->
        <comment-tree 
          v-if="reply.children && reply.children.length > 0" 
          :comments="reply.children"
          :parent-comment="reply"
          :nesting-level="nestingLevel + 1"
          @toggle-collapse="onToggleCollapse"
          @reply="onReply"
          @like="onLike"
          @delete="onDelete"
          @preview-image="onPreviewImage"
        />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'CommentTree',
  props: {
    comments: {
      type: Array,
      required: true
    },
    parentComment: {
      type: Object,
      required: true
    },
    nestingLevel: {
      type: Number,
      default: 1
    }
  },
  methods: {
    formatDate(date) {
      return this.$parent.formatDate(date);
    },
    toggleCollapse(comment) {
      this.$emit('toggle-collapse', comment);
    },
    replyToComment(comment) {
      this.$emit('reply', comment);
    },
    likeComment(comment) {
      this.$emit('like', comment);
    },
    confirmDeleteComment(comment) {
      this.$emit('delete', comment);
    },
    isCurrentUserComment(comment) {
      return this.$parent.isCurrentUserComment(comment);
    },
    previewImage(image) {
      this.$emit('preview-image', image);
    },
    // 事件中转方法，用于从子级传递到父级
    onToggleCollapse(comment) {
      this.$emit('toggle-collapse', comment);
    },
    onReply(comment) {
      this.$emit('reply', comment);
    },
    onLike(comment) {
      this.$emit('like', comment);
    },
    onDelete(comment) {
      this.$emit('delete', comment);
    },
    onPreviewImage(image) {
      this.$emit('preview-image', image);
    }
  }
}
</script>

<style scoped>
/* 嵌套回复区域样式增强 */
.comment-replies {
  margin-top: 10px;
  padding-left: 20px; /* 回复区域左侧缩进 */
  border-left: 1px solid #3c3f4e; /* 回复区域左侧分割线 */
}

/* 更深层级的回复 */
.comment-replies.level-1 {
  border-left: 1px solid #3c3f4e;
}

.comment-replies.level-2 {
  padding-left: 18px;
  border-left: 1px dashed #3c3f4e;
}

.comment-replies.level-3 {
  padding-left: 16px;
  border-left: 1px dotted #3c3f4e;
}

.comment-replies.level-4,
.comment-replies.level-5,
.comment-replies.level-6 {
  padding-left: 14px;
  border-left: 1px dotted #3c3f4e;
}

/* 递归嵌套评论样式 */
.comment-item.reply-item {
  background-color: #23252f;
  border-left: 3px solid #4a507d;
  margin-top: 12px;
  font-size: 0.98em;
  padding: 16px;
  margin-bottom: 16px;
  border-radius: 8px;
}

.comment-item.reply-item.level-2 {
  background-color: #252731;
  border-left: 3px solid #5a6195;
  font-size: 0.96em;
}

.comment-item.reply-item.level-3 {
  background-color: #27293a;
  border-left: 3px solid #6a71aa;
  font-size: 0.94em;
}

.comment-item.reply-item.level-4,
.comment-item.reply-item.level-5,
.comment-item.reply-item.level-6 {
  background-color: #292c41;
  border-left: 3px solid #7a81bf;
  font-size: 0.92em;
}

/* 头像尺寸递减 */
.comment-item.reply-item .comment-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  margin-right: 12px;
  object-fit: cover;
  border: 2px solid #4568dc;
  box-shadow: 0 0 10px rgba(69, 104, 220, 0.3);
}

.comment-item.reply-item.level-2 .comment-avatar {
  width: 32px;
  height: 32px;
}

.comment-item.reply-item.level-3 .comment-avatar {
  width: 30px;
  height: 30px;
}

.comment-item.reply-item.level-4 .comment-avatar,
.comment-item.reply-item.level-5 .comment-avatar,
.comment-item.reply-item.level-6 .comment-avatar {
  width: 28px;
  height: 28px;
}

/* 用户信息样式 */
.comment-user-info {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
}

.comment-user-meta {
  display: flex;
  flex-direction: column;
}

.comment-username {
  font-weight: 600;
  font-size: 15px;
  color: #e0e0e0;
  display: flex;
  align-items: center;
}

.comment-time {
  font-size: 12px;
  color: #9a9ea9;
  margin-top: 2px;
}

/* 评论文本样式 */
.comment-text {
  font-size: 14px;
  line-height: 1.6;
  margin-bottom: 12px;
  color: #c8cad8;
  word-break: break-word;
}

/* 评论操作按钮 */
.comment-actions {
  display: flex;
  gap: 16px;
}

.action-btn {
  font-size: 13px;
  color: #9a9ea9;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  transition: all 0.2s;
}

.action-btn:hover {
  color: #7b88ff;
}

/* 点赞按钮的已点赞状态 */
.action-btn.liked {
  color: #FF6B6B;
  font-weight: 600;
}

.action-btn.liked i {
  color: #FF6B6B;
}

/* 折叠/展开按钮 */
.toggle-replies-btn {
  background: none;
  border: none;
  color: #7b88ff;
  font-size: 13px;
  cursor: pointer;
  padding: 4px 0;
  margin-bottom: 8px;
  transition: color 0.2s;
}

.toggle-replies-btn:hover {
  text-decoration: underline;
  color: #8a96ff;
}

/* 评论图片展示区 */
.comment-images {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-top: 10px;
  margin-bottom: 10px;
}

.comment-image-item {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #3c3f4e;
  transition: transform 0.2s;
  cursor: pointer;
}

.comment-image-item:hover {
  transform: scale(1.05);
  box-shadow: 0 0 10px rgba(69, 104, 220, 0.3);
}

.comment-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 每级评论文字及按钮尺寸递减 */
.comment-item.reply-item.level-2 .comment-username,
.comment-item.reply-item.level-2 .comment-text {
  font-size: 0.98em;
}

.comment-item.reply-item.level-3 .comment-username,
.comment-item.reply-item.level-3 .comment-text {
  font-size: 0.96em;
}

.comment-item.reply-item.level-4 .comment-username,
.comment-item.reply-item.level-4 .comment-text,
.comment-item.reply-item.level-5 .comment-username,
.comment-item.reply-item.level-5 .comment-text,
.comment-item.reply-item.level-6 .comment-username,
.comment-item.reply-item.level-6 .comment-text {
  font-size: 0.94em;
}

/* 评论操作按钮递减尺寸 */
.comment-item.reply-item .comment-actions .action-btn {
  font-size: 0.95em;
  padding: 3px 7px;
}

.comment-item.reply-item.level-2 .comment-actions .action-btn {
  font-size: 0.9em;
  padding: 3px 6px;
}

.comment-item.reply-item.level-3 .comment-actions .action-btn,
.comment-item.reply-item.level-4 .comment-actions .action-btn,
.comment-item.reply-item.level-5 .comment-actions .action-btn,
.comment-item.reply-item.level-6 .comment-actions .action-btn {
  font-size: 0.85em;
  padding: 2px 5px;
}
</style>