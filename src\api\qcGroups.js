import apiClient from '@/services/api';

/**
 * 获取QC组列表数据
 * @returns {Promise<Object>} QC组数据
 */
export const getQcGroupsList = async () => {
    try {
        // 实际API调用
        const response = await apiClient.get('/omg/qc/list');
        
        // 如果响应成功且有数据
        if (response && response.code === 200 && response.data) {
            // 处理图片数据
            const qcGroups = [];
            
            // 遍历返回数据中的每个组
            Object.keys(response.data).forEach(groupName => {
                const groupData = response.data[groupName];
                
                // 确保每个组都是数组
                if (Array.isArray(groupData) && groupData.length > 0) {
                    const group = groupData[0];
                    
                    // 处理图片URL，将逗号分隔的字符串转为数组
                    const images = group.imageUrl ? group.imageUrl.split(',') : [];
                    
                    // 为每个图片创建一个对象
                    const imageObjects = images.map((url, index) => ({
                        id: `${group.id}_${index}`,
                        groupId: group.id,
                        groupName: group.groupName,
                        count: `${index + 1}/${images.length}`,
                        description: `${group.groupName} Image ${index + 1}`,
                        imageUrl: url,
                        position: group.position
                    }));
                    
                    // 将处理后的图片对象数组添加到结果中
                    qcGroups.push(imageObjects);
                }
            });
            
            // 按position排序
            qcGroups.sort((a, b) => {
                const posA = a[0]?.position || 0;
                const posB = b[0]?.position || 0;
                return posA - posB;
            });
            
            return qcGroups;
        }
        
        return [];
    } catch (error) {
        console.error('获取QC组列表失败:', error);
        return [];
    }
};

export default {
    getQcGroupsList
}; 