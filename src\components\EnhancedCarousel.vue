<template>
  <div class="enhanced-carousel-section">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-spinner-container">
      <div class="loading-spinner"></div>
    </div>
    
    <!-- 轮播图主体 -->
    <div v-else-if="slides && slides.length > 0" class="enhanced-carousel">
      <div class="carousel-container"
           @touchstart="handleTouchStart"
           @touchmove="handleTouchMove"
           @touchend="handleTouchEnd"
           @mouseenter="pauseAutoplay"
           @mouseleave="resumeAutoplay">
        
        <!-- 轮播图轨道 -->
        <div class="carousel-track" :style="{ transform: `translateX(-${currentIndex * 100}%)` }">
          <div 
            v-for="(slide, index) in slides" 
            :key="index" 
            class="carousel-slide"
            @click="handleSlideClick(slide, index)"
          >
            <img 
              :src="slide.imageUrl || slide.image" 
              :alt="slide.title || slide.alt || 'Carousel image'" 
              class="carousel-image"
              @error="handleImageError"
            >
            
            <!-- 内容遮罩层 -->
            <div v-if="slide.title || slide.description" class="slide-content">
              <h3 v-if="slide.title" class="slide-title">{{ slide.title }}</h3>
              <p v-if="slide.description" class="slide-description">{{ slide.description }}</p>
              <button v-if="slide.buttonText" class="slide-button" @click.stop="handleButtonClick(slide)">
                {{ slide.buttonText }}
              </button>
            </div>
          </div>
        </div>
        
        <!-- 导航控制按钮 -->
        <button 
          v-if="showControls && slides.length > 1"
          class="carousel-control prev" 
          @click="prevSlide"
          :disabled="currentIndex === 0 && !loop"
        >
          <i class="fas fa-chevron-left"></i>
        </button>
        
        <button 
          v-if="showControls && slides.length > 1"
          class="carousel-control next" 
          @click="nextSlide"
          :disabled="currentIndex === slides.length - 1 && !loop"
        >
          <i class="fas fa-chevron-right"></i>
        </button>
        
        <!-- 指示器 -->
        <div v-if="showIndicators && slides.length > 1" class="carousel-indicators">
          <button 
            v-for="(_, index) in slides" 
            :key="index" 
            :class="['indicator', { active: index === currentIndex }]"
            @click="goToSlide(index)"
            @mouseenter="indicatorHoverChange ? goToSlide(index) : null"
          ></button>
        </div>
        
        <!-- 幻灯片计数器 -->
        <div v-if="showCounter" class="slide-counter">
          {{ currentIndex + 1 }} / {{ slides.length }}
        </div>
      </div>
    </div>
    
    <!-- 空状态 -->
    <div v-else-if="!loading" class="empty-carousel">
      <i class="fas fa-image"></i>
      <p>{{ emptyText || 'No images to display' }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EnhancedCarousel',
  props: {
    // 轮播图数据
    slides: {
      type: Array,
      default: () => [],
      validator: (value) => {
        return Array.isArray(value) && value.every(slide => 
          slide && (slide.imageUrl || slide.image)
        );
      }
    },
    
    // 是否正在加载
    loading: {
      type: Boolean,
      default: false
    },
    
    // 自动播放间隔，单位毫秒
    autoplayInterval: {
      type: Number,
      default: 5000,
      validator: (value) => value > 0
    },
    
    // 是否启用自动播放
    autoplay: {
      type: Boolean,
      default: true
    },
    
    // 是否循环播放
    loop: {
      type: Boolean,
      default: true
    },
    
    // 是否显示控制按钮
    showControls: {
      type: Boolean,
      default: true
    },
    
    // 是否显示指示器
    showIndicators: {
      type: Boolean,
      default: true
    },
    
    // 是否显示计数器
    showCounter: {
      type: Boolean,
      default: false
    },
    
    // 指示器hover时是否自动切换
    indicatorHoverChange: {
      type: Boolean,
      default: true
    },
    
    // 最小滑动距离
    minSwipeDistance: {
      type: Number,
      default: 50
    },
    
    // 轮播图高度
    height: {
      type: [String, Number],
      default: 400
    },
    
    // 空状态提示文本
    emptyText: {
      type: String,
      default: ''
    },
    
    // 过渡动画效果
    transition: {
      type: String,
      default: 'slide', // slide, fade
      validator: (value) => ['slide', 'fade'].includes(value)
    }
  },
  
  data() {
    return {
      currentIndex: 0,
      touchStartX: 0,
      touchEndX: 0,
      interval: null,
      isPaused: false
    }
  },
  
  computed: {
    carouselHeight() {
      return typeof this.height === 'number' ? `${this.height}px` : this.height;
    }
  },
  
  mounted() {
    if (this.autoplay && this.slides.length > 1) {
      this.startAutoplay();
    }
  },
  
  beforeUnmount() {
    this.stopAutoplay();
  },
  
  watch: {
    slides: {
      handler(newSlides) {
        // 当slides改变时，重置索引
        if (this.currentIndex >= newSlides.length) {
          this.currentIndex = 0;
        }
        
        // 重新启动自动播放
        if (this.autoplay && newSlides.length > 1) {
          this.startAutoplay();
        } else {
          this.stopAutoplay();
        }
      },
      immediate: true
    },
    
    autoplay(newVal) {
      if (newVal && this.slides.length > 1) {
        this.startAutoplay();
      } else {
        this.stopAutoplay();
      }
    }
  },
  
  methods: {
    /**
     * 开始自动播放
     */
    startAutoplay() {
      this.stopAutoplay();
      
      if (this.autoplay && this.slides.length > 1 && !this.isPaused) {
        this.interval = setInterval(() => {
          this.nextSlide();
        }, this.autoplayInterval);
      }
    },
    
    /**
     * 停止自动播放
     */
    stopAutoplay() {
      if (this.interval) {
        clearInterval(this.interval);
        this.interval = null;
      }
    },
    
    /**
     * 暂停自动播放
     */
    pauseAutoplay() {
      this.isPaused = true;
      this.stopAutoplay();
    },
    
    /**
     * 恢复自动播放
     */
    resumeAutoplay() {
      this.isPaused = false;
      this.startAutoplay();
    },
    
    /**
     * 前往上一张
     */
    prevSlide() {
      if (this.currentIndex === 0) {
        this.currentIndex = this.loop ? this.slides.length - 1 : 0;
      } else {
        this.currentIndex--;
      }
      this.restartAutoplay();
      this.$emit('slide-change', {
        index: this.currentIndex,
        slide: this.slides[this.currentIndex]
      });
    },
    
    /**
     * 前往下一张
     */
    nextSlide() {
      if (this.currentIndex === this.slides.length - 1) {
        this.currentIndex = this.loop ? 0 : this.slides.length - 1;
      } else {
        this.currentIndex++;
      }
      this.restartAutoplay();
      this.$emit('slide-change', {
        index: this.currentIndex,
        slide: this.slides[this.currentIndex]
      });
    },
    
    /**
     * 前往指定幻灯片
     */
    goToSlide(index) {
      if (index >= 0 && index < this.slides.length) {
        this.currentIndex = index;
        this.restartAutoplay();
        this.$emit('slide-change', {
          index: this.currentIndex,
          slide: this.slides[this.currentIndex]
        });
      }
    },
    
    /**
     * 重新开始自动播放
     */
    restartAutoplay() {
      if (this.autoplay) {
        this.startAutoplay();
      }
    },
    
    /**
     * 处理幻灯片点击
     */
    handleSlideClick(slide, index) {
      this.$emit('slide-click', { slide, index });
      
      // 如果有链接，处理导航
      if (slide.linkUrl || slide.link) {
        const url = slide.linkUrl || slide.link;
        if (url.startsWith('/')) {
          this.$router.push(url);
        } else {
          window.open(url, '_blank');
        }
      }
    },
    
    /**
     * 处理按钮点击
     */
    handleButtonClick(slide) {
      this.$emit('button-click', slide);
    },
    
    /**
     * 处理图片错误
     */
    handleImageError(event) {
      console.warn('Carousel image failed to load:', event.target.src);
      this.$emit('image-error', event);
    },
    
    /**
     * 处理触摸开始
     */
    handleTouchStart(event) {
      this.touchStartX = event.touches[0].clientX;
      this.pauseAutoplay();
    },
    
    /**
     * 处理触摸移动
     */
    handleTouchMove(event) {
      this.touchEndX = event.touches[0].clientX;
    },
    
    /**
     * 处理触摸结束
     */
    handleTouchEnd() {
      const swipeDistance = this.touchEndX - this.touchStartX;
      
      if (Math.abs(swipeDistance) >= this.minSwipeDistance) {
        if (swipeDistance > 0) {
          this.prevSlide();
        } else {
          this.nextSlide();
        }
      }
      
      this.resumeAutoplay();
    }
  }
}
</script>

<style scoped>
/* 增强版轮播图样式 - 宇宙主题 */
.enhanced-carousel-section {
  margin: 1rem auto 2.5rem;
  position: relative;
  max-width: 1400px;
  width: 100%;
  overflow: hidden;
  z-index: 1;
}

/* 加载状态 */
.loading-spinner-container {
  height: 250px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(20, 15, 40, 0.7);
  border-radius: 16px;
  overflow: hidden;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(123, 136, 255, 0.3);
  border-radius: 50%;
  border-top-color: #7b88ff;
  animation: spin 1s ease-in-out infinite;
  filter: drop-shadow(0 0 10px rgba(123, 136, 255, 0.5));
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 轮播图主体 */
.enhanced-carousel {
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5), 0 0 30px rgba(123, 136, 255, 0.3);
  border: 1px solid rgba(123, 136, 255, 0.3);
}

/* 发光边框效果 */
.enhanced-carousel::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #7b88ff, #a080ff);
  z-index: -1;
  border-radius: 18px;
  filter: blur(12px);
  opacity: 0.6;
  animation: glow 3s infinite alternate;
}

@keyframes glow {
  0% { opacity: 0.5; filter: blur(12px); }
  100% { opacity: 0.8; filter: blur(16px); }
}

.carousel-container {
  position: relative;
  overflow: hidden;
  height: v-bind(carouselHeight);
  border-radius: 16px;
}

/* 轮播图轨道 */
.carousel-track {
  display: flex;
  transition: transform 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  height: 100%;
}

.carousel-slide {
  flex: 0 0 100%;
  position: relative;
  overflow: hidden;
  height: 100%;
  transition: transform 0.3s ease;
}

.carousel-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.8s ease;
}

.carousel-slide:hover .carousel-image {
  transform: scale(1.05);
}

/* 幻灯片内容 */
.slide-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 2rem;
  background: linear-gradient(to top, rgba(10, 5, 20, 0.9), rgba(20, 15, 35, 0.5) 70%, transparent);
  color: white;
  transform: translateY(0);
  transition: transform 0.4s ease;
}

.carousel-slide:hover .slide-content {
  transform: translateY(-5px);
}

.slide-title {
  margin: 0 0 10px;
  font-size: 1.8rem;
  font-weight: 600;
  background: linear-gradient(135deg, #a080ff 10%, #7b88ff 45%, #c4a4ff 90%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  text-shadow: 0 2px 15px rgba(160, 128, 255, 0.3);
  transition: all 0.3s ease;
}

.slide-description {
  margin: 0 0 15px;
  font-size: 1.1rem;
  opacity: 0.85;
  max-width: 80%;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
}

.slide-button {
  background: linear-gradient(135deg, #7b88ff, #a080ff);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(123, 136, 255, 0.3);
}

.slide-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(123, 136, 255, 0.4);
}

/* 控制按钮 */
.carousel-control {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 50px;
  height: 50px;
  background: rgba(25, 20, 50, 0.7);
  border: 1px solid rgba(123, 136, 255, 0.4);
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  opacity: 0;
  transition: all 0.3s ease;
  z-index: 10;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3), 0 0 10px rgba(123, 136, 255, 0.2);
  backdrop-filter: blur(5px);
}

.carousel-control:disabled {
  opacity: 0.3 !important;
  cursor: not-allowed;
}

.carousel-control.prev {
  left: 20px;
}

.carousel-control.next {
  right: 20px;
}

.enhanced-carousel:hover .carousel-control:not(:disabled) {
  opacity: 0.8;
}

.carousel-control:hover:not(:disabled) {
  background: rgba(40, 30, 80, 0.9);
  transform: translateY(-50%) scale(1.1);
  box-shadow: 0 5px 20px rgba(0, 0, 0, 0.4), 0 0 15px rgba(123, 136, 255, 0.4);
  border-color: rgba(123, 136, 255, 0.7);
  opacity: 1 !important;
}

.carousel-control::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(123, 136, 255, 0.6), rgba(160, 128, 255, 0.6));
  z-index: -1;
  opacity: 0;
  filter: blur(5px);
  transition: all 0.3s ease;
}

.carousel-control:hover::before {
  opacity: 1;
  filter: blur(8px);
}

/* 指示器 */
.carousel-indicators {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 10px;
  z-index: 10;
}

.indicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 0;
  box-shadow: 0 0 8px rgba(0, 0, 0, 0.3);
}

.indicator.active {
  background: linear-gradient(135deg, #7b88ff, #a080ff);
  transform: scale(1.2);
  box-shadow: 0 0 10px rgba(123, 136, 255, 0.7);
}

.indicator:hover:not(.active) {
  background: rgba(255, 255, 255, 0.5);
  transform: scale(1.1);
}

/* 计数器 */
.slide-counter {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 500;
  backdrop-filter: blur(5px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 星星装饰效果 */
.enhanced-carousel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background-image: 
    radial-gradient(1px 1px at 50px 30px, #fff 100%, transparent),
    radial-gradient(1px 1px at 100px 80px, #fff 100%, transparent),
    radial-gradient(2px 2px at 150px 15px, #fff 100%, transparent),
    radial-gradient(1px 1px at 200px 50px, #fff 100%, transparent),
    radial-gradient(1px 1px at 250px 90px, #fff 100%, transparent);
  opacity: 0.4;
}

/* 空状态 */
.empty-carousel {
  height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(20, 15, 40, 0.3);
  border-radius: 16px;
  color: rgba(255, 255, 255, 0.6);
  border: 2px dashed rgba(123, 136, 255, 0.3);
}

.empty-carousel i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-carousel p {
  font-size: 1.1rem;
  margin: 0;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .enhanced-carousel-section {
    margin: 0.5rem auto 2rem;
    width: 400px; /* 强制设置宽度为400px */
    max-width: 400px;
  }
  
  .enhanced-carousel {
    width: 400px; /* 轮播图容器强制400px */
    height: 150px; /* 轮播图容器强制150px */
  }
  
  .carousel-container {
    width: 400px; /* 容器强制400px */
    height: 150px; /* 容器强制150px */
    margin: 0 auto;
  }
  
  .carousel-slide {
    width: 400px; /* 幻灯片强制400px */
    height: 150px; /* 幻灯片强制150px */
  }
  
  .carousel-image {
    width: 400px !important; /* 图片强制400px宽度 */
    height: 150px !important; /* 图片强制150px高度 */
    object-fit: cover; /* 确保图片覆盖整个容器 */
  }
  
  .slide-content {
    padding: 0.8rem;
  }
  
  .slide-title {
    font-size: 1.0rem;
  }
  
  .slide-description {
    font-size: 0.8rem;
    max-width: 100%;
  }
  
  /* 移动端隐藏左右切换按钮 */
  .carousel-control {
    display: none !important;
  }
  
  .carousel-control.prev {
    display: none !important;
  }
  
  .carousel-control.next {
    display: none !important;
  }
  
  /* 移动端隐藏指示器 */
  .carousel-indicators {
    display: none !important;
  }
  
  .indicator {
    display: none !important;
  }
  
  .slide-counter {
    top: 8px;
    right: 8px;
    font-size: 0.7rem;
    padding: 2px 6px;
  }
}

@media (max-width: 480px) {
  .enhanced-carousel-section {
    margin: 0.5rem auto 1.5rem;
    width: 400px; /* 小屏幕也强制400px */
    max-width: 400px;
  }
  
  .enhanced-carousel {
    width: 400px; /* 轮播图容器强制400px */
    height: 150px; /* 轮播图容器强制150px */
  }
  
  .carousel-container {
    width: 400px; /* 容器强制400px */
    height: 150px; /* 容器强制150px */
  }
  
  .carousel-slide {
    width: 400px; /* 幻灯片强制400px */
    height: 150px; /* 幻灯片强制150px */
  }
  
  .carousel-image {
    width: 400px !important; /* 图片强制400px宽度 */
    height: 150px !important; /* 图片强制150px高度 */
    object-fit: cover; /* 确保图片覆盖整个容器 */
  }
  
  .slide-content {
    padding: 0.6rem;
  }
  
  .slide-title {
    font-size: 0.9rem;
    margin-bottom: 3px;
  }
  
  .slide-description {
    font-size: 0.7rem;
  }
  
  .slide-button {
    padding: 5px 10px;
    font-size: 0.75rem;
  }
  
  /* 小屏幕也隐藏左右切换按钮 */
  .carousel-control {
    display: none !important;
  }
  
  .carousel-control.prev {
    display: none !important;
  }
  
  .carousel-control.next {
    display: none !important;
  }
  
  /* 确保小屏幕也隐藏指示器 */
  .carousel-indicators {
    display: none !important;
  }
  
  .indicator {
    display: none !important;
  }
}
</style> 