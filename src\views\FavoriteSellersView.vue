<template>
  <div class="sellers-page">
    <div class="sellers-header">
      <h1 class="page-title">Favorite Sellers</h1>
    </div>
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <div class="loading-text">Loading favorite sellers...</div>
    </div>
    <div v-else-if="favoriteSellers.length === 0" class="empty-favorites">
      <div class="empty-icon">💔</div>
      <div class="empty-title">No Favorite Sellers Yet</div>
      <div class="empty-desc">You haven't added any sellers to your favorites list.</div>
      <el-button type="primary" @click="$router.push('/sellers')" class="browse-btn">Browse Sellers</el-button>
    </div>
    <div v-else class="sellers-grid">
      <div 
        class="seller-card fade-in" 
        v-for="seller in favoriteSellers" 
        :key="seller.id"
        :class="{'promoted': seller.type === 'promoted', 'trusted': seller.type === 'trusted'}"
      >
        <div class="card-glow"></div>
        <div class="seller-content">
          <div class="seller-logo-container">
            <img v-if="seller.logoUrl" :src="seller.logoUrl" :alt="seller.name || 'Seller'" class="seller-logo">
            <div v-else class="seller-logo">
              <div class="initials-placeholder">{{ getInitials(seller.storeName) }}</div>
            </div>
            <div v-if="seller.type === 'promoted'" class="seller-badge promoted">PROMOTED</div>
            <div v-else-if="seller.type === 'trusted'" class="seller-badge trusted">TRUSTED</div>
          </div>
          <h3 class="seller-name">{{ seller.storeName || 'Unnamed Seller' }}</h3>
          <p class="seller-desc">{{ seller.description || 'No description available' }}</p>
          <div class="rating-container">
            <div class="rating-item">
              <div class="like-icon active"></div>
              <span class="rating-value">{{ seller.likes || '0' }}</span>
            </div>
            <div class="rating-item">
              <div class="bookmark-icon active"></div>
              <span class="rating-value">{{ seller.collect || '0' }}</span>
            </div>
          </div>
          <div class="seller-buttons">
            <button class="marketplace-btn" @click="goToSellerPage(seller.platformUrl || '')">
              MARKETPLACE
            </button>
            <button class="learn-more-btn" @click="goToSellerPage(seller.websiteUrl || '')">
              LEARN MORE
            </button>
            <button class="remove-btn" @click="confirmRemove(seller.sellerId)">
              REMOVE
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
// import { useRouter } from 'vue-router' // 移除未使用的 router
import { ElMessage, ElMessageBox } from 'element-plus'
import 'element-plus/dist/index.css'
import { getFavoriteSellers } from '@/api/favoriteSellers'
import { cancelSellerCollect } from '@/api/sellerCollect'

export default {
  name: 'FavoriteSellersView',
  setup() {
    // const router = useRouter() // 移除未使用的 router
    const favoriteSellers = ref([])
    const loading = ref(true)
    const isDarkMode = ref(document.documentElement.getAttribute('data-theme') === 'dark')

    // Theme detection
    const updateTheme = () => {
      isDarkMode.value = document.documentElement.getAttribute('data-theme') === 'dark'
    }

    // Theme observer
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === 'data-theme') {
          updateTheme()
        }
      })
    })

    // 动态获取userId
    let userId = null
    try {
      const userInfo = JSON.parse(localStorage.getItem('userInfo'))
      userId = userInfo?.userId || userInfo?.id || null
    } catch (e) {
      userId = null
    }

    // 获取收藏商家数据
    const fetchFavoriteSellers = async () => {
      if (!userId) {
        ElMessage.error('请先登录')
        loading.value = false
        return
      }
      loading.value = true
      try {
        const response = await getFavoriteSellers(userId)
        if (response && Array.isArray(response.data)) {
          favoriteSellers.value = response.data.map(item => item.seller)
        } else {
          favoriteSellers.value = []
        }
      } catch (error) {
        console.error('获取收藏商家列表失败:', error)
        ElMessage.error('Failed to load favorite sellers')
        favoriteSellers.value = []
      } finally {
        loading.value = false
      }
    }

    const confirmRemove = (sellerId) => {
      ElMessageBox.confirm(
        'Are you sure you want to remove this seller from favorites?',
        'Confirm',
        {
          confirmButtonText: 'Yes',
          cancelButtonText: 'No',
          type: 'warning'
        }
      ).then(() => {
        removeFromFavorites(sellerId)
      }).catch(() => {})
    }

    const removeFromFavorites = async (sellerId) => {
      if (!userId) {
        ElMessage.error('请先登录')
        return
      }
      try {
        await cancelSellerCollect(userId, sellerId)
        favoriteSellers.value = favoriteSellers.value.filter(item => String(item.sellerId) !== String(sellerId))
        ElMessage.success('Successfully removed from favorites')
      } catch (error) {
        console.error('移除收藏商家失败:', error)
        ElMessage.error('Failed to remove from favorites')
      }
    }

    const goToSellerPage = (url) => {
      if (!url) return
      // 检查URL是否包含协议
      if (!url.startsWith('http://') && !url.startsWith('https://')) {
        url = 'https://' + url
      }
      // 在新标签页打开外部链接
      window.open(url, '_blank')
    }

    const getInitials = (name) => {
      if (!name) return 'AG'
      return name.substring(0, 2).toUpperCase()
    }

    onMounted(() => {
      fetchFavoriteSellers()
      
      // Start observing theme changes
      updateTheme()
      observer.observe(document.documentElement, { attributes: true })
    })

    onUnmounted(() => {
      // Disconnect observer when component is unmounted
      observer.disconnect()
    })

    return {
      favoriteSellers,
      loading,
      isDarkMode,
      confirmRemove,
      removeFromFavorites,
      goToSellerPage,
      getInitials
    }
  }
}
</script>

<style scoped>
.sellers-page {
  padding: 2rem 5rem;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  transition: all 0.3s ease;
}

[data-theme="dark"] .sellers-page {
  background: transparent;
  color: #e0e0e0;
}

[data-theme="light"] .sellers-page {
  background: transparent;
  color: #333;
}

.sellers-header {
  margin-bottom: 2rem;
  text-align: center;
}

[data-theme="dark"] .page-title {
  color: #c3a3ff;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.5);
}

[data-theme="light"] .page-title {
  color: #6a46b8;
  text-shadow: 0 0 10px rgba(106, 70, 184, 0.15);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  letter-spacing: 1px;
  transition: all 0.3s ease;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 200px;
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
  margin-bottom: 1rem;
  transition: all 0.3s ease;
}

[data-theme="dark"] .loading-spinner {
  border: 5px solid rgba(128, 102, 204, 0.3);
  border-top-color: #8066cc;
}

[data-theme="light"] .loading-spinner {
  border: 5px solid rgba(106, 70, 184, 0.2);
  border-top-color: #6a46b8;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

[data-theme="dark"] .loading-text {
  color: #c3a3ff;
}

[data-theme="light"] .loading-text {
  color: #6a46b8;
}

.loading-text {
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

.status-message {
  text-align: center;
  padding: 3rem;
  border-radius: 20px;
  font-size: 1.2rem;
  transition: all 0.3s ease;
}

[data-theme="dark"] .status-message {
  background: rgba(25, 25, 35, 0.7);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  color: #e0e0e0;
}

[data-theme="light"] .status-message {
  background: rgba(250, 250, 252, 0.8);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  color: #333;
}

.sellers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  perspective: 1000px;
}

.seller-card {
  backdrop-filter: blur(10px);
  border-radius: 12px;
  position: relative;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  transform-style: preserve-3d;
}

[data-theme="dark"] .seller-card {
  background: rgba(25, 25, 35, 0.7);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(80, 80, 100, 0.3);
}

[data-theme="light"] .seller-card {
  background: rgba(255, 255, 255, 0.85);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(230, 230, 240, 0.8);
}

[data-theme="dark"] .seller-card.trusted,
[data-theme="dark"] .seller-card.promoted {
  border: 1px solid #8066cc;
}

[data-theme="light"] .seller-card.trusted,
[data-theme="light"] .seller-card.promoted {
  border: 1px solid #6a46b8;
}

.card-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 12px;
  z-index: -1;
  transition: all 0.5s ease;
  opacity: 0.5;
}

[data-theme="dark"] .seller-card.trusted .card-glow, 
[data-theme="dark"] .seller-card.promoted .card-glow {
  box-shadow: 0 0 20px rgba(128, 102, 204, 0.3);
}

[data-theme="light"] .seller-card.trusted .card-glow, 
[data-theme="light"] .seller-card.promoted .card-glow {
  box-shadow: 0 0 20px rgba(106, 70, 184, 0.15);
}

.seller-card:hover {
  transform: translateY(-10px);
}

[data-theme="dark"] .seller-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.6);
  border-color: rgba(255, 255, 255, 0.3);
}

[data-theme="light"] .seller-card:hover {
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  border-color: rgba(106, 70, 184, 0.3);
}

[data-theme="dark"] .seller-card:hover .card-glow {
  opacity: 1;
  box-shadow: 0 0 30px rgba(128, 102, 204, 0.5);
}

[data-theme="light"] .seller-card:hover .card-glow {
  opacity: 1;
  box-shadow: 0 0 30px rgba(106, 70, 184, 0.3);
}

.seller-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1.5rem;
  position: relative;
  z-index: 1;
}

.seller-logo-container {
  position: relative;
  margin-bottom: 1rem;
  width: 100px;
  height: 100px;
}

.seller-logo {
  width: 100px;
  height: 100px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #ffffff;
  border-radius: 50%;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

[data-theme="dark"] .seller-logo {
  border: 3px solid rgba(128, 102, 204, 0.6);
}

[data-theme="light"] .seller-logo {
  border: 3px solid rgba(106, 70, 184, 0.4);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.seller-logo img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.initials-placeholder {
  font-size: 2.5rem;
  font-weight: 800;
  color: #1a1a25;
}

.seller-badge {
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 2;
  padding: 0.25rem 0.8rem;
  font-size: 0.7rem;
  font-weight: 800;
  letter-spacing: 1px;
  text-transform: uppercase;
  border-radius: 4px;
  color: white;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

[data-theme="dark"] .seller-badge {
  background: linear-gradient(135deg, #8066cc, #5a46a8);
}

[data-theme="light"] .seller-badge {
  background: linear-gradient(135deg, #6a46b8, #5a46a8);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

.seller-name {
  font-size: 1.2rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

[data-theme="dark"] .seller-name {
  color: #ffffff;
}

[data-theme="light"] .seller-name {
  color: #222;
}

.seller-desc {
  font-size: 0.9rem;
  margin-bottom: 1rem;
  text-align: center;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  height: 2.8em;
  transition: all 0.3s ease;
}

[data-theme="dark"] .seller-desc {
  color: rgba(255, 255, 255, 0.8);
}

[data-theme="light"] .seller-desc {
  color: rgba(40, 40, 40, 0.8);
}

.rating-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0.5rem 0 1rem;
  gap: 2rem;
}

.rating-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.like-icon, .bookmark-icon {
  width: 24px;
  height: 24px;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

[data-theme="dark"] .like-icon.active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238066cc'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}

[data-theme="light"] .like-icon.active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236a46b8'%3E%3Cpath d='M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z'/%3E%3C/svg%3E");
}

[data-theme="dark"] .bookmark-icon.active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%238066cc'%3E%3Cpath d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

[data-theme="light"] .bookmark-icon.active {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%236a46b8'%3E%3Cpath d='M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2z'/%3E%3C/svg%3E");
}

.rating-value {
  font-size: 1rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

[data-theme="dark"] .rating-value {
  color: rgba(255, 255, 255, 0.85);
}

[data-theme="light"] .rating-value {
  color: rgba(40, 40, 40, 0.85);
}

.seller-buttons {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.marketplace-btn, .learn-more-btn, .remove-btn {
  border: none;
  border-radius: 6px;
  padding: 0.6rem 0;
  cursor: pointer;
  font-weight: 700;
  font-size: 0.8rem;
  letter-spacing: 1px;
  transition: all 0.3s ease;
  text-align: center;
  width: 100%;
}

[data-theme="dark"] .marketplace-btn {
  background: linear-gradient(135deg, #8066cc, #6a46b8);
  color: #ffffff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .marketplace-btn {
  background: linear-gradient(135deg, #6a46b8, #5a46a8);
  color: #ffffff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .learn-more-btn {
  background: #252530;
  color: #ffffff;
  border: 1px solid rgba(128, 102, 204, 0.4);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .learn-more-btn {
  background: #f4f4f8;
  color: #333;
  border: 1px solid rgba(106, 70, 184, 0.3);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .remove-btn {
  background: linear-gradient(135deg, #ff3366, #dc143c);
  color: #ffffff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

[data-theme="light"] .remove-btn {
  background: linear-gradient(135deg, #ff3366, #e83e8c);
  color: #ffffff;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
}

.marketplace-btn:hover, .learn-more-btn:hover, .remove-btn:hover {
  transform: translateY(-3px);
}

[data-theme="dark"] .marketplace-btn:hover, 
[data-theme="dark"] .learn-more-btn:hover, 
[data-theme="dark"] .remove-btn:hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.3);
}

[data-theme="light"] .marketplace-btn:hover, 
[data-theme="light"] .learn-more-btn:hover, 
[data-theme="light"] .remove-btn:hover {
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
}

/* 移动端适配 */
@media (max-width: 768px) {
  .sellers-page {
    padding: 2rem 1rem;
  }
  
  .sellers-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 2rem;
  }
  
  .sellers-grid {
    grid-template-columns: 1fr;
  }
  
  .seller-logo {
    width: 80px;
    height: 80px;
  }
  
  .seller-buttons {
    flex-direction: column;
  }
}

/* 添加淡入动画 */
.fade-in {
  animation: fadeIn 0.8s ease forwards;
  opacity: 0;
  animation-delay: calc(var(--index, 0) * 0.1s);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.empty-favorites {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 320px;
  border-radius: 18px;
  margin: 3rem auto 2rem;
  padding: 3rem 2.5rem 2.5rem;
  max-width: 600px;
  transition: all 0.3s ease;
}

[data-theme="dark"] .empty-favorites {
  background: rgba(30, 30, 40, 0.7);
  box-shadow: 0 8px 32px rgba(128, 102, 204, 0.08);
}

[data-theme="light"] .empty-favorites {
  background: rgba(250, 250, 255, 0.8);
  box-shadow: 0 8px 32px rgba(106, 70, 184, 0.05);
}

.empty-icon {
  font-size: 3.2rem;
  margin-bottom: 1.2rem;
  color: #ff3366;
}

[data-theme="dark"] .empty-icon {
  text-shadow: 0 2px 12px rgba(255, 51, 102, 0.15);
}

[data-theme="light"] .empty-icon {
  text-shadow: 0 2px 12px rgba(255, 51, 102, 0.1);
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

[data-theme="dark"] .empty-title {
  color: #c3a3ff;
}

[data-theme="light"] .empty-title {
  color: #6a46b8;
}

.empty-desc {
  font-size: 1.05rem;
  margin-bottom: 1.5rem;
  text-align: center;
  transition: all 0.3s ease;
}

[data-theme="dark"] .empty-desc {
  color: #e0e0e0;
}

[data-theme="light"] .empty-desc {
  color: #444;
}

.browse-btn {
  font-size: 1rem;
  padding: 0.7rem 2.2rem;
  border-radius: 8px;
  font-weight: 600;
  border: none;
  color: #fff;
  transition: background 0.3s;
}

[data-theme="dark"] .browse-btn {
  background: linear-gradient(135deg, #8066cc, #6a46b8);
  box-shadow: 0 2px 8px rgba(128, 102, 204, 0.12);
}

[data-theme="light"] .browse-btn {
  background: linear-gradient(135deg, #6a46b8, #5a46a8);
  box-shadow: 0 2px 8px rgba(106, 70, 184, 0.08);
}

[data-theme="dark"] .browse-btn:hover {
  background: linear-gradient(135deg, #a080e0, #8066cc);
}

[data-theme="light"] .browse-btn:hover {
  background: linear-gradient(135deg, #7a56c8, #6a46b8);
}
</style> 