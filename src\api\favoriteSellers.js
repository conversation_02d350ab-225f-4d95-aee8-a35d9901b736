import apiClient from '@/services/api';

// 模拟数据
const mockFavoriteSellers = [
    {
        id: '1',
        name: 'Luxury Fashion Store',
        description: '提供最新时尚潮流服饰，品质保证11111111111111111111111111111111111111111111111111111',
        logoUrl: 'https://via.placeholder.com/100x100?text=LS',
        type: 'promoted',
        likes: 45,
        bookmarks: 3,
        platformUrl: 'https://example.com/store1',
        websiteUrl: 'https://example.com/about1'
    },
    {
        id: '2',
        name: 'Tech Gadgets Hub',
        description: '最新数码产品，科技前沿',
        logoUrl: 'https://via.placeholder.com/100x100?text=TG',
        type: 'trusted',
        likes: 30,
        bookmarks: 24,
        platformUrl: 'https://example.com/store2',
        websiteUrl: 'https://example.com/about2'
    },
    {
        id: '3',
        name: 'Organic Health Products',
        description: '100%有机认证的健康食品',
        logoUrl: 'https://via.placeholder.com/100x100?text=OH',
        type: 'trusted',
        likes: 23,
        bookmarks: 28,
        platformUrl: 'https://example.com/store3',
        websiteUrl: 'https://example.com/about3'
    },
    {
        id: '4',
        name: 'Home Decor & Lifestyle',
        description: '打造舒适家居生活',
        logoUrl: 'https://via.placeholder.com/100x100?text=HD',
        type: 'promoted',
        likes: 51,
        bookmarks: 15,
        platformUrl: 'https://example.com/store4',
        websiteUrl: 'https://example.com/about4'
    }
];

// 模拟延迟
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * 获取收藏商家列表
 * @param {string} userId 用户ID
 * @returns {Promise<Object>} 收藏商家列表
 */
export const getFavoriteSellers = async (userId) => {
    try {
        // 调用真实后端接口
        const response = await apiClient.get('/sellerCollect/list', { params: { userId } });
        return response;
    } catch (error) {
        console.log('使用模拟数据替代API调用');
        await delay(800); // 模拟网络延迟
        return { data: [] };
    }
};

/**
 * 添加收藏商家
 * @param {string} sellerId 商家ID
 * @returns {Promise<Object>} 操作结果
 */
export const addFavoriteSeller = async (sellerId) => {
    try {
        // 实际API调用
        const response = await apiClient.post('/user/favorite-sellers', { sellerId });
        return response.data;
    } catch (error) {
        console.log('使用模拟数据替代API调用');
        await delay(500); // 模拟网络延迟
        return { success: true, message: 'Seller added to favorites' };
    }
};

/**
 * 移除收藏商家
 * @param {string} sellerId 商家ID
 * @returns {Promise<Object>} 操作结果
 */
export const removeFavoriteSeller = async (sellerId) => {
    try {
        // 实际API调用
        const response = await apiClient.delete(`/user/favorite-sellers/${sellerId}`);
        return response.data;
    } catch (error) {
        console.log('使用模拟数据替代API调用');
        await delay(500); // 模拟网络延迟
        return { success: true, message: 'Seller removed from favorites' };
    }
};

/**
 * 检查商家是否已收藏
 * @param {string} sellerId 商家ID
 * @returns {Promise<boolean>} 是否已收藏
 */
export const checkFavoriteSeller = async (sellerId) => {
    try {
        // 实际API调用
        const response = await apiClient.get(`/user/favorite-sellers/check/${sellerId}`);
        return response.data.isFavorite;
    } catch (error) {
        console.log('使用模拟数据替代API调用');
        await delay(300); // 模拟网络延迟
        // 模拟检查是否在收藏列表中
        return mockFavoriteSellers.some(seller => seller.id === sellerId);
    }
};

export default {
    getFavoriteSellers,
    addFavoriteSeller,
    removeFavoriteSeller,
    checkFavoriteSeller
}; 