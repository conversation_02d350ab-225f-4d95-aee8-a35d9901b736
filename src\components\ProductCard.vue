<template>
  <div
    class="product-card"
    :class="{ 'light-theme': !isDarkMode }"
    :data-product-id="productId"
    @click="navigateToProductDetail"
  >
    <div class="trending-product-container">
      <!-- Product Images Container -->
      <div class="product-images-container">
        <!-- Main large image -->
        <div
          class="main-image-container"
          @mouseenter="handleMouseEnter"
          @mouseleave="handleMouseLeave"
        >
          <!-- 移动端：先显示图片，3秒后显示视频 -->
          <img
            :src="productImage"
            :alt="productName"
            v-show="
              (!isHovering && !isMobile) ||
              !productVideo ||
              (isMobile && showImageFirst)
            "
          />

          <!-- 视频元素 - PC端鼠标悬停时显示，移动端3秒后显示 -->
          <div
            class="video-container"
            v-show="
              (isHovering && !isMobile && productVideo) ||
              (isMobile && productVideo && !showImageFirst)
            "
          >
            <video
              ref="videoElement"
              class="product-video"
              :src="productVideo"
              :style="{ objectFit: videoFitMode }"
              muted
              :loop="!isMobile"
              playsinline
              preload="metadata"
              @ended="onVideoEnded"
            ></video>
          </div>

          <!-- 红色勾选图标 - 当productStatus为1时显示 -->
          <div v-if="productStatusValue === 1" class="check-icon">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              xmlns:xlink="http://www.w3.org/1999/xlink"
              width="20"
              height="20"
              viewBox="0 0 20 20"
              fill="none"
            >
              <path
                d="M7.5924 1.20027C7.34888 1.4078 7.22711 1.51158 7.09706 1.59874C6.79896 1.79854 6.46417 1.93721 6.1121 2.00672C5.95851 2.03705 5.79903 2.04977 5.48008 2.07522C4.6787 2.13918 4.278 2.17115 3.94371 2.28923C3.17051 2.56233 2.56233 3.17051 2.28923 3.94371C2.17115 4.278 2.13918 4.6787 2.07522 5.48008C2.04977 5.79903 2.03705 5.95851 2.00672 6.1121C1.93721 6.46417 1.79854 6.79896 1.59874 7.09706C1.51158 7.22711 1.40781 7.34887 1.20027 7.5924C0.678835 8.20428 0.418104 8.51023 0.265216 8.83011C-0.0884052 9.56995 -0.0884052 10.43 0.265216 11.1699C0.418105 11.4898 0.678827 11.7957 1.20027 12.4076C1.40778 12.6511 1.51158 12.7729 1.59874 12.9029C1.79854 13.201 1.93721 13.5358 2.00672 13.8879C2.03705 14.0415 2.04977 14.201 2.07522 14.5199C2.13918 15.3213 2.17115 15.722 2.28923 16.0563C2.56233 16.8295 3.17051 17.4377 3.94371 17.7108C4.278 17.8288 4.6787 17.8608 5.48008 17.9248C5.79903 17.9502 5.95851 17.963 6.1121 17.9933C6.46417 18.0628 6.79896 18.2015 7.09706 18.4013C7.22711 18.4884 7.34887 18.5922 7.5924 18.7997C8.20429 19.3212 8.51023 19.5819 8.83011 19.7348C9.56995 20.0884 10.43 20.0884 11.1699 19.7348C11.4898 19.5819 11.7957 19.3212 12.4076 18.7997C12.6511 18.5922 12.7729 18.4884 12.9029 18.4013C13.201 18.2015 13.5358 18.0628 13.8879 17.9933C14.0415 17.963 14.201 17.9502 14.5199 17.9248C15.3213 17.8608 15.722 17.8288 16.0563 17.7108C16.8295 17.4377 17.4377 16.8295 17.7108 16.0563C17.8288 15.722 17.8608 15.3213 17.9248 14.5199C17.9502 14.201 17.963 14.0415 17.9933 13.8879C18.0628 13.5358 18.2015 13.201 18.4013 12.9029C18.4884 12.7729 18.5922 12.6511 18.7997 12.4076C19.3212 11.7957 19.5819 11.4898 19.7348 11.1699C20.0884 10.43 20.0884 9.56995 19.7348 8.83011C19.5819 8.51023 19.3212 8.20429 18.7997 7.5924C18.5922 7.34887 18.4884 7.22711 18.4013 7.09706C18.2015 6.79896 18.0628 6.46417 17.9933 6.1121C17.963 5.95851 17.9502 5.79903 17.9248 5.48008C17.8608 4.6787 17.8288 4.278 17.7108 3.94371C17.4377 3.17051 16.8295 2.56233 16.0563 2.28923C15.722 2.17115 15.3213 2.13918 14.5199 2.07522C14.201 2.04977 14.0415 2.03705 13.8879 2.00672C13.5358 1.93721 13.201 1.79854 12.9029 1.59874C12.7729 1.51158 12.6511 1.40781 12.4076 1.20027C11.7957 0.678828 11.4898 0.418105 11.1699 0.265216C10.43 -0.0884052 9.56995 -0.0884052 8.83011 0.265216C8.51023 0.418105 8.20428 0.678833 7.5924 1.20027ZM14.3735 7.86314C14.6913 7.5453 14.6913 7.03 14.3735 6.71216C14.0557 6.39433 13.5403 6.39433 13.2225 6.71216L8.37227 11.5624L6.77746 9.9676C6.45963 9.64977 5.94432 9.64977 5.62649 9.9676C5.30866 10.2854 5.30866 10.8007 5.62649 11.1186L7.79678 13.2889C8.11461 13.6067 8.62992 13.6067 8.94775 13.2889L14.3735 7.86314Z"
                fill-rule="evenodd"
                style="mix-blend-mode: normal"
                fill="#F22C2C"
              ></path>
            </svg>
          </div>

          <!-- 图片2 - 当productStatus为2时显示 -->
          <div v-if="productStatusValue === 2" class="tag-icon">
            <img src="@/assets/2.png" alt="Status icon" class="status-image" />
          </div>
        </div>
      </div>

      <!-- Product info section with price -->
      <div class="product-info-section">
        <div class="price-row">
          <p class="current-price">{{ formattedPrice }}</p>
          <div class="product-attributes" v-if="productAttributes">
            {{ productAttributes }}
          </div>
          <div class="product-id">{{ productPlatform }}</div>
          <div class="favorite-icon" @click.stop="handleCollect">
            <i
              :class="[isCollected ? 'fas fa-heart collected' : 'far fa-heart']"
            ></i>
          </div>
        </div>

        <h3 class="product-name">{{ productName }}</h3>

        <!-- Social Stats -->
        <div class="social-stats">
          <div class="stat-container" @click.stop="handleLike">
            <i
              :class="[isLiked ? 'fas fa-thumbs-up liked' : 'far fa-thumbs-up']"
            ></i>
            <span style="margin-left: 5px">{{ productLikes }}</span>
          </div>
          <div class="stat-container">
            <i class="far fa-image"></i>
            <span style="margin-left: 5px">{{ productViews }}</span>
          </div>
          <div class="stat-container">
            <i class="fas fa-comment"></i>
            <span style="margin-left: 5px">{{ productComments }}</span>
          </div>
          <div class="stat-container">
            <i class="far fa-eye"></i>
            <span style="margin-left: 5px">{{ virtualViews }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { addCollection, removeFavorite } from "@/api/favorites";
import { ElMessage } from "element-plus";
import productsApi from "@/api/products";
import { useRouter } from "vue-router";

// 全局视频管理器 - 重构移动端视频播放逻辑
const MobileVideoManager = {
  playingVideos: new Map(), // 当前播放的视频集合 {productId: {timestamp, element}}
  maxConcurrentVideos: 2, // 最大同时播放视频数量
  currentViewportCenter: null, // 当前视口中心位置
  scrollTimeout: null, // 滚动防抖定时器
  isScrolling: false, // 是否正在滚动

  // 添加播放中的视频
  addPlayingVideo(productId, element) {
    this.playingVideos.set(productId, {
      timestamp: Date.now(),
      element: element
    });
    console.log(
      `🎬 添加播放视频: ${productId}, 当前播放数量: ${this.playingVideos.size}`
    );
  },

  // 移除播放中的视频
  removePlayingVideo(productId) {
    this.playingVideos.delete(productId);
    console.log(
      `⏹️ 移除播放视频: ${productId}, 当前播放数量: ${this.playingVideos.size}`
    );
  },

  // 停止所有正在播放的视频
  stopAllPlayingVideos() {
    console.log(`🛑 停止所有正在播放的视频 (${this.playingVideos.size}个)`);

    for (const [productId] of this.playingVideos) {
      // 发送停止事件
      window.dispatchEvent(
        new CustomEvent("forceStopVideo", {
          detail: { productId, reason: "scroll" },
        })
      );
    }

    this.playingVideos.clear();
  },

  // 检查元素是否在视口中心区域
  isInCenterViewport(element) {
    if (!element) return false;

    const rect = element.getBoundingClientRect();
    const viewportHeight = window.innerHeight;
    const viewportWidth = window.innerWidth;

    // 定义中心区域：视口高度的中间40%，宽度的中间80%
    const centerTop = viewportHeight * 0.3;
    const centerBottom = viewportHeight * 0.7;
    const centerLeft = viewportWidth * 0.1;
    const centerRight = viewportWidth * 0.9;

    // 检查元素是否在中心区域
    const isInVerticalCenter = rect.top < centerBottom && rect.bottom > centerTop;
    const isInHorizontalCenter = rect.left < centerRight && rect.right > centerLeft;

    return isInVerticalCenter && isInHorizontalCenter;
  },

  // 获取视口中心区域的所有商品元素
  getCenterViewportProducts() {
    const productCards = document.querySelectorAll('.product-card');
    const centerProducts = [];

    productCards.forEach(card => {
      if (this.isInCenterViewport(card)) {
        const productId = card.getAttribute('data-product-id');
        if (productId) {
          centerProducts.push({
            productId,
            element: card,
            rect: card.getBoundingClientRect()
          });
        }
      }
    });

    // 按垂直位置排序，然后按水平位置排序
    centerProducts.sort((a, b) => {
      const rowDiff = Math.floor(a.rect.top / 100) - Math.floor(b.rect.top / 100);
      if (rowDiff !== 0) return rowDiff;
      return a.rect.left - b.rect.left;
    });

    return centerProducts;
  },

  // 处理滚动事件
  handleScroll() {
    // 设置滚动状态
    this.isScrolling = true;

    // 清除之前的定时器
    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
    }

    // 立即停止所有正在播放的视频
    this.stopAllPlayingVideos();

    // 设置防抖定时器，滚动停止后重新检测
    this.scrollTimeout = setTimeout(() => {
      this.isScrolling = false;
      this.checkAndPlayCenterVideos();
    }, 300); // 滚动停止300ms后重新检测
  },

  // 检查并播放中心区域的视频
  checkAndPlayCenterVideos() {
    if (this.isScrolling) return;

    const centerProducts = this.getCenterViewportProducts();
    console.log(`📍 检测到中心区域商品: ${centerProducts.length}个`);

    // 只播放前两个（同一行的两个商品）
    const productsToPlay = centerProducts.slice(0, this.maxConcurrentVideos);

    productsToPlay.forEach(product => {
      // 发送播放事件
      window.dispatchEvent(
        new CustomEvent("startCenterVideo", {
          detail: { productId: product.productId },
        })
      );
    });
  },

  // 初始化滚动监听
  initScrollListener() {
    // 移除之前的监听器
    if (this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler);
    }

    // 创建防抖的滚动处理器
    this.scrollHandler = this.throttle(() => {
      this.handleScroll();
    }, 100);

    // 添加滚动监听
    window.addEventListener('scroll', this.scrollHandler, { passive: true });

    console.log('📱 移动端滚动监听器已初始化');
  },

  // 节流函数
  throttle(func, limit) {
    let inThrottle;
    return function() {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    }
  },

  // 清理滚动监听
  cleanupScrollListener() {
    if (this.scrollHandler) {
      window.removeEventListener('scroll', this.scrollHandler);
      this.scrollHandler = null;
    }

    if (this.scrollTimeout) {
      clearTimeout(this.scrollTimeout);
      this.scrollTimeout = null;
    }
  },

  // 获取当前播放视频数量
  getPlayingCount() {
    return this.playingVideos.size;
  },

  // 获取播放状态信息
  getStatus() {
    const centerProducts = this.getCenterViewportProducts();
    return {
      playing: Array.from(this.playingVideos.keys()),
      playingCount: this.playingVideos.size,
      maxConcurrent: this.maxConcurrentVideos,
      isScrolling: this.isScrolling,
      centerProducts: centerProducts.map(p => p.productId)
    };
  },

  // 打印状态信息
  logStatus() {
    const status = this.getStatus();
    console.log('📊 MobileVideoManager 状态:', status);
  },

  // 清理所有状态（用于调试）
  reset() {
    this.stopAllPlayingVideos();
    this.cleanupScrollListener();
    console.log("🔄 MobileVideoManager 已重置");
  },

  // 初始化管理器
  init() {
    this.initScrollListener();
    // 初始检测
    setTimeout(() => {
      this.checkAndPlayCenterVideos();
    }, 1000);
  },
};

export default {
  name: "ProductCard",
  props: {
    product: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const router = useRouter();

    // 导航到产品详情页
    const navigateToProductDetail = () => {
      const productId =
        props.product.id ||
        props.product.productId ||
        props.product.itemId ||
        "";
      router.push({ name: "product-detail", params: { id: productId } });
    };

    return {
      navigateToProductDetail,
    };
  },
  data() {
    return {
      isDarkMode: true,
      isCollected: false,
      isLiked: false,
      localLikes: 0,
      isHovering: false, // 控制视频是否显示
      videoElement: null, // 存储视频元素
      isMobile: false, // 检测是否为移动设备
      // 移动端视频播放控制
      showImageFirst: true, // 移动端先显示图片
      imageDisplayTimer: null, // 图片显示定时器
      isVideoPlaying: false, // 视频是否正在播放
      videoPlayPromise: null, // 视频播放Promise
      isInPlaybackProcess: false, // 是否正在播放流程中（包括2秒等待期）
      isInCenterViewport: false, // 是否在视口中心区域
    };
  },
  created() {
    this.isCollected = this.product.isCollected || false;
    this.isLiked = this.product.isLiked || false;
    this.localLikes = this.product.likes || 0;
  },
  computed: {
    // 确保即使API返回的数据格式不同，组件也能正确显示
    productImage() {
      return (
        this.product.mainImage ||
        this.product.image ||
        this.product.imageUrl ||
        this.product.thumbnail ||
        ""
      );
    },
    productPrice() {
      // 处理不同格式的价格数据
      const price =
        this.product.price ||
        this.product.salePrice ||
        this.product.newPrice ||
        "0";
      return typeof price === "number" ? price.toFixed(2) : price;
    },
    formattedPrice() {
      // 现在后台录入的是人民币价格，需要转换为美元显示
      const rmbPrice = parseFloat(this.productPrice);
      const exchangeRate = 6.5; // 默认汇率，可以根据需要调整
      const usdPrice = (rmbPrice / exchangeRate).toFixed(2); // 确保保留两位小数
      return `$${usdPrice}`;
    },
    productId() {
      return (
        this.product.productId || this.product.id || this.product.itemId || ""
      );
    },
    productPlatform() {
      // 获取原始平台名称
      const platform = this.product.platform || "OMG";

      // 根据平台名称返回对应的代码
      if (platform === "微店") return "weidian";
      if (platform === "淘宝") return "taobao";
      if (platform === "1688") return "1688";
      if (platform === "OMG") return "OMG";

      // 其他平台直接返回原始值
      return platform;
    },
    productName() {
      // 确保优先获取商品名称，截图中可能没有显示名称
      return (
        this.product.name ||
        this.product.title ||
        this.product.productName ||
        ""
      );
    },
    productLikes() {
      return (
        this.localLikes || this.product.likes || this.product.likeCount || "0"
      );
    },
    productViews() {
      return this.product.qc || this.product.qc || "0";
    },
    productComments() {
      // 如果comments是数组，则返回数组长度
      if (Array.isArray(this.product.comments)) {
        return this.product.comments.length;
      }
      // 否则返回commentCount属性或默认值0
      return this.product.commentCount || "0";
    },
    virtualViews() {
      // 计算总浏览量（真实浏览量 + 虚拟浏览量）
      const realViews = parseInt(this.product.views || 0);
      const virtualViews = parseInt(this.product.virtualViews || 0);
      const totalViews = realViews + virtualViews;
      return totalViews.toString();
    },
    productVideo() {
      // 获取视频URL，如果不存在则返回空字符串
      return this.product.videoUrl || "";
    },
    videoFitMode() {
      // 获取视频适配模式，默认为contain
      return this.product.videoFitMode || "contain";
    },
    productStatusValue() {
      // 检查多种可能的字段名称，确保处理为整型
      const status =
        this.product.productStatus ||
        this.product.status_id ||
        this.product.statusId ||
        0;
      // 转换为数字类型
      return Number(status);
    },
    productAttributes() {
      // 获取商品属性，支持多种可能的字段名称
      return (
        this.product.productAttributes ||
        this.product.attributes ||
        this.product.attr ||
        ""
      );
    },
  },
  mounted() {
    // 检查当前主题
    this.checkCurrentTheme();

    // 检测移动设备
    this.checkMobileDevice();

    // 添加主题变化监听
    document.addEventListener("themechange", this.checkCurrentTheme);

    // 添加窗口大小变化监听
    window.addEventListener("resize", this.checkMobileDevice);

    // 如果存在emitter，也添加监听
    if (window.emitter) {
      window.emitter.on("theme-changed", this.handleThemeChange);
    }

    // 监听HTML元素的data-theme属性变化
    const htmlElement = document.documentElement;
    const themeObserver = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.attributeName === "data-theme") {
          this.checkCurrentTheme();
        }
      });
    });

    themeObserver.observe(htmlElement, {
      attributes: true,
      attributeFilter: ["data-theme"],
    });

    // 获取视频元素引用
    this.$nextTick(() => {
      if (this.$refs.videoElement) {
        this.videoElement = this.$refs.videoElement;
      }
    });

    // 存储观察器引用以便在组件卸载时清理
    this.themeObserver = themeObserver;

    // 添加移动端视频事件监听器
    this.setupMobileVideoListeners();

    // 将视频管理器暴露到全局，方便调试
    if (!window.MobileVideoManager) {
      window.MobileVideoManager = MobileVideoManager;
      console.log('🔧 MobileVideoManager 已暴露到全局，可使用 window.MobileVideoManager.logStatus() 查看状态');

      // 初始化管理器（只初始化一次）
      MobileVideoManager.init();
    }
  },
  beforeUnmount() {
    // 清理事件监听器
    document.removeEventListener("themechange", this.checkCurrentTheme);
    window.removeEventListener("resize", this.checkMobileDevice);
    if (window.emitter) {
      window.emitter.off("theme-changed", this.handleThemeChange);
    }

    // 清理观察器
    if (this.themeObserver) {
      this.themeObserver.disconnect();
    }

    // 清理移动端视频事件监听器
    this.cleanupMobileVideoListeners();

    // 清理定时器
    if (this.imageDisplayTimer) {
      clearTimeout(this.imageDisplayTimer);
    }

    // 从全局管理器中移除当前视频
    if (this.isVideoPlaying) {
      MobileVideoManager.removePlayingVideo(this.productId);
    }
  },
  methods: {
    // 检测当前主题
    checkCurrentTheme() {
      // 检查HTML元素的data-theme属性
      const htmlElement = document.documentElement;
      const theme = htmlElement.getAttribute("data-theme");
      this.isDarkMode = theme === "dark";
    },

    // 处理主题变化
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode !== "undefined") {
        this.isDarkMode = data.isDarkMode;
      }
    },

    // 添加点击处理方法
    handleClick() {
      // 触发点击事件，让父组件处理导航
      this.$emit("click", this.productId);
    },

    // 处理收藏/取消收藏
    async handleCollect(event) {
      // 阻止事件冒泡,避免点击收藏图标时跳转到产品详情
      event.stopPropagation();

      // 如果用户未登录,提示登录
      if (!localStorage.getItem("isLoggedIn")) {
        this.showToast("Please login first to add to favorites", "warning");
        this.$router.push("/login");
        return;
      }

      try {
        if (this.isCollected) {
          // 取消收藏
          await removeFavorite(this.productId);
          this.isCollected = false;
          this.showToast("Removed from favorites");
        } else {
          // 添加收藏
          await addCollection(this.productId);
          this.isCollected = true;
          this.showToast("Added to favorites");
        }

        // 触发事件通知父组件收藏状态已更新
        this.$emit("collect-updated", {
          ...this.product,
          isCollected: this.isCollected,
        });
      } catch (error) {
        console.error("收藏操作失败:", error);
        this.showToast(
          this.isCollected
            ? "Failed to remove from favorites"
            : "Failed to add to favorites",
          "error"
        );
      }
    },

    // 处理点赞/取消点赞
    async handleLike(event) {
      // 阻止事件冒泡,避免点击点赞图标时跳转到产品详情
      event.stopPropagation();

      // 如果用户未登录,提示登录
      if (!localStorage.getItem("isLoggedIn")) {
        this.showToast("Please login first to like", "warning");
        this.$router.push("/login");
        return;
      }

      try {
        if (this.isLiked) {
          // 取消点赞
          await productsApi.unlikeProduct(this.productId);
          this.isLiked = false;
          // 减少点赞数
          this.localLikes = Math.max(0, parseInt(this.localLikes) - 1);
          this.showToast("Unlike successful");
        } else {
          // 检查用户今天是否已经点赞过该商品
          const checkResponse = await productsApi.checkLikeToday(
            this.productId
          );

          if (
            checkResponse.code === 200 &&
            checkResponse.data &&
            checkResponse.data.isLikedToday
          ) {
            this.showToast(
              "You have already liked this product today",
              "warning"
            );
            return;
          }

          // 点赞
          await productsApi.likeProduct(this.productId);
          this.isLiked = true;
          // 增加点赞数
          this.localLikes = parseInt(this.localLikes) + 1;
          this.showToast("Like successful");
        }

        // 触发事件通知父组件点赞状态已更新
        this.$emit("like-updated", {
          ...this.product,
          isLiked: this.isLiked,
          likes: this.localLikes,
        });
      } catch (error) {
        console.error("点赞操作失败:", error);
        this.showToast(this.isLiked ? "Unlike failed" : "Like failed", "error");
      }
    },

    // 显示提示消息
    showToast(message, type = "success") {
      ElMessage({
        message,
        type,
        duration: 2000,
      });
    },

    // 处理鼠标进入事件
    handleMouseEnter() {
      // 移动端不处理鼠标事件，使用滚动播放
      if (this.isMobile) return;

      this.isHovering = true;
      console.log(
        `鼠标悬停在商品 ${this.productId} 上，视频URL: ${this.productVideo}`
      );

      // 确保获取到最新的视频元素
      this.$nextTick(() => {
        this.videoElement = this.$refs.videoElement;

        // 如果视频元素存在，则播放
        if (this.videoElement) {
          console.log(`找到视频元素，尝试播放`);
          // 确保视频已加载
          this.videoElement.load();
          this.videoElement.play().catch((e) => {
            console.error(`视频播放失败:`, e);
          });
        } else {
          console.warn(`未找到视频元素，productVideo = ${this.productVideo}`);
        }
      });
    },

    // 处理鼠标离开事件
    handleMouseLeave() {
      // 移动端不处理鼠标事件，使用滚动播放
      if (this.isMobile) return;

      this.isHovering = false;

      // 如果视频元素存在，则暂停
      if (this.videoElement) {
        console.log(`鼠标离开，暂停视频`);
        this.videoElement.pause();
      }
    },

    // 检测移动设备
    checkMobileDevice() {
      // 更准确的移动设备检测
      const isMobileWidth = window.innerWidth <= 768;
      const isTouchDevice = 'ontouchstart' in window || navigator.maxTouchPoints > 0;
      const isMobileUserAgent = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

      this.isMobile = isMobileWidth || (isTouchDevice && isMobileUserAgent);

      console.log(`📱 设备检测结果:
        宽度检测: ${isMobileWidth} (${window.innerWidth}px)
        触摸设备: ${isTouchDevice}
        移动UA: ${isMobileUserAgent}
        最终结果: ${this.isMobile}`);
    },

    // 设置移动端视频事件监听器
    setupMobileVideoListeners() {
      if (!this.isMobile || !this.productVideo) return;

      // 监听强制停止视频事件
      this.handleForceStopVideoEvent = (event) => {
        if (event.detail.productId === this.productId) {
          console.log(`📢 收到强制停止视频事件: ${this.productId}, 原因: ${event.detail.reason || 'unknown'}`);
          this.forceStopVideo();
        }
      };
      window.addEventListener("forceStopVideo", this.handleForceStopVideoEvent);

      // 监听开始播放中心视频事件
      this.handleStartCenterVideoEvent = (event) => {
        if (event.detail.productId === this.productId) {
          console.log(`📢 收到开始播放中心视频事件: ${this.productId}`);
          this.startCenterVideoPlayback();
        }
      };
      window.addEventListener("startCenterVideo", this.handleStartCenterVideoEvent);
    },

    // 清理移动端视频事件监听器
    cleanupMobileVideoListeners() {
      if (this.handleForceStopVideoEvent) {
        window.removeEventListener("forceStopVideo", this.handleForceStopVideoEvent);
        this.handleForceStopVideoEvent = null;
      }

      if (this.handleStartCenterVideoEvent) {
        window.removeEventListener("startCenterVideo", this.handleStartCenterVideoEvent);
        this.handleStartCenterVideoEvent = null;
      }
    },

    // 开始中心视频播放流程
    startCenterVideoPlayback() {
      if (!this.isMobile || !this.productVideo) return;

      // 如果已经在播放流程中，不重复触发
      if (this.isInPlaybackProcess) {
        console.log(`⚠️ 视频已在播放流程中: ${this.productId}`);
        return;
      }

      // 检查当前播放数量是否已达上限
      if (MobileVideoManager.getPlayingCount() >= MobileVideoManager.maxConcurrentVideos) {
        console.log(`⚠️ 已达到最大播放数量，无法播放: ${this.productId}`);
        return;
      }

      console.log(`🎬 开始中心视频播放流程: ${this.productId}`);

      // 标记为播放流程中
      this.isInPlaybackProcess = true;

      // 重置状态
      this.showImageFirst = true;
      this.isHovering = true; // 显示容器

      // 清除之前的定时器
      if (this.imageDisplayTimer) {
        clearTimeout(this.imageDisplayTimer);
      }

      // 2秒后切换到视频（根据需求改为2秒）
      this.imageDisplayTimer = setTimeout(() => {
        // 检查是否还在中心区域
        if (!MobileVideoManager.isInCenterViewport(this.$el)) {
          console.log(`⚠️ 视频已离开中心区域，取消播放: ${this.productId}`);
          this.isInPlaybackProcess = false;
          return;
        }

        this.showImageFirst = false;
        this.startVideoPlayback();
      }, 2000); // 改为2秒
    },

    // 开始视频播放
    startVideoPlayback() {
      if (!this.isMobile || !this.productVideo) return;

      // 最后一次检查播放数量
      if (MobileVideoManager.getPlayingCount() >= MobileVideoManager.maxConcurrentVideos) {
        console.log(`🚫 播放数量已达上限，无法播放视频: ${this.productId}`);
        this.isInPlaybackProcess = false;
        return;
      }

      this.$nextTick(() => {
        this.videoElement = this.$refs.videoElement;

        if (this.videoElement) {
          console.log(`🎥 移动端开始播放视频: ${this.productId}`);

          // 添加到全局管理器
          MobileVideoManager.addPlayingVideo(this.productId, this.videoElement);
          this.isVideoPlaying = true;

          // 设置视频事件监听
          this.videoElement.addEventListener("ended", () => {
            console.log(`✅ 视频播放完成: ${this.productId}`);
            this.onVideoEnded();
          });

          this.videoElement.load();
          this.videoPlayPromise = this.videoElement.play().catch((e) => {
            console.error(`❌ 移动端视频播放失败:`, e);
            this.isVideoPlaying = false;
            this.isInPlaybackProcess = false;
            MobileVideoManager.removePlayingVideo(this.productId);
          });
        }
      });
    },

    // 视频播放结束处理
    onVideoEnded() {
      console.log(`🏁 视频播放结束: ${this.productId}`);

      // 从播放列表中移除
      MobileVideoManager.removePlayingVideo(this.productId);

      // 重置状态
      this.isVideoPlaying = false;
      this.isInPlaybackProcess = false;
      this.showImageFirst = true;
      this.isHovering = false;

      // 视频播放完成后，检查是否有新的中心视频需要播放
      setTimeout(() => {
        MobileVideoManager.checkAndPlayCenterVideos();
      }, 500);
    },

    // 强制停止视频（由全局管理器调用）
    forceStopVideo() {
      if (!this.isMobile) return;

      console.log(`🚨 强制停止视频: ${this.productId}`);

      // 清除定时器
      if (this.imageDisplayTimer) {
        clearTimeout(this.imageDisplayTimer);
        this.imageDisplayTimer = null;
      }

      // 停止视频
      if (this.videoElement && this.isVideoPlaying) {
        this.videoElement.pause();
        this.isVideoPlaying = false;
      }

      // 重置所有状态
      this.isInPlaybackProcess = false;
      this.showImageFirst = true;
      this.isHovering = false;

      // 注意：这里不调用 MobileVideoManager.removePlayingVideo
      // 因为是由管理器主动调用的，避免重复操作
    },
  },
};
</script>

<style scoped>
.product-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  cursor: pointer;
  border: 1px solid #bf52e3;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}

.product-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.25);
  border-color: #8e2de2;
}

/* 浅色主题样式 */
.product-card.light-theme {
  background: #ffffff;
  border: 1px solid #e0c6f5;
  box-shadow: 0 4px 12px rgba(142, 45, 226, 0.1);
}

.product-card.light-theme:hover {
  box-shadow: 0 12px 24px rgba(142, 45, 226, 0.15);
  border-color: #bf52e3;
}

.trending-product-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  flex: 1;
}

/* Product Images Container */
.product-images-container {
  width: 100%;
  background: #fff;
  padding: 0;
}

/* 视频样式 */
.product-video {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain; /* 改为contain，确保整个视频都可见，可能会有黑边 */
  background-color: #000; /* 添加黑色背景，使黑边不那么明显 */
  z-index: 1;
}

/* 添加视频容器的额外样式 */
.video-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 1;
}

.main-image-container {
  width: 100%;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fff;
  padding: 0;
  min-height: 260px;
  overflow: hidden;
  aspect-ratio: 1; /* 保持正方形比例，确保一致的显示效果 */
}

.main-image-container img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
}

/* Tag Icon */
.tag-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
}

.status-image {
  width: 21px;
  height: 21px;
  display: block;
}

/* 红色圆形图标 */
.check-icon {
  position: absolute;
  top: 10px;
  left: 10px;
  z-index: 2;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Product Info Section */
.product-info-section {
  padding: 15px;
  background: linear-gradient(to left, #a742c6 0%, #a742c6 10%, #5d1d8c 100%);
  color: white;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

/* 浅色主题产品信息区域 */
.light-theme .product-info-section {
  background: rgba(240, 213, 252, 1);
  color: #333;
}

.price-row {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 8px;
}

.current-price {
  font-size: 28px;
  font-weight: 700;
  color: #bf52e3 !important;
  margin: 0;
  display: flex;
  align-items: center;
}

/* 浅色主题价格 */
.light-theme .current-price {
  color: #8e2de2 !important;
}

/* 商品属性标签 */
.product-attributes {
  display: inline-block;
  background: rgba(255, 165, 0, 0.2); /* 橙色背景，区别于平台标签 */
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
  border: 1px solid rgba(255, 165, 0, 0.3);
}

.product-id {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  margin-left: 8px;
}

/* 浅色主题商品属性 */
.light-theme .product-attributes {
  background: rgba(255, 165, 0, 0.15);
  color: #d2691e;
  border-color: rgba(255, 165, 0, 0.4);
}

/* 浅色主题产品ID */
.light-theme .product-id {
  background: rgba(142, 45, 226, 0.2);
  color: #333;
}

.favorite-icon {
  margin-left: auto;
  font-size: 20px;
  color: #ddd;
  opacity: 0.8;
  cursor: pointer;
  transition: all 0.3s ease;
}

.favorite-icon:hover {
  transform: scale(1.1);
}

.favorite-icon .collected {
  color: #ff71ce !important;
}

/* 浅色主题收藏图标 */
.light-theme .favorite-icon {
  color: #999;
}

.light-theme .favorite-icon .collected {
  color: #ff71ce !important;
}

.product-name {
  font-size: 14px;
  font-weight: normal;
  color: #fff;
  margin: 0 0 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  opacity: 0.9;
}

/* 浅色主题产品名称 */
.light-theme .product-name {
  color: #333;
  opacity: 0.85;
}

/* Social Stats */
.social-stats {
  display: flex;
  justify-content: flex-start;
  gap: 20px;
  align-items: center;
  font-size: 13px;
  margin-top: auto; /* 推到底部 */
}

.social-stats .stat-container {
  display: flex;
  align-items: center;
  margin-right: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.social-stats .stat-container:hover {
  transform: translateY(-2px);
}

.liked {
  color: #ff6b6b;
}

.stat-container i {
  color: rgba(255, 255, 255, 0.6);
  font-size: 14px;
}

/* 浅色主题社交状态 */
.light-theme .stat-container {
  color: rgba(51, 51, 51, 0.8);
}

.light-theme .stat-container i {
  color: rgba(142, 45, 226, 0.6);
}

/* 响应式样式调整 */
@media (max-width: 768px) {
  .main-image-container {
    min-height: 180px;
  }

  .current-price {
    font-size: 20px;
  }

  .product-info-section {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .main-image-container {
    min-height: 140px;
  }

  .product-info-section {
    padding: 4px;
  }

  .current-price {
    font-size: 18px;
  }

  .product-attributes {
    font-size: 10px;
    padding: 1px 6px;
    margin-left: 6px;
  }

  .product-id {
    font-size: 9px;
    padding: 1px 3px;
  }

  .social-stats {
    gap: 6px;
    font-size: 11px;
  }

  .product-title {
    font-size: 12px;
    line-height: 1.2;
  }
}

/* 针对更小屏幕的优化 */
@media (max-width: 360px) {
  .main-image-container {
    min-height: 120px;
  }

  .product-info-section {
    padding: 3px;
  }

  .current-price {
    font-size: 16px;
  }

  .product-title {
    font-size: 11px;
    line-height: 1.1;
  }

  .social-stats {
    gap: 4px;
    font-size: 10px;
  }

  .product-id {
    font-size: 8px;
    padding: 1px 2px;
  }
}
</style>
