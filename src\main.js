import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import Toast from 'vue-toastification'
import 'vue-toastification/dist/index.css'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'

// 导入i18n国际化配置
import i18n from './i18n'
import { removePurpleLine } from '@/utils/removePurpleLine' // 导入移除紫色横线的工具

// 执行移除紫色横线的方法
removePurpleLine()

const app = createApp(App)

const toastOptions = {
  position: "top-right",
  timeout: 3000,
  closeOnClick: true,
  pauseOnFocusLoss: true,
  pauseOnHover: true,
  draggable: true,
  draggablePercent: 0.6,
  showCloseButtonOnHover: false,
  hideProgressBar: true,
  closeButton: "button",
  icon: true,
  rtl: false
}

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

// 先使用i18n，再使用router和store
app.use(i18n)
app.use(store)
app.use(router)
app.use(ElementPlus)
app.use(Toast, toastOptions)

app.mount('#app')
