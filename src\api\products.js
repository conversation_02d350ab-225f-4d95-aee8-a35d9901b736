import apiClient from '@/services/api';

/**
 * 通用API响应处理程序
 * 提供标准化的错误处理和响应格式
 */
const handleApiResponse = async (apiCall) => {
  try {
    const response = await apiCall();

    // 检查response是否为undefined或null
    if (!response) {
      console.error('API响应为空');
      return {
        code: 500,
        msg: 'API响应为空',
        data: null
      };
    }

    // 检查response.data是否存在，兼容不同的API响应格式
    if (response.data !== undefined) {
      return {
        code: response.data.code || 200,
        msg: response.data.msg || 'Success',
        data: response.data.data || response.data
      };
    } else {
      // 直接使用response作为数据，适用于拦截器已经提取了data的情况
      return {
        code: response.code || 200,
        msg: response.msg || 'Success',
        data: response.data || response
      };
    }
  } catch (error) {
    console.error('API请求错误:', error);
    return {
      code: error.response?.data?.code || error.response?.status || 500,
      msg: error.response?.data?.msg || error.message || '服务器错误',
      data: null
    };
  }
};

/**
 * 商品相关API接口
 */
const productsApi = {
  /**
   * 获取所有商品分类
   * @returns {Promise} 分类列表
   */
  getAllCategories() {
    return handleApiResponse(() => apiClient.get('/omg/categories/all'));
  },

  /**
   * 获取分类树形结构
   * @returns {Promise} 分类树结构
   */
  getCategoryTree() {
    return handleApiResponse(() => apiClient.get('/categories/tree'));
  },

  /**
   * 根据分类获取商品列表
   * @param {number} categoryId 分类ID
   * @param {number} brandId 品牌ID
   * @param {Object} params 其他参数
   * @returns {Promise} 商品列表
   */
  getProductsByCategory(categoryId, brandId, params = {}) {
    // 构建查询参数
    const queryParams = {
      ...params // 确保params中的所有属性都被复制
    };
    
    // 仅在非null或undefined时添加categoryId和brandId
    if (categoryId !== null && categoryId !== undefined) {
      queryParams.categoryId = categoryId;
    }
    
    if (brandId !== null && brandId !== undefined) {
      queryParams.brandId = brandId;
    }
    
    // 日志输出完整请求参数
    console.log('🔍 getProductsByCategory API调用，参数:', queryParams);
    
    // 构建完整URL以便调试
    const url = '/omg/products/categories';
    const queryString = Object.entries(queryParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
    console.log(`🔗 完整请求URL: ${url}?${queryString}`);
    
    return handleApiResponse(() => apiClient.get(url, {
      params: queryParams
    }));
  },

  /**
   * 获取包含子分类的所有商品
   * @param {number} categoryId 分类ID
   * @returns {Promise} 商品列表（包含子分类商品）
   */
  getProductsByCategoryWithChildren(categoryId) {
    return handleApiResponse(() => apiClient.get(`/products/category/${categoryId}/with-children`));
  },

  /**
   * 获取热门商品(按点赞数排序)
   * @param {number} limit 返回记录数量限制
   * @returns {Promise} 热门商品列表
   */
  getHotProducts(limit = 10) {
    return handleApiResponse(() => apiClient.get('/products/hot', { params: { limit } }));
  },

  /**
   * 获取最新商品(按创建时间排序)
   * @param {number} limit 返回记录数量限制
   * @returns {Promise} 最新商品列表
   */
  getLatestProducts(limit = 10) {
    return handleApiResponse(() => apiClient.get('/products/latest', { params: { limit } }));
  },

  /**
   * 获取推荐商品
   * @param {number} userId 用户ID
   * @param {number} [categoryId] 分类ID，可选
   * @param {number} [brandId] 品牌ID，可选
   * @param {number} [limit] 返回记录数量，可选
   * @returns {Promise} 推荐商品列表
   */
  getRecommendProducts(userId, categoryId, brandId, limit) {
    const params = {};

    if (userId) {
      params.userId = userId;
    }
    if (categoryId) {
      params.categoryId = categoryId;
    }
    if (brandId) {
      params.brandId = brandId;
    }
    if (limit) {
      params.limit = limit;
    }
    return handleApiResponse(() => apiClient.get('/omg/products/recommend', { params }));
  },

  /**
   * 关键词搜索商品
   * @param {Object} params 搜索参数
   * @param {string} [params.keyword] 搜索关键词
   * @param {string} [params.categoryId] 分类ID
   * @param {number} [params.minPrice] 最低价格
   * @param {number} [params.maxPrice] 最高价格
   * @param {number} [params.page=1] 页码
   * @param {number} [params.pageSize=20] 每页数量
   * @returns {Promise} 搜索结果商品列表
   */
  searchProducts(params = {}) {
    // 设置默认分页参数
    const searchParams = {
      page: 1,
      pageSize: 20,
      ...params
    };
    return handleApiResponse(() => apiClient.post('/omg/products/search', searchParams));
  },

  /**
   * 获取商品详情
   * @param {number} productId 商品ID
   * @returns {Promise} 商品详情
   */
  getProductDetail(productId) {
    console.log(`Requesting product details, ID: ${productId}`);
    return handleApiResponse(() => {
      // 创建专门的axios配置，增加超时时间
      return apiClient.get(`/omg/products/${productId}`, {
        timeout: 45000, // 45秒超时，比全局的30秒更长
        headers: {
          'Cache-Control': 'no-cache',
          'Pragma': 'no-cache'
        }
      });
    });
  },

  /**
   * 商品点赞
   * @param {number} productId 商品ID
   * @returns {Promise} 操作结果
   */
  likeProduct(productId) {
    // 从localStorage获取用户ID
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const userId = userInfo.userId;

    return handleApiResponse(() => apiClient.post(`/omg/productLike/${productId}/like`, { userId }));
  },

  /**
   * 取消商品点赞
   * @param {number} productId 商品ID
   * @returns {Promise} 操作结果
   */
  unlikeProduct(productId) {
    // 从localStorage获取用户ID
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const userId = userInfo.userId;

    return handleApiResponse(() => apiClient.post(`/omg/productLike/${productId}/unlike`, { userId }));
  },

  /**
   * 检查用户今日是否已对商品点赞
   * @param {number|Array<number>} productId 单个商品ID或商品ID数组
   * @returns {Promise} 检查结果，包含isLikedToday字段
   */
  checkLikeToday(productId) {
    // 从localStorage获取用户ID
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const userId = userInfo.userId;

    // 检查是否为数组，支持批量检查
    if (Array.isArray(productId)) {
      return handleApiResponse(() => apiClient.post(`/omg/productLike/batchCheckToday`, { 
        userId, 
        productIds: productId 
      }));
    }
    
    // 单个商品检查，保持原有逻辑
    return handleApiResponse(() => apiClient.get(`/omg/productLike/${productId}/checkLikeToday`, { params: { userId } }));
  },

  /**
   * 增加商品浏览量
   * @param {number} productId 商品ID
   * @returns {Promise} 操作结果
   */
  incrementProductViews(productId) {
    return handleApiResponse(() => apiClient.post(`/omg/products/${productId}/view`));
  },

  /**
   * 获取商品评论列表
   * @param {number} productId 商品ID
   * @param {number} [page=1] 页码
   * @param {number} [pageSize=10] 每页数量
   * @returns {Promise} 评论列表
   */
  getProductComments(productId, page = 1, pageSize = 10) {
    return handleApiResponse(() =>
      apiClient.get(`/products/${productId}/comments`, {
        params: { page, pageSize }
      })
    );
  },

  /**
   * 添加商品评论
   * @param {number} productId 商品ID
   * @param {Object} comment 评论内容
   * @returns {Promise} 操作结果
   */
  addProductComment(productId, comment) {
    return handleApiResponse(() => apiClient.post(`/products/${productId}/comments`, comment));
  },

  /**
   * 获取所有商品
   * @param {Object} params 查询参数，与searchProducts相同
   * @param {number} [params.page=1] 页码
   * @param {number} [params.pageSize=20] 每页数量
   * @param {string} [params.sortBy='createdAt'] 排序字段
   * @param {string} [params.sortOrder='desc'] 排序方向
   * @returns {Promise} 商品列表
   */
  getAllProducts(params = {}) {
    // 与searchProducts保持一致的接口调用方式
    return handleApiResponse(() => apiClient.post('/products/search', params));
  },

  /**
   * 通过标签获取商品
   * @param {string} tag 标签名称
   * @param {Object} params 查询参数
   * @returns {Promise} 商品列表
   */
  getProductsByTag(tag, params = {}) {
    return handleApiResponse(() => apiClient.get(`/products/tag/${tag}`, { params }));
  },

  /**
   * 根据分类ID获取品牌列表
   * @param {number} categoryId 分类ID
   * @returns {Promise} 品牌列表
   */
  getBrandsByCategory(categoryId) {
    return handleApiResponse(() => apiClient.get('/omg/brands/getBands', {
      params: { categoryId }
    }));
  },

  /**
   * 获取所有品牌
   * @returns {Promise} 品牌列表
   */
  getAllBrands() {
    return handleApiResponse(() => apiClient.get('/omg/brands/all'));
  },

  /**
   * 获取轮播图数据
   * @returns {Promise} 轮播图数据列表
   */
  getBanners() {
    return handleApiResponse(() => apiClient.get('/omg/banners/getBands'));
  },

  /**
   * 获取热门趋势产品
   * @returns {Promise} 热门趋势产品列表
   */
  getTrendingProducts() {
    return handleApiResponse(() => apiClient.get('/products/trending'));
  },

  /**
   * 根据ID获取产品
   * @param {number} id 产品ID
   * @returns {Promise} 产品详情
   */
  getProductById(id) {
    return handleApiResponse(() => apiClient.get(`/products/${id}`));
  },

  /**
   * 获取首页推荐商品列表 - 用于Recommend按钮功能
   * @param {Object} params 查询参数
   * @param {number} [params.page=1] 页码
   * @param {number} [params.pageSize=20] 每页数量
   * @param {string} [params.merchant] 商家名称，可选
   * @returns {Promise} 推荐商品列表
   */
  getRecommendedProducts(params = {}) {
    // 设置默认分页参数
    const queryParams = {
      page: 1,
      pageSize: 20,
      ...params
    };
    
    console.log('🔍 getRecommendedProducts API调用，参数:', queryParams);
    
    // 构建完整URL以便调试
    const url = '/omg/products/recommended';
    const queryString = Object.entries(queryParams)
      .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
      .join('&');
    console.log(`🔗 完整请求URL: ${url}?${queryString}`);
    
    return handleApiResponse(() => apiClient.get(url, {
      params: queryParams
    }));
  },

  /**
   * 切换点赞状态
   * @param {number} productId 商品ID
   * @returns {Promise} 操作结果
   */
  toggleProductLike(productId) {
    return handleApiResponse(() => apiClient.post('/products/like', { productId }));
  }
};

export default productsApi;


