import apiClient from '@/services/api';

/**
 * 添加收藏
 * @param {number|string} userId 用户ID
 * @param {number|string} sellerId 商家ID
 * @returns {Promise} 返回axios请求Promise
 */
export function addSellerCollect(userId, sellerId) {
    return apiClient.get('/sellerCollect/add', {
        params: {
            userId,
            sellerId
        }
    });
}

/**
 * 批量检查收藏状态
 * @param {number|string} userId 用户ID
 * @param {number[]|string[]} sellerIds 商家ID数组
 * @returns {Promise} 返回axios请求Promise
 */
export function checkSellerCollectStatus(userId, sellerIds) {
    return apiClient.post('/sellerCollect/check', sellerIds, {
        params: { userId }
    });
}

/**
 * 取消收藏
 * @param {number|string} userId 用户ID
 * @param {number|string} sellerId 商家ID
 * @returns {Promise} 返回axios请求Promise
 */
export function cancelSellerCollect(userId, sellerId) {
    return apiClient.get('/sellerCollect/cancel', {
        params: {
            userId,
            sellerId
        }
    });
} 