<template>
  <div class="starry-background-container" :class="{ 'light-theme': !isDarkTheme }">
    <!-- 深色背景层 -->
    <div class="dark-bg-overlay" :class="{ 'light-theme': !isDarkTheme }"></div>
  </div>
</template>

<script>
import { emitter } from '@/utils/eventBus';

export default {
  name: 'StarryBackground',
  data() {
    return {
      isDarkTheme: document.documentElement.getAttribute('data-theme') === 'dark'
    }
  },
  created() {
    // 监听主题变化
    emitter.on('theme-changed', this.handleThemeChange);
    emitter.on('apply-theme-to-page', this.handleThemeChange);
  },
  beforeUnmount() {
    // 移除主题变化监听
    emitter.off('theme-changed', this.handleThemeChange);
    emitter.off('apply-theme-to-page', this.handleThemeChange);
  },
  methods: {
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode !== 'undefined') {
        this.isDarkTheme = data.isDarkMode;
      }
    }
  }
}
</script>

<style scoped>
.starry-background-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -10;
  pointer-events: none;
  overflow: visible;
  background: #1e0940; /* 深色主题的深紫色背景 */
  background-image: linear-gradient(
    125deg, 
    #1e0940 0%, 
    #380d6d 50%, 
    #1e0940 100%
  );
  transition: all 0.5s ease;
}

/* 深色覆盖层 */
.dark-bg-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(30, 9, 64, 0.7);
  z-index: -9;
  pointer-events: none;
  transition: all 0.5s ease;
}

/* 浅色主题样式 */
.starry-background-container.light-theme {
  background: #f0d5fc; /* 浅色主题的淡紫色背景 */
  background-image: linear-gradient(
    131deg,
    #f0d5fc 0%,
    #fcf7ff 50%,
    #f0d5fc 100%
  );
}

.dark-bg-overlay.light-theme {
  background-color: rgba(249, 245, 252, 0.7);
}
</style> 