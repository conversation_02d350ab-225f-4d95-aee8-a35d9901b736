import axios from "axios";
import { ElMessage } from "element-plus";
import router from '@/router'; // 导入路由

// 确定正确的API基础URL
const getBaseApiUrl = () => {
  try {
    // 对于Vite环境
    if (typeof import.meta !== 'undefined' && import.meta.env) {
      return import.meta.env.VITE_APP_BASE_API;
    }
  } catch (e) {
    console.log("Not in Vite environment");
  }

  try {
    // 对于Vue CLI环境
    if (typeof process !== 'undefined' && process.env) {
      return process.env.VUE_APP_BASE_API;
    }
  } catch (e) {
    console.log("Not in Vue CLI environment");
  }

  // 默认API路径
  return '/api/front';
};

// token相关常量
const TOKEN_NAME = 'satoken'; // token名称
const AUTH_HEADER = 'Authorization'; // 认证请求头名称
const AUTH_SCHEME = 'Bearer'; // 认证方案前缀

// 创建axios实例
const apiClient = axios.create({
  baseURL: getBaseApiUrl(),
  timeout: 30000, // 增加超时时间到30秒
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
apiClient.interceptors.request.use(
  config => {
    // 记录请求信息
    console.log(`Sending request: ${config.method.toUpperCase()} ${config.url}`, {
      headers: config.headers,
      params: config.params,
      data: config.data
    });

    // 获取token
    const token = localStorage.getItem(TOKEN_NAME);
    if (token) {
      // 将token添加到请求头
      config.headers[AUTH_HEADER] = `${AUTH_SCHEME} ${token}`;
    }
    return config;
  },
  error => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
apiClient.interceptors.response.use(
  response => {
    // 记录响应信息
    console.log(`响应成功: ${response.config.method.toUpperCase()} ${response.config.url}`, {
      status: response.status,
      data: response.data
    });

    // 如果响应成功并且包含token信息，自动保存
    const data = response.data;
    if (data && data.code === 200 && data.data && data.data.tokenName && data.data.tokenValue) {
      // 保存token
      localStorage.setItem(data.data.tokenName, data.data.tokenValue);
    }
    // 成功的回调，简化数据
    return response.data;
  },
  error => {
    // 记录错误信息
    console.error(`响应错误: ${error.config?.method?.toUpperCase()} ${error.config?.url}`, {
      status: error.response?.status,
      data: error.response?.data,
      message: error.message
    });

    // 处理网络错误
    let msg = '';
    let status = error.response?.status || 0;
    let shouldShowMessage = true;

    // 检查是否是商品详情页面的请求，如果是则不显示错误提示（让页面自己处理重试）
    const isProductDetailRequest = error.config?.url?.includes('/omg/products/') &&
                                  !error.config?.url?.includes('/like') &&
                                  !error.config?.url?.includes('/collect');

    switch (status) {
      case 401:
        msg = "身份验证失败，请重新登录";
        // 清除本地token
        localStorage.removeItem(TOKEN_NAME);
        // 重定向到登录页
        router.push('/login');
        break;
      case 403:
        msg = "无权访问该资源";
        break;
      case 404:
        msg = "请求的资源不存在";
        break;
      case 500:
        msg = "服务器内部错误";
        break;
      case 0:
        // 网络超时或连接失败
        if (isProductDetailRequest) {
          shouldShowMessage = false; // 商品详情请求失败时不显示提示，让页面处理重试
        } else {
          msg = "网络连接超时，请检查网络连接";
        }
        break;
      default:
        if (isProductDetailRequest) {
          shouldShowMessage = false; // 商品详情请求失败时不显示提示，让页面处理重试
        } else {
          msg = "网络连接异常";
        }
    }

    // 只在需要时显示错误消息
    if (shouldShowMessage && msg) {
      ElMessage({
        type: 'error',
        message: msg,
        duration: 3000
      });
    }

    return Promise.reject(error);
  }
);

// token工具方法
export const tokenUtil = {
  // 保存token
  saveToken(tokenName, tokenValue) {
    localStorage.setItem(tokenName, tokenValue);
  },

  // 获取token
  getToken(tokenName = TOKEN_NAME) {
    return localStorage.getItem(tokenName);
  },

  // 删除token
  removeToken(tokenName = TOKEN_NAME) {
    localStorage.removeItem(tokenName);
  },

  // 检查是否已登录
  isLoggedIn() {
    return !!localStorage.getItem(TOKEN_NAME);
  }
};

// 产品相关的API方法
export const productApi = {
  // 获取产品列表
  getProducts(params) {
    return apiClient.get('/products', { params });
  },

  // 获取产品详情
  getProductById(id) {
    return apiClient.get(`/products/${id}`);
  },

  // 带查询参数的GET请求示例
  searchProducts(keyword, category, priceRange) {
    // 方法1：使用params对象（推荐方式）
    return apiClient.get('/products/search', {
      params: {
        keyword: keyword,
        category: category,
        minPrice: priceRange?.min,
        maxPrice: priceRange?.max
      }
    });

    // 方法2：手动构建查询字符串
    // const queryString = `?keyword=${encodeURIComponent(keyword)}&category=${encodeURIComponent(category)}`;
    // return apiClient.get(`/products/search${queryString}`);
  },


};

export default apiClient;
