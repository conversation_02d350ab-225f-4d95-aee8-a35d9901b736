/**
 * 邀请码处理核心脚本
 * 功能：处理URL中的邀请码参数，记录访问统计，存储邀请码供注册使用
 */

class InviteCodeHandler {
    constructor() {
        // 从window对象获取API基础URL，或使用默认值
        this.apiBaseUrl = window.VUE_APP_API_BASE_URL || 'http://localhost:8080';
        this.storageKey = 'omg_invite_code';
        this.visitedKey = 'omg_invite_visited';
        this.init();
    }

    /**
     * 初始化邀请码处理
     */
    init() {
        try {
            const inviteCode = this.getInviteCodeFromUrl();
            if (inviteCode) {
                console.log('检测到邀请码:', inviteCode);
                this.processInviteCode(inviteCode);
            }
        } catch (error) {
            console.error('邀请码处理初始化失败:', error);
        }
    }

    /**
     * 从URL中获取邀请码参数
     * @returns {string|null} 邀请码或null
     */
    getInviteCodeFromUrl() {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get('invite_code') || urlParams.get('inviteCode');
    }

    /**
     * 处理邀请码
     * @param {string} inviteCode 邀请码
     */
    async processInviteCode(inviteCode) {
        try {
            // 验证邀请码格式
            if (!this.validateInviteCodeFormat(inviteCode)) {
                console.warn('邀请码格式无效:', inviteCode);
                return;
            }

            // 验证邀请码有效性
            const isValid = await this.validateInviteCode(inviteCode);
            if (!isValid) {
                console.warn('邀请码无效或已失效:', inviteCode);
                return;
            }

            // 存储邀请码
            this.storeInviteCode(inviteCode);

            // 记录访问（防重复）
            await this.recordVisit(inviteCode);

            // 清理URL参数（可选）
            this.cleanUrlParams();

        } catch (error) {
            console.error('处理邀请码失败:', error);
        }
    }

    /**
     * 验证邀请码格式
     * @param {string} inviteCode 邀请码
     * @returns {boolean} 是否有效
     */
    validateInviteCodeFormat(inviteCode) {
        // 基本格式验证：长度3-50，只包含字母数字
        return /^[A-Za-z0-9]{3,50}$/.test(inviteCode);
    }

    /**
     * 验证邀请码有效性
     * @param {string} inviteCode 邀请码
     * @returns {Promise<boolean>} 是否有效
     */
    async validateInviteCode(inviteCode) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/invite/validate/${inviteCode}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const result = await response.json();
                console.log('邀请码验证API响应:', result);

                // 兼容多种响应格式
                if (result.data) {
                    // 格式1: { data: { success: true } }
                    if (result.data.success === true) return true;
                    // 格式2: { data: { code: 200 } }
                    if (result.data.code === 200) return true;
                    // 格式3: { data: true }
                    if (result.data === true) return true;
                }

                // 格式4: { success: true }
                if (result.success === true) return true;
                // 格式5: { code: 200 }
                if (result.code === 200) return true;

                console.warn('邀请码验证失败，响应格式:', result);
                return false;
            }
            return false;
        } catch (error) {
            console.error('验证邀请码失败:', error);
            return false;
        }
    }

    /**
     * 记录访问统计
     * @param {string} inviteCode 邀请码
     */
    async recordVisit(inviteCode) {
        try {
            // 检查是否已经记录过访问（防重复）
            const visitKey = `${this.visitedKey}_${inviteCode}`;
            const lastVisit = localStorage.getItem(visitKey);
            const now = Date.now();

            // 10分钟内不重复记录
            if (lastVisit && (now - parseInt(lastVisit)) < 10 * 60 * 1000) {
                console.log('10分钟内已记录访问，跳过重复统计');
                return;
            }

            const response = await fetch(`${this.apiBaseUrl}/api/invite/visit?inviteCode=${inviteCode}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                const result = await response.json();
                if (result.success) {
                    console.log('邀请码访问记录成功');
                    // 记录访问时间，防止重复统计
                    localStorage.setItem(visitKey, now.toString());
                } else {
                    console.warn('邀请码访问记录失败:', result.message);
                }
            }
        } catch (error) {
            console.error('记录邀请码访问失败:', error);
        }
    }

    /**
     * 存储邀请码到本地存储
     * @param {string} inviteCode 邀请码
     */
    storeInviteCode(inviteCode) {
        try {
            const inviteData = {
                code: inviteCode,
                timestamp: Date.now(),
                url: window.location.href
            };
            localStorage.setItem(this.storageKey, JSON.stringify(inviteData));
            console.log('邀请码已存储:', inviteCode);
        } catch (error) {
            console.error('存储邀请码失败:', error);
        }
    }

    /**
     * 获取存储的邀请码
     * @returns {string|null} 邀请码或null
     */
    getStoredInviteCode() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const inviteData = JSON.parse(stored);
                // 检查是否过期（7天有效期）
                const sevenDays = 7 * 24 * 60 * 60 * 1000;
                if (Date.now() - inviteData.timestamp < sevenDays) {
                    return inviteData.code;
                } else {
                    // 过期则清除
                    this.clearStoredInviteCode();
                }
            }
            return null;
        } catch (error) {
            console.error('获取存储的邀请码失败:', error);
            return null;
        }
    }

    /**
     * 清除存储的邀请码
     */
    clearStoredInviteCode() {
        try {
            localStorage.removeItem(this.storageKey);
            console.log('已清除存储的邀请码');
        } catch (error) {
            console.error('清除邀请码失败:', error);
        }
    }

    /**
     * 清理URL中的邀请码参数
     */
    cleanUrlParams() {
        try {
            const url = new URL(window.location);
            url.searchParams.delete('invite_code');
            url.searchParams.delete('inviteCode');

            // 使用 replaceState 避免页面刷新
            window.history.replaceState({}, document.title, url.toString());
        } catch (error) {
            console.error('清理URL参数失败:', error);
        }
    }

    /**
     * 获取邀请码统计信息（可选功能）
     * @param {string} inviteCode 邀请码
     * @returns {Promise<Object|null>} 统计信息
     */
    async getInviteCodeStats(inviteCode) {
        try {
            const response = await fetch(`${this.apiBaseUrl}/api/invite/stats/${inviteCode}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            });

            if (response.ok) {
                return await response.json();
            }
            return null;
        } catch (error) {
            console.error('获取邀请码统计失败:', error);
            return null;
        }
    }
}

// 创建全局实例
window.InviteCodeHandler = new InviteCodeHandler();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InviteCodeHandler;
}
