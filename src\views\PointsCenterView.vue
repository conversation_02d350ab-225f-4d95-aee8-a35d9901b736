<template>
  <div class="points-center-page">
    <div class="points-container">
      <div class="points-header">
        <h1>POINTS CENTER</h1>
        <div class="header-actions">
          <button class="check-in-btn" @click="doCheckIn" :disabled="isCheckingIn || hasCheckedInToday">
            <i class="fas fa-calendar-check"></i> 
            {{ hasCheckedInToday ? 'Already Checked-in' : 'Check-in' }}
          </button>
          <router-link to="/" class="home-btn">
            <i class="fas fa-home"></i> Home
          </router-link>
        </div>
      </div>
      
      <div class="points-content">
        <!-- Current Points Section -->
        <div class="points-summary-section">
          <div class="points-summary-card">
            <h3>Your Points</h3>
            <div class="points-balance">
              <div class="points-icon">
                <i class="fas fa-coins"></i>
              </div>
              <div class="points-amount">{{ userPoints.total || 0 }}</div>
            </div>
            <p class="points-subtitle">Available Points</p>
            
            <div class="points-actions">
              <button class="action-btn exchange-btn" @click="showExchangeDialog = true">
                <i class="fas fa-exchange-alt"></i> Exchange Points
              </button>
            </div>
          </div>
        </div>
        
        <!-- Points Exchange Marketplace Section -->
        <div class="points-marketplace-section">
          <div class="info-card">
            <h3>Points Exchange Marketplace</h3>
            
            <!-- 加载中状态 -->
            <div v-if="isLoadingProducts" class="loading-products">
              <div class="loading-spinner"></div>
              <p>Loading products...</p>
            </div>
            
            <!-- 无产品状态 -->
            <div v-else-if="marketplaceProducts.length === 0" class="empty-products">
              <i class="fas fa-shopping-bag empty-icon"></i>
              <p>No products available</p>
            </div>
            
            <!-- 产品列表 -->
            <div v-else class="marketplace-grid">
              <div class="product-card" v-for="(product, index) in marketplaceProducts" :key="product.id || index">
                <div class="product-image">
                  <img :src="product.image" :alt="product.title">
                </div>
                <div class="product-details">
                  <h4>{{ product.title }}</h4>
                  <p>{{ product.description }}</p>
                  <div class="product-footer">
                    <span class="product-points">{{ product.points }} points</span>
                    <span v-if="product.stockQuantity" class="product-stock">Stock: {{ product.stockQuantity }}</span>
                    <button class="redeem-btn" :disabled="userPoints.total < product.points">
                      {{ userPoints.total >= product.points ? 'Redeem' : 'Not enough points' }}
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- How to Earn Points Section -->
        <div class="earn-points-section">
          <div class="info-card">
            <h3>How to Earn Points</h3>
            <div class="earn-methods">
              <div 
                class="earn-method" 
                v-for="(method, index) in earnMethods" 
                :key="index"
                :class="{ 'clickable': method.clickable }"
                @click="method.clickable ? handleEarnMethodClick(method) : null"
              >
                <div class="earn-icon">
                  <i :class="method.icon"></i>
                </div>
                <div class="earn-details">
                  <h4>{{ method.title }}</h4>
                  <p>{{ method.description }}</p>
                  <span class="earn-amount">+{{ method.points }} points</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Points History Section -->
        <div class="points-history-section">
          <div class="info-card">
            <h3>Points History</h3>
            
            <!-- Loading state -->
            <div v-if="isLoading" class="history-loading">
              <div class="loading-spinner"></div>
              <p>Loading your points history...</p>
            </div>
            
            <!-- Empty state -->
            <div v-if="!isLoading && pointsHistory.length === 0" class="empty-history">
              <i class="fas fa-history empty-icon"></i>
              <p>No points history yet</p>
            </div>
            
            <!-- History list -->
            <div v-if="!isLoading && pointsHistory.length > 0" class="history-list">
              <div class="history-item" v-for="(item, index) in pointsHistory" :key="index">
                <div class="history-content">
                  <div class="history-type" :class="{ 'earned': item.type === 'earned', 'spent': item.type === 'spent' }">
                    <i :class="item.type === 'earned' ? 'fas fa-plus-circle' : 'fas fa-minus-circle'"></i>
                    {{ item.type === 'earned' ? 'Earned' : 'Spent' }}
                  </div>
                  <div class="history-details">
                    <h4>{{ item.title }}</h4>
                    <p class="history-date">{{ formatDate(item.date) }}</p>
                  </div>
                </div>
                <div class="history-amount" :class="{ 'earned': item.type === 'earned', 'spent': item.type === 'spent' }">
                  {{ item.type === 'earned' ? '+' : '-' }}{{ item.amount }} points
                </div>
              </div>
              
              <!-- Load more button -->
              <button v-if="hasMoreHistory" class="load-more-btn" @click="loadMoreHistory" :disabled="isLoadingMore">
                {{ isLoadingMore ? 'Loading...' : 'Load More' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Exchange Points Dialog -->
    <el-dialog
      v-model="showExchangeDialog"
      title="Exchange Points"
      width="500px"
      class="custom-dialog"
      :show-close="true"
      :close-on-click-modal="false"
    >
      <div class="exchange-content">
        <div class="current-points">
          <p>Your current points: <strong>{{ userPoints.total || 0 }}</strong></p>
        </div>
        
        <div class="exchange-options">
          <h4>Select an option to exchange</h4>
          <div class="option-cards">
            <div 
              v-for="(option, index) in exchangeOptions" 
              :key="index"
              class="option-card"
              :class="{ 'selected': selectedOption === index, 'disabled': userPoints.total < option.points }"
              @click="selectExchangeOption(index)"
            >
              <div class="option-icon">
                <i :class="option.icon"></i>
              </div>
              <div class="option-details">
                <h5>{{ option.title }}</h5>
                <p>{{ option.description }}</p>
                <span class="option-cost">{{ option.points }} points</span>
              </div>
            </div>
          </div>
        </div>
        
        <div class="exchange-actions">
          <el-button @click="showExchangeDialog = false">Cancel</el-button>
          <el-button 
            type="primary" 
            @click="exchangePoints" 
            :disabled="selectedOption === null || userPoints.total < (exchangeOptions[selectedOption]?.points || 0) || isExchanging"
          >
            {{ isExchanging ? 'Processing...' : 'Exchange Now' }}
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ElMessage, ElDialog, ElButton } from 'element-plus'
import userCheckInApi from '@/api/userCheckIn'
import postsApi from '@/api/posts'

export default {
  name: 'PointsCenterView',
  components: {
    ElDialog,
    ElButton
  },
  data() {
    return {
      userPoints: {
        total: 0,
        pending: 0
      },
      isLoading: true,
      isLoadingMore: false,
      isExchanging: false,
      isCheckingIn: false,
      hasCheckedInToday: false,
      pointsHistory: [],
      currentPage: 1,
      hasMoreHistory: false,
      selectedOption: null,
      showExchangeDialog: false,
      scrollObserver: null,
      pageSize: 10,
      marketplaceProducts: [],
      isLoadingProducts: false,
      earnMethods: [
        {
          title: 'Daily Check-in',
          description: 'Earn 1 point for daily check-in, increases with consecutive days',
          points: 1,
          icon: 'fas fa-calendar-check',
          clickable: false
        },
        {
          title: 'Create a Post',
          description: 'Earn points by sharing content with the community (Click to create)',
          points: 3,
          icon: 'fas fa-pen',
          clickable: true
        },
        {
          title: 'Like Comments',
          description: 'Earn points by liking other users\' comments (Click to explore)',
          points: 1,
          icon: 'fas fa-thumbs-up',
          clickable: true
        },
        {
          title: 'Daily Point Limit',
          description: 'Maximum 10 points can be earned per day',
          points: 10,
          icon: 'fas fa-stopwatch',
          clickable: false
        }
      ],
      exchangeOptions: [
        {
          title: '$5 Discount',
          description: 'Get $5 off your next purchase',
          points: 500,
          icon: 'fas fa-tag'
        },
        {
          title: 'Premium Badge',
          description: 'Exclusive badge for your profile',
          points: 1000,
          icon: 'fas fa-certificate'
        },
        {
          title: 'Priority Support',
          description: '1 month of priority customer support',
          points: 1500,
          icon: 'fas fa-headset'
        }
      ]
    }
  },
  mounted() {
    this.checkLoginStatus();
    this.fetchUserPoints();
    this.fetchPointsHistory().then(() => {
      this.checkTodayCheckInStatus();
      this.$nextTick(() => {
        this.setupScrollObserver();
      });
    });
    
    // 调用获取商品列表的方法
    this.fetchMarketplaceProducts();
  },
  beforeUnmount() {
    if (this.scrollObserver) {
      this.scrollObserver.disconnect();
    }
  },
  methods: {
    // Check if user is logged in
    checkLoginStatus() {
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
      if (!isLoggedIn) {
        ElMessage({
          message: 'Please login to access the Points Center',
          type: 'warning',
          duration: 3000
        });
        this.$router.push('/login');
      }
    },
    
    // Fetch user points
    async fetchUserPoints() {
      try {
        // Check if user is logged in
        const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
        if (!isLoggedIn) return;

        // Get user info from localStorage
        const userInfoStr = localStorage.getItem('userInfo');
        if (!userInfoStr) return;

        const userInfo = JSON.parse(userInfoStr);
        const userId = userInfo.userId || userInfo.id;
        
        if (!userId) {
          console.error('User ID not found');
          return;
        }

        // Call API to get user points
        const response = await userCheckInApi.getUserPoints(userId);
        
        if (response && response.code === 200 && response.data) {
          // Update user points from API response
          this.userPoints = {
            total: response.data.points || 0,
            pending: 0
          };
        } else {
          console.error('Failed to get user points', response);
          // Fallback to mock data
          this.userPoints = {
            total: 0,
            pending: 15
          };
        }
      } catch (error) {
        console.error('Error fetching user points:', error);
        // Fallback to mock data in case of error
        this.userPoints = {
          total: 0,
          pending: 15
        };
      }
    },
    
    // Fetch points history
    async fetchPointsHistory() {
      this.isLoading = true;
      
      try {
        // Check if user is logged in
        const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
        if (!isLoggedIn) {
          this.isLoading = false;
          return;
        }

        // Get user info from localStorage
        const userInfoStr = localStorage.getItem('userInfo');
        if (!userInfoStr) {
          this.isLoading = false;
          return;
        }

        const userInfo = JSON.parse(userInfoStr);
        const userId = userInfo.userId || userInfo.id;
        
        if (!userId) {
          console.error('User ID not found');
          this.isLoading = false;
          return;
        }
        
        // Reset pagination parameters
        this.currentPage = 1;
        
        // Call API to get points history
        const response = await userCheckInApi.getPointsHistory(userId, this.currentPage, this.pageSize);
        
        if (response && response.code === 200 && response.data) {
          // Map API data to the format our UI expects
          this.pointsHistory = response.data.map(item => ({
            id: item.id,
            title: item.type,
            amount: Math.abs(item.points), // Use absolute value for display
            type: item.points >= 0 ? 'earned' : 'spent', // Determine if earned or spent
            date: new Date(item.createdAt)
          }));
          
          // Sort by date descending (newest first)
          this.pointsHistory.sort((a, b) => b.date - a.date);
          
          // Set flag for "load more" button
          this.hasMoreHistory = this.pointsHistory.length >= this.pageSize;
        } else {
          console.error('Failed to get points history', response);
          // Fallback to empty array
          this.pointsHistory = [];
          this.hasMoreHistory = false;
        }
      } catch (error) {
        console.error('Error fetching points history:', error);
        // Fallback to empty array in case of error
        this.pointsHistory = [];
        this.hasMoreHistory = false;
      } finally {
        this.isLoading = false;
      }
    },
    
    // Load more history
    async loadMoreHistory() {
      if (this.isLoadingMore || !this.hasMoreHistory) {
        return;
      }
      
      this.isLoadingMore = true;
      this.currentPage++;
      
      try {
        // Get user info from localStorage
        const userInfoStr = localStorage.getItem('userInfo');
        if (!userInfoStr) {
          this.isLoadingMore = false;
          return;
        }

        const userInfo = JSON.parse(userInfoStr);
        const userId = userInfo.userId || userInfo.id;
        
        if (!userId) {
          console.error('User ID not found');
          this.isLoadingMore = false;
          return;
        }
        
        // Call API to get points history with pagination
        const response = await userCheckInApi.getPointsHistory(userId, this.currentPage, this.pageSize);
        
        if (response && response.code === 200 && response.data) {
          // Map API data to the format our UI expects
          const moreHistory = response.data.map(item => ({
            id: item.id,
            title: item.type,
            amount: Math.abs(item.points),
            type: item.points >= 0 ? 'earned' : 'spent',
            date: new Date(item.createdAt)
          }));
          
          // Add to existing history
          this.pointsHistory = [...this.pointsHistory, ...moreHistory];
          
          // Update hasMoreHistory flag based on if we got fewer results than expected
          this.hasMoreHistory = moreHistory.length >= this.pageSize;
          
          if (this.hasMoreHistory) {
            this.$nextTick(() => {
              this.updateScrollObserver();
            });
          }
        } else {
          console.error('Failed to get more points history', response);
          this.hasMoreHistory = false;
        }
      } catch (error) {
        console.error('Error loading more history:', error);
        this.hasMoreHistory = false;
      } finally {
        this.isLoadingMore = false;
      }
    },
    
    // Format date
    formatDate(date) {
      if (!date) return '';
      
      const now = new Date();
      const diff = now - new Date(date);
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));
      const hours = Math.floor(diff / (1000 * 60 * 60));
      
      if (days === 0) {
        if (hours === 0) {
          return 'Just now';
        }
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
      } else if (days === 1) {
        return 'Yesterday';
      } else if (days < 7) {
        return `${days} days ago`;
      } else {
        const d = new Date(date);
        return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`;
      }
    },
    
    // Select exchange option
    selectExchangeOption(index) {
      if (this.userPoints.total >= this.exchangeOptions[index].points) {
        this.selectedOption = index;
      }
    },
    
    // Exchange points
    exchangePoints() {
      if (this.selectedOption === null) return;
      
      const option = this.exchangeOptions[this.selectedOption];
      if (this.userPoints.total < option.points) {
        ElMessage({
          message: 'Not enough points for this exchange',
          type: 'error'
        });
        return;
      }
      
      this.isExchanging = true;
      
      // In a real app, this would be an API call
      // For this demo, we'll simulate the process
      setTimeout(() => {
        // Update user points
        this.userPoints.total -= option.points;
        
        // Add to history
        this.pointsHistory.unshift({
          id: new Date().getTime(),
          title: `Exchanged for ${option.title}`,
          amount: option.points,
          type: 'spent',
          date: new Date()
        });
        
        this.isExchanging = false;
        this.showExchangeDialog = false;
        this.selectedOption = null;
        
        ElMessage({
          message: `Successfully exchanged for ${option.title}`,
          type: 'success'
        });
      }, 1500);
    },
    
    // Check if user has already checked in today
    async checkTodayCheckInStatus() {
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
      if (!isLoggedIn) return;

      try {
        const userInfoStr = localStorage.getItem('userInfo');
        if (!userInfoStr) return;

        const userInfo = JSON.parse(userInfoStr);
        const userId = userInfo.userId || userInfo.id;
        
        if (!userId) return;

        // Check from history for a check-in today
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        const todayCheckIn = this.pointsHistory.find(item => {
          if (item.title === '每日签到' && item.type === 'earned') {
            const itemDate = new Date(item.date);
            itemDate.setHours(0, 0, 0, 0);
            return itemDate.getTime() === today.getTime();
          }
          return false;
        });
        
        this.hasCheckedInToday = !!todayCheckIn;
      } catch (error) {
        console.error('Error checking check-in status:', error);
      }
    },
    
    // New check-in method
    async doCheckIn() {
      // Check if already checked in today
      if (this.hasCheckedInToday) {
        ElMessage({
          message: 'You have already checked in today',
          type: 'warning',
          duration: 3000
        });
        return;
      }
      
      // Check if user is logged in
      const isLoggedIn = localStorage.getItem('isLoggedIn') === 'true';
      if (!isLoggedIn) {
        ElMessage({
          message: 'Please login to check in',
          type: 'warning',
          duration: 3000
        });
        this.$router.push('/login');
        return;
      }

      // Get user info from localStorage
      const userInfoStr = localStorage.getItem('userInfo');
      if (!userInfoStr) {
        ElMessage({
          message: 'User information not found',
          type: 'error',
          duration: 3000
        });
        return;
      }

      try {
        const userInfo = JSON.parse(userInfoStr);
        const userId = userInfo.userId || userInfo.id;
        
        if (!userId) {
          ElMessage({
            message: 'User ID not found',
            type: 'error',
            duration: 3000
          });
          return;
        }
        
        this.isCheckingIn = true;
        
        // Call the check-in API using the userCheckInApi service
        const response = await userCheckInApi.doCheckIn(userId);
        
        if (response && response.code === 200) {
          const { earnedPoints, continuousDays } = response.data;
          
          // Show success message
          ElMessage({
            message: `Check-in successful! You earned ${earnedPoints} points. This is your ${continuousDays}${this.getOrdinalSuffix(continuousDays)} consecutive day.`,
            type: 'success',
            duration: 5000
          });
          
          // Add to history
          this.pointsHistory.unshift({
            id: new Date().getTime(),
            title: `Daily Check-in Bonus`,
            amount: earnedPoints,
            type: 'earned',
            date: new Date()
          });
          
          // Update checked-in status
          this.hasCheckedInToday = true;
          
          // Refresh user points to get the latest total
          this.fetchUserPoints();
        } else {
          ElMessage({
            message: response?.msg || 'Check-in failed',
            type: 'error',
            duration: 3000
          });
        }
      } catch (error) {
        console.error('Check-in error:', error);
        ElMessage({
          message: 'Failed to check in. Please try again later.',
          type: 'error',
          duration: 3000
        });
      } finally {
        this.isCheckingIn = false;
      }
    },
    
    // Helper method to get ordinal suffix (1st, 2nd, 3rd, etc.)
    getOrdinalSuffix(num) {
      if (num > 3 && num < 21) return 'th';
      switch (num % 10) {
        case 1: return 'st';
        case 2: return 'nd';
        case 3: return 'rd';
        default: return 'th';
      }
    },

    // Setup scroll observer
    setupScrollObserver() {
      const options = {
        root: null,
        rootMargin: '0px 0px 200px 0px',
        threshold: 0.1
      };
      
      this.scrollObserver = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting && this.hasMoreHistory && !this.isLoadingMore) {
            this.loadMoreHistory();
          }
        });
      }, options);
      
      const loadMoreBtn = document.querySelector('.load-more-btn');
      if (loadMoreBtn) {
        this.scrollObserver.observe(loadMoreBtn);
      }
      
      const historyContainer = document.querySelector('.points-history-section');
      if (historyContainer) {
        this.scrollObserver.observe(historyContainer);
      }
    },
    
    // Update scroll observer
    updateScrollObserver() {
      if (this.scrollObserver) {
        this.scrollObserver.disconnect();
        
        const loadMoreBtn = document.querySelector('.load-more-btn');
        if (loadMoreBtn) {
          this.scrollObserver.observe(loadMoreBtn);
        }
      }
    },

    // Handle earn method click
    async handleEarnMethodClick(method) {
      if (method.title === 'Create a Post') {
        // 重定向到社区页面
        this.$router.push('/community');
      } else if (method.title === 'Like Comments') {
        // 显示加载状态
        ElMessage({
          message: 'Finding a random post for you...',
          type: 'info',
          duration: 1500
        });
        
        try {
          // 获取帖子列表
          const response = await postsApi.getAllPosts();
          
          if (response.code === 200 && response.data && response.data.length > 0) {
            const posts = Array.isArray(response.data) ? response.data : response.data.list || [];
            
            if (posts.length > 0) {
              // 随机选择一个帖子
              const randomIndex = Math.floor(Math.random() * posts.length);
              const randomPost = posts[randomIndex];
              this.$router.push(`/community/post/${randomPost.id}`);
              return;
            }
          }
          
          // 如果API调用失败，回退到硬编码ID列表
          const fallbackIds = [1, 2, 3, 4, 5];
          const randomId = fallbackIds[Math.floor(Math.random() * fallbackIds.length)];
          this.$router.push(`/community/post/${randomId}`);
        } catch (error) {
          console.error('Error finding random post:', error);
          // 失败时跳转到社区页面
          this.$router.push('/community');
        }
      }
    },

    // 获取积分商城商品列表
    async fetchMarketplaceProducts() {
      try {
        this.isLoadingProducts = true;
        const response = await userCheckInApi.getMarketplaceProducts();
        
        if (response && response.code === 200 && response.data) {
          // 映射API返回的数据到我们的格式
          this.marketplaceProducts = response.data.map(item => ({
            id: item.id,
            title: item.name,
            description: item.description,
            points: parseInt(item.pointRequired),
            image: item.imageUrl,
            stockQuantity: parseInt(item.stockQuantity)
          }));
        } else {
          console.error('Failed to get marketplace products', response);
          // 加载失败时显示提示
          ElMessage({
            message: 'Failed to load marketplace products',
            type: 'error',
            duration: 3000
          });
        }
      } catch (error) {
        console.error('Error fetching marketplace products:', error);
        ElMessage({
          message: 'Error loading marketplace products',
          type: 'error',
          duration: 3000
        });
      } finally {
        this.isLoadingProducts = false;
      }
    },
  }
}
</script>

<style scoped>
.points-center-page {
  padding: 2rem;
  min-height: calc(100vh - 70px);
  background: #121212;
  background-image: radial-gradient(circle at 50% 50%, #1a1a1a 0%, #0a0a0a 100%);
  color: #e0e0e0;
}

.points-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 2rem;
  background: rgba(30, 30, 35, 0.9);
  border-radius: 15px;
  border: 1px solid rgba(80, 80, 100, 0.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.points-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid rgba(120, 70, 200, 0.3);
  flex-wrap: wrap;
  gap: 1rem;
}

.points-header h1 {
  color: #c3a3ff;
  font-size: 2rem;
  margin: 0;
  letter-spacing: 2px;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.check-in-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: white;
  padding: 0.7rem 1.2rem;
  border-radius: 30px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(120, 70, 200, 0.3);
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.check-in-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(120, 70, 200, 0.5);
  background: linear-gradient(135deg, #42306a, #352458);
}

.check-in-btn:disabled {
  background: linear-gradient(135deg, #2a2a2a, #222222);
  cursor: not-allowed;
  opacity: 0.7;
  border-color: rgba(120, 70, 200, 0.1);
}

.check-in-btn:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(120, 70, 200, 0.3);
}

.home-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: white;
  padding: 0.7rem 1.2rem;
  border-radius: 30px;
  border: none;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  white-space: nowrap;
  box-shadow: 0 2px 8px rgba(120, 70, 200, 0.3);
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.home-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(120, 70, 200, 0.5);
  background: linear-gradient(135deg, #42306a, #352458);
}

.points-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* Points Summary Section */
.points-summary-section {
  width: 100%;
}

.points-summary-card {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border-radius: 15px;
  padding: 1.5rem;
  color: white;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4), 0 0 20px rgba(120, 70, 200, 0.4);
  text-align: center;
  position: relative;
  overflow: hidden;
  border: 1px solid rgba(120, 70, 200, 0.3);
}

.points-summary-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, rgba(120, 70, 200, 0.15), transparent 70%);
  opacity: 0.7;
  pointer-events: none;
  border-radius: 15px;
  z-index: 1;
}

.points-summary-card h3 {
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
  letter-spacing: 1px;
  font-weight: 600;
  position: relative;
  z-index: 2;
  color: #c3a3ff;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
}

.points-balance {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 0.5rem;
  position: relative;
  z-index: 2;
}

.points-icon {
  font-size: 2.5rem;
  color: rgba(255, 255, 255, 0.9);
  animation: coinSpin 5s infinite linear;
  filter: drop-shadow(0 0 8px rgba(120, 70, 200, 0.5));
}

@keyframes coinSpin {
  0% { transform: rotateY(0deg); }
  100% { transform: rotateY(360deg); }
}

.points-amount {
  font-size: 3.5rem;
  font-weight: 700;
  text-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  color: #c3a3ff;
  position: relative;
  z-index: 2;
}

.points-subtitle {
  font-size: 1rem;
  opacity: 0.9;
  margin-bottom: 2rem;
  position: relative;
  z-index: 2;
}

.points-actions {
  margin-top: 1rem;
  position: relative;
  z-index: 2;
}

.action-btn {
  background: rgba(255, 255, 255, 0.15);
  color: #c3a3ff;
  padding: 0.8rem 1.5rem;
  border: 1px solid rgba(120, 70, 200, 0.3);
  border-radius: 30px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(120, 70, 200, 0.5);
  background: rgba(120, 70, 200, 0.3);
}

.action-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.exchange-btn {
  font-size: 1rem;
}

/* Points Marketplace Section */
.points-marketplace-section {
  width: 100%;
  margin-bottom: 2rem;
}

.marketplace-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.product-card {
  background: linear-gradient(135deg, #242428, #1a1a20);
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(80, 80, 100, 0.3);
  transform-origin: center;
}

.product-card:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(120, 70, 200, 0.4);
  border-color: rgba(120, 70, 200, 0.5);
  background: linear-gradient(135deg, #2d2d35, #22222a);
}

.product-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, rgba(120, 70, 200, 0.15), transparent 70%);
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  border-radius: 15px;
  z-index: 1;
}

.product-card:hover::before {
  opacity: 1;
}

.product-image {
  height: 180px;
  overflow: hidden;
  background-color: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease;
  filter: drop-shadow(0 0 8px rgba(120, 70, 200, 0.3));
}

.product-card:hover .product-image img {
  transform: scale(1.05);
  filter: drop-shadow(0 0 12px rgba(120, 70, 200, 0.5));
}

.product-details {
  padding: 1.25rem;
  background: rgba(35, 35, 40, 0.9);
}

.product-details h4 {
  margin: 0 0 0.5rem;
  color: #c3a3ff;
  font-size: 1.1rem;
}

.product-details p {
  margin: 0 0 1rem;
  color: #aaa;
  font-size: 0.9rem;
  height: 2.5em;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0.5rem;
}

.product-points {
  font-weight: 600;
  color: #c3a3ff;
  font-size: 1rem;
}

.redeem-btn {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.3s ease;
}

.redeem-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, #42306a, #352458);
  box-shadow: 0 2px 8px rgba(120, 70, 200, 0.5);
}

.redeem-btn:disabled {
  background: #2a2a2a;
  color: #666;
  cursor: not-allowed;
}

/* How to Earn Points Section */
.earn-points-section {
  width: 100%;
}

.info-card {
  background: linear-gradient(135deg, #242428, #1a1a20);
  border-radius: 15px;
  padding: 1.5rem;
  border: 1px solid rgba(80, 80, 100, 0.3);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

.info-card h3 {
  color: #c3a3ff;
  margin-top: 0;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  letter-spacing: 1px;
  border-bottom: 1px solid rgba(120, 70, 200, 0.3);
  padding-bottom: 0.8rem;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
}

.earn-methods {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.earn-method {
  display: flex;
  background: rgba(40, 40, 45, 0.7);
  border-radius: 12px;
  padding: 1.2rem;
  transition: all 0.3s ease;
  border: 1px solid rgba(80, 80, 100, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.earn-method:hover {
  background: rgba(50, 50, 55, 0.8);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  border-color: rgba(120, 70, 200, 0.3);
}

.earn-method.clickable {
  cursor: pointer;
  position: relative;
}

.earn-method.clickable:hover {
  background: rgba(120, 70, 200, 0.2);
  transform: translateY(-5px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4), 0 0 30px rgba(120, 70, 200, 0.3);
}

.earn-method.clickable:active {
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(120, 70, 200, 0.2);
}

.earn-method.clickable::after {
  content: '\f105';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #c3a3ff;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.earn-method.clickable:hover::after {
  right: 10px;
  opacity: 1;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.5);
}

.earn-icon {
  font-size: 1.8rem;
  color: #c3a3ff;
  margin-right: 1rem;
  display: flex;
  align-items: center;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
}

.earn-details h4 {
  margin: 0 0 0.5rem;
  color: #c3a3ff;
  font-size: 1.1rem;
}

.earn-details p {
  margin: 0 0 0.5rem;
  color: #aaa;
  font-size: 0.9rem;
}

.earn-amount {
  color: #c3a3ff;
  font-weight: 600;
  font-size: 0.9rem;
}

/* Points History Section */
.points-history-section {
  width: 100%;
}

.history-loading, .empty-history {
  text-align: center;
  padding: 2rem 0;
  color: #aaa;
}

.loading-spinner {
  border: 4px solid rgba(120, 70, 200, 0.2);
  border-top: 4px solid #c3a3ff;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
  box-shadow: 0 0 20px rgba(120, 70, 200, 0.3);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.empty-icon {
  font-size: 3rem;
  color: rgba(120, 70, 200, 0.3);
  margin-bottom: 1rem;
  text-shadow: 0 0 15px rgba(120, 70, 200, 0.5);
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.2rem;
  background: rgba(40, 40, 45, 0.7);
  border-radius: 12px;
  transition: all 0.3s ease;
  border: 1px solid rgba(80, 80, 100, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.history-item:hover {
  background: rgba(50, 50, 55, 0.8);
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3), 0 0 20px rgba(120, 70, 200, 0.2);
  border-color: rgba(120, 70, 200, 0.3);
}

.history-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.history-type {
  display: flex;
  align-items: center;
  gap: 0.4rem;
  padding: 0.4rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 600;
  white-space: nowrap;
}

.history-type.earned {
  background: rgba(120, 70, 200, 0.2);
  color: #c3a3ff;
}

.history-type.spent {
  background: rgba(200, 70, 70, 0.2);
  color: #ff9a9a;
}

.history-details h4 {
  margin: 0 0 0.2rem;
  font-size: 1rem;
  color: #e0e0e0;
}

.history-date {
  margin: 0;
  font-size: 0.8rem;
  color: #888;
}

.history-amount {
  font-weight: 600;
  font-size: 1.1rem;
}

.history-amount.earned {
  color: #c3a3ff;
}

.history-amount.spent {
  color: #ff9a9a;
}

.load-more-btn {
  background: rgba(120, 70, 200, 0.2);
  color: #c3a3ff;
  border: 1px solid rgba(120, 70, 200, 0.3);
  padding: 0.8rem;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
  margin-top: 1rem;
  width: 100%;
}

.load-more-btn:hover:not(:disabled) {
  background: rgba(120, 70, 200, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(120, 70, 200, 0.5);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Exchange Dialog Styles */
.custom-dialog :deep(.el-dialog) {
  background: #1a1a1a;
  border-radius: 15px;
  border: 1px solid rgba(120, 70, 200, 0.3);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.8), 0 0 30px rgba(120, 70, 200, 0.3);
  overflow: hidden;
}

.custom-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  color: #c3a3ff;
  padding: 1rem 1.5rem;
}

.custom-dialog :deep(.el-dialog__title) {
  color: #c3a3ff;
  font-weight: 600;
}

.custom-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: rgba(255, 255, 255, 0.8);
}

.custom-dialog :deep(.el-dialog__body) {
  background: #1a1a1a;
  color: #e0e0e0;
  padding: 1.5rem;
}

.exchange-content {
  padding: 1rem;
}

.current-points {
  margin-bottom: 1.5rem;
  padding: 1rem;
  background: rgba(120, 70, 200, 0.1);
  border-radius: 8px;
  text-align: center;
  border: 1px solid rgba(120, 70, 200, 0.2);
}

.current-points p {
  margin: 0;
  color: #c3a3ff;
  font-size: 1.1rem;
}

.exchange-options h4 {
  margin: 0 0 1rem;
  color: #c3a3ff;
  font-size: 1.1rem;
}

.option-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.option-card {
  border: 2px solid transparent;
  border-radius: 12px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(40, 40, 45, 0.7);
}

.option-card:hover:not(.disabled) {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4), 0 0 20px rgba(120, 70, 200, 0.2);
  background: rgba(50, 50, 55, 0.8);
  border-color: rgba(120, 70, 200, 0.3);
}

.option-card.selected {
  border-color: #c3a3ff;
  background: rgba(120, 70, 200, 0.2);
  box-shadow: 0 0 20px rgba(120, 70, 200, 0.4);
}

.option-card.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.option-icon {
  font-size: 1.8rem;
  color: #c3a3ff;
  margin-bottom: 0.8rem;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.3);
}

.option-details h5 {
  margin: 0 0 0.5rem;
  color: #c3a3ff;
  font-size: 1rem;
}

.option-details p {
  margin: 0 0 0.5rem;
  color: #aaa;
  font-size: 0.85rem;
}

.option-cost {
  display: block;
  font-weight: 600;
  color: #c3a3ff;
  margin-top: 0.5rem;
}

.exchange-actions {
  display: flex;
  justify-content: end;
  gap: 1rem;
  margin-top: 1rem;
}

.exchange-actions :deep(.el-button) {
  background: rgba(40, 40, 45, 0.8);
  border-color: rgba(120, 70, 200, 0.3);
  color: #e0e0e0;
}

.exchange-actions :deep(.el-button--primary) {
  background: linear-gradient(135deg, #352458, #2d1e4a);
  border-color: rgba(120, 70, 200, 0.3);
  color: white;
}

.exchange-actions :deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, #42306a, #352458);
  border-color: rgba(120, 70, 200, 0.5);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(120, 70, 200, 0.5);
}

.exchange-actions :deep(.el-button:hover) {
  border-color: rgba(120, 70, 200, 0.5);
  color: #c3a3ff;
  transform: translateY(-2px);
}

/* Responsive styles */
@media (max-width: 768px) {
  .points-center-page {
    padding: 1rem;
  }
  
  .points-container {
    padding: 1.5rem;
  }
  
  .points-header h1 {
    font-size: 1.6rem;
  }
  
  .header-actions {
    flex-direction: column;
    gap: 0.8rem;
    width: 100%;
  }
  
  .check-in-btn, 
  .home-btn {
    width: 100%;
    justify-content: center;
  }
  
  .earn-methods,
  .marketplace-grid {
    grid-template-columns: 1fr;
  }
  
  .history-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .history-amount {
    margin-top: 0.5rem;
    align-self: flex-end;
  }
  
  .option-cards {
    grid-template-columns: 1fr;
  }
  
  .product-image {
    height: 150px;
  }
}

.loading-products, .empty-products {
  text-align: center;
  padding: 3rem 0;
  color: #aaa;
}

.empty-icon {
  font-size: 3rem;
  color: rgba(120, 70, 200, 0.3);
  margin-bottom: 1rem;
  text-shadow: 0 0 15px rgba(120, 70, 200, 0.5);
}

.product-stock {
  color: #888;
  font-size: 0.8rem;
  display: flex;
  align-items: center;
}
</style> 