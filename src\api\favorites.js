import apiClient from '@/services/api';

/**
 * 获取当前登录用户信息
 * @returns {Object} 用户信息对象
 */
function getCurrentUser() {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    return {
        userId: userInfo.userId || userInfo.id
    };
}

/**
 * 获取用户收藏列表
 * @param {Object} params 查询参数
 * @returns {Promise}
 */
export function getFavorites(params = {}) {
    const { userId } = getCurrentUser();

    return apiClient.get('/omg/collect/getAllCollectedProducts', {
        params: { userId, ...params }
    });
}

/**
 * 添加到收藏
 * @param {string|number} productId 商品ID
 * @returns {Promise}
 */
export function addToFavorites(productId) {
    const { userId } = getCurrentUser();

    return apiClient.post('/collect/addCollect', {
        userId,
        productId
    });
}

/**
 * 取消收藏
 * @param {string|number} productId 商品ID
 * @returns {Promise}
 */
export function removeFavorite(productId) {
    const { userId } = getCurrentUser();

    return apiClient.get('/omg/collect/cancelCollection', {
        params: {
            userId,
            productId
        }
    });
}

/**
 * 批量检查商品是否被收藏
 * @param {Array<number>} productIds 商品ID数组
 * @returns {Promise<Object>} 返回一个Map，key为商品ID，value为是否收藏
 */
export const checkCollectionBatch = (productIds) => {
    const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
    const userId = userInfo.userId || userInfo.id;

    return apiClient.get(`/collect/isCollected`, {
        params: {
            userId,
            productIds: productIds
        }
    });
};

/**
 * 检查商品是否已收藏
 * @param {string|number} productId 商品ID
 * @returns {Promise}
 */
export function checkIsCollected(productId) {
    const { userId } = getCurrentUser();

    return apiClient.get('/collect/isCollected', {
        params: {
            userId,
            productIds: productId
        }
    });
}

/**
 * 添加商品到收藏（兼容旧版本）
 * @param {string|number} productId 商品ID
 * @returns {Promise}
 */
export function addCollection(productId) {
    const { userId } = getCurrentUser();

    return apiClient.get('/omg/collect/addCollection', {
        params: {
            userId,
            productId
        }
    });
}



// 为了兼容性保留一些别名方法
export const getUserFavorites = getFavorites;
export const addFavorite = addToFavorites;
export const checkFavoriteStatus = checkIsCollected;

// 默认导出所有方法
export default {
    getFavorites,
    addToFavorites,
    removeFavorite,
    checkIsCollected,
    addCollection,
    // 别名
    getUserFavorites,
    addFavorite,
    checkFavoriteStatus,
    checkCollectionBatch
}; 