<template>
  <div class="post-detail-container" :class="{ 'dark-theme': isDarkMode }" :data-theme="isDarkMode ? 'dark' : 'light'">
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>加载中...</p>
    </div>
    
    <!-- 错误信息 -->
    <div v-else-if="error" class="error-container">
      <p class="error-message">{{ error }}</p>
      <button @click="fetchPostData" class="retry-button">重试</button>
    </div>
    
    <!-- 帖子内容部分 -->
    <div v-else-if="post" class="post-content-container">
      <!-- 帖子图片 -->
      <div v-if="post.images && post.images.length > 0" class="post-image-container">
        <!-- 视频内容 -->
        <div v-if="isVideo(post)" class="media-container">
          <video 
            class="post-video" 
            :poster="getVideoPoster(post)" 
            preload="metadata"
            :id="`detail-post-video`"
            controls
            @click.stop
          >
            <source :src="getVideoUrl(post)" :type="getVideoType(post)">
            Your browser does not support video playback.
          </video>
          <div class="video-overlay" @click.stop="toggleVideoPlay">
            <div class="play-button">
              <i class="fas fa-play"></i>
            </div>
          </div>
        </div>
        <!-- 图片内容 -->
        <div v-else class="media-container">
          <img :src="post.images[0].imageUrl" :alt="post.title" class="post-image">
        </div>
      </div>
      
      <!-- 帖子标题 -->
      <h2 class="post-title">{{ post.title }}</h2>
      
      <!-- 帖子正文 -->
      <div class="post-text" v-html="formatContent(post.content)"></div>

      <!-- 帖子日期和标签 -->
      <div class="post-meta">
        <span class="post-date">{{ formatDate(post.createTime) }}</span>
        <div class="tags-container">
          <span v-for="tag in post.tags" :key="tag.id" class="tag" :style="{ backgroundColor: getTagColor(tag) }">{{ tag.name }}</span>
        </div>
      </div>
      
      <!-- 帖子操作按钮 -->
      <div class="post-actions">
        <button class="action-button" :class="{'liked': post.isLiked}" @click="likePost">
          <i :class="post.isLiked ? 'fas fa-heart' : 'far fa-heart'"></i>
          <span class="action-count">{{ formatLikeCount(post.likes) }}</span>
            </button>
        <button class="action-button">
          <i class="far fa-comment"></i>
          <span class="action-count">{{ post.commentCount || 0 }}</span>
            </button>
        <button class="action-button">
          <i class="far fa-eye"></i>
          <span class="action-count">{{ post.views || 0 }}</span>
            </button>
        <button class="action-button" @click="sharePost">
          <i class="fas fa-share"></i>
            </button>
          </div>
            </div>
            
    <!-- 评论输入框 -->
    <div class="comment-input-container">
      <input type="text" placeholder="Type Comment" class="comment-input" v-model="commentText">
      <button class="comment-submit-button" @click="submitComment">
        <i class="fas fa-arrow-right"></i>
                </button>
                </div>
                
    <!-- 评论列表 -->
    <div v-if="comments && comments.length > 0" class="comments-container">
      <!-- 评论项 -->
      <div v-for="comment in comments" :key="comment.id" class="comment">
        <div class="comment-avatar">
          <img v-if="comment.users && comment.users.avatarUrl" :src="comment.users.avatarUrl" alt="用户头像" class="comment-avatar-img">
          <span v-else class="avatar-letter">{{ getAvatarLetter(comment.users) }}</span>
        </div>
        <div class="comment-content">
          <div class="comment-header">
            <span class="comment-author">{{ comment.users ? comment.users.username : '匿名用户' }}</span>
            <span class="comment-time">{{ formatDate(comment.createdAt) }}</span>
          </div>
          <p class="comment-text">{{ comment.postContent }}</p>
          <div class="comment-actions">
            <button class="comment-action-button" :class="{'liked': comment.isLiked}" @click="likeComment(comment)">
              <i :class="comment.isLiked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i>
              {{ formatLikeCount(comment.likeCount) }}
            </button>
            <button class="comment-action-button" @click="replyToComment(comment)">
              <i class="far fa-comment"></i> Reply
            </button>
            <!-- 只有用户自己发的评论才显示删除按钮 -->
            <button v-if="isUserOwnComment(comment)" class="comment-action-button delete-button" @click="confirmDeleteComment(comment)">
              <i class="far fa-trash-alt"></i> Delete
            </button>
          </div>
          
          <!-- 回复框 -->
          <div v-if="replyingTo && replyingTo.id === comment.id" class="reply-container">
            <div class="reply-input-container">
              <input type="text" v-model="replyText" placeholder="Reply to comment..." class="reply-input" @keyup.enter="submitReply" />
              <div class="reply-buttons">
                <button class="reply-button submit-button" @click="submitReply">
                  <i class="fas fa-paper-plane"></i>
                </button>
                <button class="reply-button cancel-button" @click="cancelReply">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </div>
        
          <!-- 显示回复数量，点击展开/收起 -->
          <div v-if="(comment.children && comment.children.length > 0) || (comment.replies && comment.replies.length > 0)" 
               class="view-replies" 
               @click="toggleReplies(comment.id)">
            <i :class="isCommentExpanded(comment.id) ? 'fas fa-chevron-up' : 'fas fa-chevron-down'"></i> 
            {{ isCommentExpanded(comment.id) ? 'Hide Replies' : 'View Replies (' + ((comment.children && comment.children.length) || (comment.replies && comment.replies.length) || 0) + ')' }}
          </div>
        
          <!-- 嵌套的回复评论 -->
          <div v-if="isCommentExpanded(comment.id)" class="nested-replies">
            <!-- 使用children或replies数组，优先使用children -->
            <div v-for="reply in (comment.children || comment.replies || [])" :key="reply.id" class="nested-reply">
              <div class="nested-reply-avatar">
                <img v-if="reply.users && reply.users.avatarUrl" :src="reply.users.avatarUrl" alt="用户头像" class="nested-reply-avatar-img">
                <span v-else class="avatar-letter">{{ getAvatarLetter(reply.users) }}</span>
              </div>
              <div class="nested-reply-content">
                <div class="nested-reply-header">
                  <span class="nested-reply-author">{{ reply.users ? reply.users.username : '匿名用户' }}</span>
                  <span v-if="reply.replyToUser" class="reply-to-indicator">
                    replying to <span class="reply-to-user">@{{ reply.replyToUser.username }}</span>
                  </span>
                  <span class="nested-reply-time">{{ formatDate(reply.createdAt) }}</span>
                </div>
                <p class="nested-reply-text">{{ reply.postContent }}</p>
                <div class="nested-reply-actions">
                  <button class="nested-reply-action-button" :class="{'liked': reply.isLiked}" @click="likeComment(reply)">
                    <i :class="reply.isLiked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i>
                    {{ formatLikeCount(reply.likeCount) }}
                  </button>
                  <button class="nested-reply-action-button" @click="replyToComment(reply, comment)">
                    <i class="far fa-comment"></i> Reply
                  </button>
                  <!-- 只有用户自己发的评论才显示删除按钮 -->
                  <button v-if="isUserOwnComment(reply)" class="nested-reply-action-button delete-button" @click="confirmDeleteComment(reply)">
                    <i class="far fa-trash-alt"></i> Delete
                  </button>
                </div>
                
                <!-- 为嵌套回复添加回复框 -->
                <div v-if="replyingTo && replyingTo.id === reply.id" class="reply-container">
                  <div class="reply-input-container">
                    <input type="text" v-model="replyText" placeholder="Reply to comment..." class="reply-input" @keyup.enter="submitReply" />
                    <div class="reply-buttons">
                      <button class="reply-button submit-button" @click="submitReply">
                        <i class="fas fa-paper-plane"></i>
                      </button>
                      <button class="reply-button cancel-button" @click="cancelReply">
                        <i class="fas fa-times"></i>
                      </button>
                    </div>
                  </div>
                </div>
                
                <!-- 二级嵌套回复 -->
                <div v-if="(reply.children && reply.children.length > 0) || (reply.replies && reply.replies.length > 0)" class="second-level-replies">
                  <div v-for="nestedReply in (reply.children || reply.replies || [])" :key="nestedReply.id" class="second-level-reply">
                    <div class="nested-reply-avatar">
                      <img v-if="nestedReply.users && nestedReply.users.avatarUrl" :src="nestedReply.users.avatarUrl" alt="用户头像" class="nested-reply-avatar-img">
                      <span v-else class="avatar-letter">{{ getAvatarLetter(nestedReply.users) }}</span>
                    </div>
                    <div class="nested-reply-content">
                      <div class="nested-reply-header">
                        <span class="nested-reply-author">{{ nestedReply.users ? nestedReply.users.username : '匿名用户' }}</span>
                        <span class="reply-to-indicator">
                          replying to <span class="reply-to-user">@{{ reply.users ? reply.users.username : '匿名用户' }}</span>
                        </span>
                        <span class="nested-reply-time">{{ formatDate(nestedReply.createdAt) }}</span>
                      </div>
                      <p class="nested-reply-text">{{ nestedReply.postContent }}</p>
                      <div class="nested-reply-actions">
                        <button class="nested-reply-action-button" :class="{'liked': nestedReply.isLiked}" @click="likeComment(nestedReply)">
                          <i :class="nestedReply.isLiked ? 'fas fa-thumbs-up' : 'far fa-thumbs-up'"></i>
                          {{ formatLikeCount(nestedReply.likeCount) }}
                        </button>
                        <button class="nested-reply-action-button" @click="replyToComment(nestedReply, reply)">
                          <i class="far fa-comment"></i> Reply
                        </button>
                        <!-- 只有用户自己发的评论才显示删除按钮 -->
                        <button v-if="isUserOwnComment(nestedReply)" class="nested-reply-action-button delete-button" @click="confirmDeleteComment(nestedReply)">
                          <i class="far fa-trash-alt"></i> Delete
                        </button>
                      </div>
                      
                      <!-- 为二级嵌套回复添加回复框 -->
                      <div v-if="replyingTo && replyingTo.id === nestedReply.id" class="reply-container">
                        <div class="reply-input-container">
                          <input type="text" v-model="replyText" placeholder="Reply to comment..." class="reply-input" @keyup.enter="submitReply" />
                          <div class="reply-buttons">
                            <button class="reply-button submit-button" @click="submitReply">
                              <i class="fas fa-paper-plane"></i>
                            </button>
                            <button class="reply-button cancel-button" @click="cancelReply">
                              <i class="fas fa-times"></i>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 移动端底部操作栏 -->
    <div v-if="post && isMobile" class="mobile-action-bar">
      <div class="mobile-action-item" @click="likePost">
        <i :class="post.isLiked ? 'fas fa-heart' : 'far fa-heart'" :style="{color: post.isLiked ? '#ff4757' : '#ffffff'}"></i>
        <span class="mobile-action-count">{{ formatLikeCount(post.likes) }}</span>
          </div>
      <div class="mobile-action-item" @click="scrollToComments">
        <i class="far fa-comment"></i>
        <span class="mobile-action-count">{{ post.commentCount || 0 }}</span>
      </div>
      <div class="mobile-action-item">
        <i class="far fa-eye"></i>
        <span class="mobile-action-count">{{ post.views || 0 }}</span>
      </div>
      <div class="mobile-action-item" @click="sharePost">
        <i class="fas fa-share"></i>
      </div>
    </div>
  </div>
</template>

<script>
import { reactive } from 'vue';
import postsApi from '@/api/posts';
import { emitter } from '@/utils/eventBus';

export default {
  name: 'PostDetailView',
  props: {
    id: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    // 主题管理
    const savedDarkMode = localStorage.getItem('darkMode');
    const initialDarkMode = savedDarkMode !== null ? savedDarkMode === 'true' : true;

    return {
      post: null,
      comments: [],
      loading: false,
      error: null,
      commentText: '',
      isCheckingLikes: false,
      viewIncremented: false, // 标记是否已增加浏览量
      viewTimer: null, // 用于存储定时器ID
      replyingTo: null, // 当前正在回复的评论
      parentComment: null, // 如果回复的是二级评论，这里存储其父评论
      replyText: '', // 回复内容
      expandedComments: reactive({}), // 使用reactive创建响应式对象
      isMobile: false, // 是否为移动设备
      isDarkMode: initialDarkMode, // 主题状态
      isVideoPlaying: false // 是否有视频正在播放
    };
  },
  created() {
    this.resetViewState();
    this.fetchPostData();
    this.checkMobile();
    window.addEventListener('resize', this.checkMobile);

    // 监听主题变化事件
    emitter.on('theme-changed', this.handleThemeChange);
    emitter.on('apply-theme-to-page', this.handleThemeChange);
  },
  beforeUnmount() {
    // 组件销毁前清除定时器和事件监听
    this.clearViewTimer();
    window.removeEventListener('resize', this.checkMobile);

    // 清理主题事件监听器
    emitter.off('theme-changed', this.handleThemeChange);
    emitter.off('apply-theme-to-page', this.handleThemeChange);
    
    // 停止视频播放
    this.stopVideoPlayback();
  },
  watch: {
    // 监听评论数据变化，自动检查点赞状态
    comments: {
      handler(newComments, oldComments) {
        // 只有当评论数据实际发生变化时才检查点赞状态
        if (newComments && newComments.length > 0 && 
            (!oldComments || 
             oldComments.length !== newComments.length || 
             JSON.stringify(newComments.map(c => c.id)) !== JSON.stringify(oldComments.map(c => c.id)))) {
          this.checkCommentsLikeStatus();
        }
      },
      deep: false // 改为false，只监听数组引用变化，不监听内部属性变化
    },
    // 监听帖子浏览量变化
    'post.views': {
      handler() {
        // 监听浏览量变化，不使用参数
      }
    },
    // 监听路由参数变化
    id: {
      handler(newId, oldId) {
        if (newId !== oldId) {
          this.resetViewState();
          this.fetchPostData();
        }
      },
      immediate: false
    }
  },
  methods: {
    // 添加一个格式化点赞数的方法
    formatLikeCount(count) {
      // 确保count是数字类型
      if (count === undefined || count === null) return 0;
      // 将输入转换为数字
      const numCount = parseInt(count, 10);
      // 如果转换失败，返回0
      return isNaN(numCount) ? 0 : numCount;
    },
    
    async fetchPostData() {
      this.loading = true;
      this.error = null;
      
      try {
        // 使用getPostDetail接口获取帖子详情
        const response = await postsApi.getPostDetail(this.id);
        
        if (response.code === 200 && response.data) {
          this.post = response.data;
          
          // 检查帖子点赞状态
          await this.checkPostLikeStatus();
          
          // 如果帖子数据中已包含评论，直接使用
          if (this.post.comments && this.post.comments.length > 0) {
            this.comments = this.post.comments;
            // 检查评论点赞状态
            await this.checkCommentsLikeStatus();
              } else {
            // 否则获取评论
            await this.fetchComments();
            // 注意：fetchComments方法内部会调用checkCommentsLikeStatus，所以这里不需要再调用
          }
          
          // 帖子数据加载成功后，开始计时增加浏览量
          this.startViewTimer();
          } else {
            this.error = '未找到该帖子';
        }
      } catch (err) {
        this.error = '加载帖子数据时出错';
      } finally {
        this.loading = false;
      }
    },
    
    async fetchComments() {
      try {
        const response = await postsApi.getPostCommentsWithPaging(this.id);
        
        if (response.code === 200 && response.data) {
          // 获取评论列表
          const commentsList = response.data.list || [];
          
          // 清空原有评论
          this.comments = [];
          
          // 保留已展开评论的状态，避免重新获取评论后折叠所有评论
          const expandedCommentsCopy = {...this.expandedComments};
          
          // 第一步：构建完整的评论树，确保每个评论都在正确的位置上
          // 用于存储所有评论的Map，键是评论ID，值是评论对象
          const allCommentsMap = new Map();
          
          // 用于标记评论是否是某个评论的子评论
          const isChildComment = new Set();
          
          // 首先将所有评论添加到Map中
          commentsList.forEach(comment => {
            if (comment && comment.id) {
              // 确保comment.children始终是数组
              if (!comment.children) {
                comment.children = [];
              }
              allCommentsMap.set(comment.id, comment);
            }
          });
          
          // 第二步：处理父子关系，将子评论添加到其父评论的children数组中
          commentsList.forEach(comment => {
            if (comment && comment.id && comment.parentCommentId) {
              // 这是一个子评论，找到其父评论
              const parentComment = allCommentsMap.get(comment.parentCommentId);
              
              if (parentComment) {
                // 确保父评论有children数组
                if (!parentComment.children) {
                  parentComment.children = [];
                }
                
                // 将此评论添加到父评论的children数组中
                // 避免重复添加
                if (!parentComment.children.some(c => c.id === comment.id)) {
                  parentComment.children.push(comment);
                }
                
                // 标记此评论为子评论
                isChildComment.add(comment.id);
              }
            }
          });
          
          // 第三步：过滤出顶级评论（不是任何评论的子评论）
          const topLevelComments = commentsList.filter(comment => 
            comment && comment.id && !isChildComment.has(comment.id)
          );
          
          // 第四步：递归处理所有评论，确保children数组中没有重复项
          const deduplicateChildren = (comment) => {
            if (!comment.children || comment.children.length === 0) {
              return;
            }
            
            // 使用Set去除重复的子评论ID
            const uniqueChildrenIds = new Set();
            const uniqueChildren = [];
            
            for (const child of comment.children) {
              if (child && child.id && !uniqueChildrenIds.has(child.id)) {
                uniqueChildrenIds.add(child.id);
                uniqueChildren.push(child);
                
                // 递归处理子评论的子评论
                deduplicateChildren(child);
              }
            }
            
            // 用去重后的子评论替换原数组
            comment.children = uniqueChildren;
          };
          
          // 对所有顶级评论执行去重
          topLevelComments.forEach(comment => {
            deduplicateChildren(comment);
          });
          
          // 设置处理后的评论
          this.comments = topLevelComments;
          
          // 恢复之前已展开的评论状态
          this.expandedComments = expandedCommentsCopy;
          
          // 取消当前回复状态
          this.cancelReply();
          
          // 如果有评论且用户已登录，检查点赞状态
          if (this.comments.length > 0) {
            await this.checkCommentsLikeStatus();
          }
        }
      } catch (err) {
        console.error('获取评论失败:', err);
      }
    },
    
    async checkCommentsLikeStatus() {
      // 如果已经在检查中，则不重复调用
      if (this.isCheckingLikes) {
        return;
      }
      
      this.isCheckingLikes = true; // 设置标志位
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，不检查点赞状态
        if (!userId) {
          return;
        }
        
        // 收集所有评论ID，包括所有层级的嵌套评论
        const commentIds = new Set(); // 使用Set避免重复ID
        
        // 递归函数收集所有评论ID
        const collectCommentIds = (comments) => {
          if (!comments || !comments.length) return;
          
          comments.forEach(comment => {
            if (comment && comment.id) {
              commentIds.add(comment.id);
            
              // 收集一级回复的ID (使用children属性)
              if (comment.children && comment.children.length) {
                collectCommentIds(comment.children);
              }
            }
          });
        };
                  
        // 收集所有评论ID
        collectCommentIds(this.comments);
        
        // 转换Set为数组
        const commentIdsArray = Array.from(commentIds);
        
        if (commentIdsArray.length === 0) {
          this.isCheckingLikes = false;
          return;
        }
        
        
        // 批量检查点赞状态
        const response = await postsApi.checkLikedComments({
          userId: userId,
          commentIds: commentIdsArray
        });
        
        if (response.code === 200 && response.data) {
          // 递归更新评论点赞状态
          const updateLikeStatus = (comments) => {
            if (!comments || !comments.length) return;
            
            comments.forEach(comment => {
              if (comment && comment.id && response.data[comment.id]) {
                const commentData = response.data[comment.id];
                if (commentData) {
                  comment.isLiked = commentData.liked;
                  comment.likeCount = commentData.likeCount || 0;
                }
              }
              
              // 更新一级回复的点赞状态 (使用children属性)
              if (comment.children && comment.children.length) {
                updateLikeStatus(comment.children);
              }
            });
          };
          
          // 更新所有评论的点赞状态
          updateLikeStatus(this.comments);
        }
      } catch (err) {
        console.error('检查评论点赞状态失败:', err);
      } finally {
        this.isCheckingLikes = false; // 重置标志位
      }
    },
    
    async likePost() {
      if (!this.post) return;
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，跳转到登录页
        if (!userId) {
          this.$router.push('/login');
          return;
        }
        
        // 获取当前点赞状态
        const isLiked = this.post.isLiked;
        
        // 立即更新UI状态
        this.post.isLiked = !isLiked;
        if (isLiked) {
          // 如果当前已点赞，取消点赞，点赞数减1
          this.post.likes = Math.max(0, (this.post.likes || 0) - 1);
        } else {
          // 如果当前未点赞，点赞，点赞数加1
          this.post.likes = (this.post.likes || 0) + 1;
        }
        
        // 调用API
        const params = {
          userId: userId,
          postId: this.post.id
        };
        
        const response = isLiked 
          ? await postsApi.unlikePostNew(params)
          : await postsApi.likePostNew(params);
        
        
        if (response.code === 200 && response.data) {
          // 使用接口返回的数据更新点赞状态和点赞数
          this.post.isLiked = !isLiked;
          
          // 增强点赞数处理逻辑
          if (response.data.likeCount !== undefined) {
            try {
              // 打印调试信息
              
              // 尝试将返回的点赞数转换为数字
              const likeCount = parseInt(response.data.likeCount, 10);
              
              // 确保转换结果是有效数字
              if (!isNaN(likeCount)) {
                this.post.likes = likeCount;
              } else {
                console.error('API返回的点赞数无法转换为数字');
                // 如果转换失败，使用之前的策略
                this.post.likes = isLiked 
                  ? Math.max(0, (this.post.likes || 0) - 1)
                  : (this.post.likes || 0) + 1;
              }
            } catch (error) {
              console.error('处理点赞数时出错:', error);
              // 出错时使用之前的策略
              this.post.likes = isLiked 
                ? Math.max(0, (this.post.likes || 0) - 1)
                : (this.post.likes || 0) + 1;
            }
          }
        } else {
          // 如果API调用失败，恢复原状态
          this.post.isLiked = isLiked;
          this.post.likes = isLiked
            ? (this.post.likes || 0) + 1
            : Math.max(0, (this.post.likes || 0) - 1);
          console.error('Like operation failed:', response.msg);
        }
      } catch (err) {
        console.error('Like error:', err);
      }
    },
    
    sharePost() {
      if (!this.post) return;

      const postUrl = `${window.location.origin}/client/post/${this.post.id}`;

      // 尝试使用现代 Clipboard API
      if (navigator.clipboard && window.isSecureContext) {
        navigator.clipboard.writeText(postUrl).then(() => {
          console.log('链接已复制到剪贴板');
        }).catch(err => {
          console.error('Clipboard API failed:', err);
          this.fallbackCopyToClipboard(postUrl);
        });
      } else {
        // 使用备用方案
        this.fallbackCopyToClipboard(postUrl);
      }
    },

    // 备用复制方案
    fallbackCopyToClipboard(text) {
      try {
        // 创建临时文本区域
        const textArea = document.createElement('textarea');
        textArea.value = text;
        textArea.style.position = 'fixed';
        textArea.style.left = '-999999px';
        textArea.style.top = '-999999px';
        document.body.appendChild(textArea);
        textArea.focus();
        textArea.select();

        // 尝试复制
        const successful = document.execCommand('copy');
        document.body.removeChild(textArea);

        if (successful) {
          console.log('链接已复制到剪贴板 (备用方案)');
        } else {
          console.error('复制失败，请手动复制链接:', text);
          // 可以在这里添加一个模态框显示链接让用户手动复制
          this.showShareModal(text);
        }
      } catch (err) {
        console.error('备用复制方案失败:', err);
        this.showShareModal(text);
      }
    },

    // 显示分享模态框
    showShareModal(url) {
      // 使用浏览器原生 prompt 作为最后的备用方案
      const userAgent = navigator.userAgent.toLowerCase();
      if (userAgent.includes('mobile') || userAgent.includes('android') || userAgent.includes('iphone')) {
        // 移动端使用 Web Share API（如果支持）
        if (navigator.share) {
          navigator.share({
            title: this.post.title || '分享帖子',
            text: this.post.content ? this.post.content.substring(0, 100) + '...' : '',
            url: url
          }).catch(err => {
            console.error('Web Share API failed:', err);
            prompt('请复制以下链接:', url);
          });
        } else {
          prompt('请复制以下链接:', url);
        }
      } else {
        // 桌面端使用 prompt
        prompt('请复制以下链接:', url);
      }
    },
    
    formatDate(dateString) {
      if (!dateString) return '';
      
      const date = new Date(dateString);
      const now = new Date();
      const diff = now - date;
      
      // 小于1分钟
      if (diff < 60 * 1000) {
        return 'just now';
      }
      
      // 小于1小时
      if (diff < 60 * 60 * 1000) {
        const minutes = Math.floor(diff / (60 * 1000));
        return `${minutes} ${minutes === 1 ? 'minute' : 'minutes'} ago`;
      }
      
      // 小于24小时
      if (diff < 24 * 60 * 60 * 1000) {
        const hours = Math.floor(diff / (60 * 60 * 1000));
        return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
      }
      
      // 小于30天
      if (diff < 30 * 24 * 60 * 60 * 1000) {
        const days = Math.floor(diff / (24 * 60 * 60 * 1000));
        return `${days} ${days === 1 ? 'day' : 'days'} ago`;
      }
      
      // 格式化为年月日
      const options = { year: 'numeric', month: 'short', day: 'numeric' };
      return date.toLocaleDateString('en-US', options);
    },

    getAvatarLetter(user) {
      if (!user) return '?';
      
      const username = user.username || '';
      return username.charAt(0).toUpperCase();
    },
    
    async submitComment() {
      if (!this.commentText.trim()) {
        return;
      }
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，跳转到登录页
        if (!userId) {
          this.$router.push('/login');
              return;
            }
            
        const commentData = {
          postId: this.id,
          userId: userId,
          postContent: this.commentText
        };
        
        const response = await postsApi.createComment(commentData);
            
            if (response.code === 200) {
          this.commentText = ''; // 清空输入框
          
          // 更新评论列表
          await this.fetchComments();
          
          // 更新帖子评论数
          if (this.post) {
            this.post.commentCount = (this.post.commentCount || 0) + 1;
          }
          
          // 检查新评论的点赞状态
          await this.checkCommentsLikeStatus();
            } else {
          console.error('Failed to post comment:', response.msg);
        }
      } catch (err) {
        console.error('提交评论出错:', err);
      }
    },
    
    async likeComment(comment) {
      if (!comment) return;
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，跳转到登录页
        if (!userId) {
          this.$router.push('/login');
          return;
        }
        
        // 获取当前点赞状态
        const isLiked = comment.isLiked;
        
        // 立即更新UI状态
        comment.isLiked = !isLiked;
        if (isLiked) {
          // 如果当前已点赞，取消点赞，点赞数减1
          comment.likeCount = Math.max(0, (comment.likeCount || 0) - 1);
          } else {
          // 如果当前未点赞，点赞，点赞数加1
          comment.likeCount = (comment.likeCount || 0) + 1;
        }
        
        // 调用API
        const params = {
          userId: userId,
          commentId: comment.id
        };
        
        const response = isLiked 
          ? await postsApi.unlikeComment(params)
          : await postsApi.likeComment(params);   
        
        
        if (response.code === 200 && response.data) {
          // 使用接口返回的数据更新点赞状态和点赞数
          comment.isLiked = !isLiked; // 或者使用response.data.liked如果接口返回了这个值
          
          // 增强点赞数处理逻辑
          if (response.data.likeCount !== undefined) {
            try {
              // 打印调试信息
              
              // 尝试将返回的点赞数转换为数字
              const likeCount = parseInt(response.data.likeCount, 10);
              
              // 确保转换结果是有效数字
              if (!isNaN(likeCount)) {
                comment.likeCount = likeCount;
              } else {
                console.error('评论API返回的点赞数无法转换为数字');
                // 如果转换失败，使用之前的策略
                comment.likeCount = isLiked 
                  ? Math.max(0, (comment.likeCount || 0) - 1)
                  : (comment.likeCount || 0) + 1;
              }
            } catch (error) {
              console.error('处理评论点赞数时出错:', error);
              // 出错时使用之前的策略
              comment.likeCount = isLiked 
                ? Math.max(0, (comment.likeCount || 0) - 1)
                : (comment.likeCount || 0) + 1;
            }
          }
          
          // 不需要重新检查所有评论的点赞状态，因为我们已经更新了当前评论
          } else {
          // 如果API调用失败，恢复原状态
          comment.isLiked = isLiked;
          comment.likeCount = isLiked
            ? (comment.likeCount || 0) + 1
            : Math.max(0, (comment.likeCount || 0) - 1);
          console.error('Like operation failed:', response.msg);
        }
      } catch (err) {
        console.error('Like error:', err);
      }
    },
    
    replyToComment(comment, parentComment = null) {
      // 从localStorage获取用户信息
      const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
      const userId = userInfo.userId;
      
      // 如果未登录，跳转到登录页
      if (!userId) {
        this.$router.push('/login');
        return;
      }
      
      // 如果已经点击同一个评论的回复按钮，则关闭回复框
      if (this.replyingTo && this.replyingTo.id === comment.id) {
        this.cancelReply();
        return;
      }
        
      // 设置当前正在回复的评论
      this.replyingTo = comment;
      
      // 查找评论的真实父评论
      const findActualParent = (comment) => {
        // 如果是顶级评论，它自己就是父评论
        if (!comment.parentCommentId) {
          return comment;
        }
        
        // 在所有顶级评论中查找此评论的父评论
        for (const topComment of this.comments) {
          // 先检查当前顶级评论是否就是父评论
          if (topComment.id === comment.parentCommentId) {
            return topComment;
          }
          
          // 如果不是，检查其子评论
          if (topComment.children && topComment.children.length > 0) {
            for (const childComment of topComment.children) {
              if (childComment.id === comment.parentCommentId) {
                // 找到了直接父评论，但我们需要返回顶级评论
                return topComment;
              }
            }
          }
        }
        
        // 如果找不到父评论，返回null
        return null;
      };
      
      // 确定父评论
      if (parentComment) {
        // 如果提供了父评论，使用它
        this.parentComment = parentComment;
      } else {
        // 尝试找到实际的父评论
        const actualParent = findActualParent(comment);
        this.parentComment = actualParent || comment;
      }
      
      
      this.replyText = '';
    },

    cancelReply() {
      // 取消回复
      this.replyingTo = null;
      this.parentComment = null;
      this.replyText = '';
    },
    
    async submitReply() {
      // 如果没有正在回复的评论或回复内容为空，不执行
      if (!this.replyingTo || !this.replyText.trim()) {
        return;
      }
      
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录，跳转到登录页
        if (!userId) {
          this.$router.push('/login');
          return;
        }
        
        // 确定真正的父评论ID
        let parentCommentId;
        
        if (this.parentComment) {
          // 如果回复的是子评论，而父评论不是顶级评论，需要找到顶级评论ID
          if (this.replyingTo.parentCommentId && this.parentComment.parentCommentId) {
            // 这是一个嵌套的子评论回复，找到顶级评论
            for (const topComment of this.comments) {
              // 检查当前评论是否包含我们的父评论作为子评论
              if (topComment.children && topComment.children.some(child => child.id === this.parentComment.id)) {
                parentCommentId = topComment.id;
                break;
              }
            }
            
            // 如果没找到顶级评论，使用当前父评论ID
            if (!parentCommentId) {
              parentCommentId = this.parentComment.id;
            }
          } else {
            // 正常情况，使用父评论ID
            parentCommentId = this.parentComment.id;
          }
        } else {
          // 如果没有设置父评论，使用当前回复的评论ID
          parentCommentId = this.replyingTo.id;
        }
        
        // 准备回复数据
        const replyData = {
          postId: this.id,
          userId: userId,
          postContent: this.replyText,
          parentCommentId: parentCommentId
        };
        
        // 如果回复的是嵌套回复，而不是主评论，添加replyToUserId
        if (this.replyingTo.id !== parentCommentId) {
          replyData.replyToUserId = this.replyingTo.userId;
        }
        
        
        // 调用回复评论接口
        const response = await postsApi.replyToComment(replyData);
        
        if (response.code === 200) {
          
          // 记住当前正在回复的评论，以便后续展开
          const currentParentId = parentCommentId;
          
          // 清空回复框
          this.cancelReply();
          
          // 重新获取评论列表
          await this.fetchComments();
          
          // 自动展开刚刚回复的评论
          if (currentParentId) {
            this.expandedComments[currentParentId] = true;
          }
          
          // 更新帖子评论数
          if (this.post) {
            this.post.commentCount = (this.post.commentCount || 0) + 1;
          }
        } else {
          console.error('Failed to post reply:', response.msg);
        }
      } catch (err) {
        console.error('回复评论失败:', err);
      }
    },

    async checkPostLikeStatus() {
      try {
        // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        // 如果未登录或没有帖子数据，不检查点赞状态
        if (!userId || !this.post) {
          return;
        }
        
        // 检查帖子点赞状态
        const response = await postsApi.batchCheckPostLikeStatus({
          userId: userId,
          postIds: [this.post.id]
        });
        
        if (response.code === 200 && response.data) {
          // 更新帖子的点赞状态
          const isLiked = !!response.data[this.post.id];
          this.post.isLiked = isLiked;
        }
      } catch (err) {
        // 错误处理
      }
    },

    startViewTimer() {
      // 如果已经增加过浏览量，不再重复增加
      if (this.viewIncremented) {
        return;
      }
      
      // 清除可能存在的旧定时器
      this.clearViewTimer();
      
      // 设置3秒后增加浏览量的定时器
      this.viewTimer = setTimeout(() => {
        // 确保只调用一次
        if (!this.viewIncremented) {
          this.incrementPostView();
        }
      }, 3000); // 3秒后执行
    },

    clearViewTimer() {
      // 清除定时器
      if (this.viewTimer) {
        clearTimeout(this.viewTimer);
        this.viewTimer = null;
      }
    },
    
    async incrementPostView() {
      // 如果已经增加过浏览量或没有帖子数据，不执行
      if (this.viewIncremented || !this.post || !this.post.id) {
        return;
      }
      
      // 立即标记为已增加，防止重复调用
      this.viewIncremented = true;
      
      try {
        const postId = this.post.id;
        const response = await postsApi.incrementPostView(postId);
        
        if (response.code === 200 && response.data !== undefined) {
          // 更新帖子浏览量
          this.post.views = response.data;
        } else {
          // 如果失败，重置标记，允许重试
          this.viewIncremented = false;
        }
        } catch (err) {
        // 如果出错，重置标记，允许重试
        this.viewIncremented = false;
      }
    },

    resetViewState() {
      // 清除定时器
      this.clearViewTimer();
      
      // 重置状态
      this.post = null;
      this.comments = [];
      this.loading = false;
      this.error = null;
      this.commentText = '';
      this.isCheckingLikes = false;
      this.viewIncremented = false;
      
      // 重置展开状态
      Object.keys(this.expandedComments).forEach(key => {
        delete this.expandedComments[key];
      });
    },

    isUserOwnComment(comment) {
      // 从localStorage获取用户信息
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
      // 如果未登录或评论没有用户信息，返回false
      if (!userId || !comment || !comment.users) return false;
      
      // 检查评论的用户ID是否与当前登录用户ID相同
      return comment.userId === userId;
    },
    
    confirmDeleteComment(comment) {
      // 使用Element UI的确认对话框
      this.$confirm('Are you sure you want to delete this comment?', 'Confirm', {
        confirmButtonText: 'Yes',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        // 用户点击确定，执行删除操作
        this.deleteComment(comment);
      }).catch(() => {
        // 用户点击取消，不做任何操作
        this.$message({
          type: 'info',
          message: 'Delete canceled'
        });
      });
    },
    
    async deleteComment(comment) {
      try {
        const response = await postsApi.deleteComment(comment.id);
        
        if (response.code === 200) {
          
          // 检查是否为主评论还是嵌套回复
          const isMainComment = !comment.parentCommentId;
          
          if (isMainComment) {
            // 如果是主评论，从评论列表中移除
            this.comments = this.comments.filter(c => c.id !== comment.id);
        } else {
            // 如果是嵌套回复，从父评论的children中移除
            const parentComment = this.comments.find(c => c.id === comment.parentCommentId);
            
            if (parentComment && parentComment.children) {
              parentComment.children = parentComment.children.filter(r => r.id !== comment.id);
              
              // 如果是二级嵌套回复
              if (!parentComment) {
                // 查找所有一级回复
                this.comments.forEach(mainComment => {
                  if (mainComment.children) {
                    mainComment.children.forEach(reply => {
                      if (reply.children) {
                        // 从二级回复中移除
                        reply.children = reply.children.filter(r => r.id !== comment.id);
                      }
                    });
                  }
                });
              }
            }
          }
          
          // 更新帖子评论数
          if (this.post) {
            this.post.commentCount = Math.max(0, (this.post.commentCount || 0) - 1);
          }
        } else {
          this.$message({
            type: 'error',
            message: 'Failed to delete comment: ' + response.msg
          });
        }
      } catch (err) {
        this.$message({
          type: 'error',
          message: 'Failed to delete comment, please try again later'
        });
      }
    },

    async loadMoreReplies(comment) {
      try {
        // 这里应该调用获取更多回复的API
        // 由于API可能有所不同，这里只是一个示例
        // 实际实现需要根据你的API来调整
        console.log(`加载评论ID ${comment.id} 的更多回复功能正在开发中`);
        
        // 示例：如果有专门的获取评论回复的接口，可以这样调用
        /*
        const response = await postsApi.getCommentReplies({
          commentId: comment.id,
          page: Math.ceil(comment.replies.length / 10) + 1,
          pageSize: 10
        });
        
        if (response.code === 200 && response.data) {
          // 将新加载的回复添加到现有回复列表中
          const newReplies = response.data.list || [];
          comment.replies = [...comment.replies, ...newReplies];
          
          // 检查新加载的回复的点赞状态
          await this.checkCommentsLikeStatus();
        }
        */
        } catch (err) {
        console.error('加载更多回复失败:', err);
      }
    },

    formatContent(content) {
      if (!content) return '';
      
      // 使用组件的isMobile属性
      const isMobile = this.isMobile;
      
      // 1. 处理已有的段落（保留原有的换行）
      let formattedContent = content.replace(/\n{2,}/g, '</p><p>');
      formattedContent = formattedContent.replace(/\n/g, '<br>');
      
      // 2. 如果内容没有明显的段落分隔，根据句子进行智能分段
      if (!content.includes('\n')) {
        // 按句号、问号、感叹号分割，但保留这些标点符号
        const sentences = content.match(/[^.!?。？！]+[.!?。？！]+/g) || [content];
        
        // 如果句子数量大于1，且内容长度超过一定值，进行分段处理
        if (sentences.length > 1 && content.length > 100) {
          let paragraphs = [];
          let currentParagraph = '';
          
          // 每3-5个句子组成一个段落，移动端可以更少一些
          const sentencesPerParagraph = isMobile ? 2 : 4;
          const lengthThreshold = isMobile ? 100 : 150;
          
          sentences.forEach((sentence, index) => {
            currentParagraph += sentence;
            
            // 根据句子长度和数量决定何时分段
            if ((index + 1) % sentencesPerParagraph === 0 || 
                (currentParagraph.length > lengthThreshold && (index + 1) % 2 === 0) ||
                index === sentences.length - 1) {
              paragraphs.push(currentParagraph);
              currentParagraph = '';
            }
          });
          
          // 如果最后还有未添加的段落
          if (currentParagraph) {
            paragraphs.push(currentParagraph);
          }
          
          // 将段落连接起来
          formattedContent = paragraphs.join('</p><p>');
        }
      }
      
      // 3. 处理特殊格式（如果需要）
      // 加粗文本 **文本** -> <strong>文本</strong>
      formattedContent = formattedContent.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      
      // 斜体文本 *文本* -> <em>文本</em>
      formattedContent = formattedContent.replace(/\*(.*?)\*/g, '<em>$1</em>');
      
      // 4. 处理链接
      formattedContent = formattedContent.replace(/(https?:\/\/[^\s]+)/g, '<a href="$1" target="_blank" rel="noopener noreferrer">$1</a>');
      
      // 5. 确保内容被包裹在段落标签中
      if (!formattedContent.startsWith('<p>')) {
        formattedContent = '<p>' + formattedContent;
      }
      if (!formattedContent.endsWith('</p>')) {
        formattedContent = formattedContent + '</p>';
      }
      
      // 6. 移动端特殊处理 - 为每个段落添加一个特殊的类
      if (isMobile) {
        formattedContent = formattedContent.replace(/<p>/g, '<p class="mobile-paragraph">');
      }
      
      return formattedContent;
    },

    isCommentExpanded(commentId) {
      return !!this.expandedComments[commentId];
    },

    toggleReplies(commentId) {
      this.expandedComments[commentId] = !this.expandedComments[commentId];
    },

    getTagColor(tag) {
      // 预定义的颜色数组
      const colors = [
        '#3a4a9c', // 原始的蓝色
        '#6a359c', // 紫色
        '#9c3535', // 红色
        '#359c6a', // 绿色
        '#9c7635', // 橙色
        '#359c9c', // 青色
        '#9c359c', // 品红
        '#5a9c35', // 黄绿色
        '#9c5a35', // 棕色
        '#35359c'  // 深蓝色
      ];
      
      // 如果有id，使用id来选择颜色
      if (tag.id) {
        return colors[tag.id % colors.length];
      }
      
      // 如果有name但没有id，使用name的字符和来选择颜色
      if (tag.name) {
        let sum = 0;
        for (let i = 0; i < tag.name.length; i++) {
          sum += tag.name.charCodeAt(i);
        }
        return colors[sum % colors.length];
      }
      
      // 默认返回第一个颜色
      return colors[0];
    },

    checkMobile() {
      this.isMobile = window.innerWidth <= 768;
    },

    scrollToComments() {
      // 找到评论区域元素
      const commentsContainer = document.querySelector('.comments-container');
      // 如果找不到评论区域，则滚动到评论输入框
      const commentInput = document.querySelector('.comment-input-container');

      if (commentsContainer) {
        // 滚动到评论区域
        commentsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
      } else if (commentInput) {
        // 滚动到评论输入框
        commentInput.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    },

    // 主题变化处理
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode !== 'undefined') {
        this.isDarkMode = data.isDarkMode;
      }
    },

    isVideo(post) {
      // 检查帖子是否有videoUrl属性
      if (post.videoUrl) return true;
      
      // 检查帖子的媒体类型
      if (post.mediaType === 'video') return true;
      
      // 检查帖子的内容URL是否是视频格式
      if (post.contentUrl) {
        const url = post.contentUrl.toLowerCase();
        return url.endsWith('.mp4') || url.endsWith('.webm') || url.endsWith('.ogg') || url.includes('video');
      }
      
      // 检查帖子的图片数组中是否有视频
      if (post.images && post.images.length > 0) {
        const firstMedia = post.images[0];
        // 检查媒体类型或URL
        if (firstMedia.type === 'video' || firstMedia.mediaType === 'video') return true;
        if (firstMedia.videoUrl) return true;
        if (firstMedia.url) {
          const url = firstMedia.url.toLowerCase();
          return url.endsWith('.mp4') || url.endsWith('.webm') || url.endsWith('.ogg');
        }
        if (firstMedia.imageUrl) {
          const url = firstMedia.imageUrl.toLowerCase();
          return url.endsWith('.mp4') || url.endsWith('.webm') || url.endsWith('.ogg');
        }
      }
      
      return false;
    },

    getVideoPoster(post) {
      // 直接从帖子获取封面图
      if (post.coverImage) return post.coverImage;
      if (post.thumbnail) return post.thumbnail;
      
      // 从images数组获取
      if (post.images && post.images.length > 0) {
        const secondImage = post.images.length > 1 ? post.images[1].imageUrl : null;
        if (secondImage) return secondImage; // 如果有第二张图片，使用它作为封面
        
        // 使用第一张图片的缩略图
        const firstMedia = post.images[0];
        if (firstMedia.thumbnail) return firstMedia.thumbnail;
        if (firstMedia.imageUrl && !this.isVideoUrl(firstMedia.imageUrl)) return firstMedia.imageUrl;
      }
      
      return ''; // 如果没有找到封面图，返回空字符串
    },

    getVideoUrl(post) {
      // 直接从帖子获取视频URL
      if (post.videoUrl) return post.videoUrl;
      
      // 从contentUrl获取
      if (post.contentUrl && this.isVideoUrl(post.contentUrl)) return post.contentUrl;
      
      // 从images数组获取
      if (post.images && post.images.length > 0) {
        const firstMedia = post.images[0];
        if (firstMedia.videoUrl) return firstMedia.videoUrl;
        if (firstMedia.url && this.isVideoUrl(firstMedia.url)) return firstMedia.url;
        if (firstMedia.imageUrl && this.isVideoUrl(firstMedia.imageUrl)) return firstMedia.imageUrl;
      }
      
      return ''; // 如果没有找到视频URL，返回空字符串
    },

    // 判断URL是否是视频格式
    isVideoUrl(url) {
      if (!url) return false;
      
      const lowerCaseUrl = url.toLowerCase();
      return lowerCaseUrl.endsWith('.mp4') || 
             lowerCaseUrl.endsWith('.webm') || 
             lowerCaseUrl.endsWith('.ogg') || 
             lowerCaseUrl.includes('video');
    },

    getVideoType(post) {
      const videoUrl = this.getVideoUrl(post);
      if (!videoUrl) return 'video/mp4'; // 默认类型
      
      if (videoUrl.toLowerCase().endsWith('.mp4')) return 'video/mp4';
      if (videoUrl.toLowerCase().endsWith('.webm')) return 'video/webm';
      if (videoUrl.toLowerCase().endsWith('.ogg')) return 'video/ogg';
      
      return 'video/mp4'; // 默认类型
    },

    toggleVideoPlay() {
      const videoId = `detail-post-video`;
      const videoElement = document.getElementById(videoId);
      const overlay = document.querySelector('.video-overlay');
      
      if (!videoElement) return;
      
      if (videoElement.paused) {
        // 播放视频
        videoElement.play()
          .then(() => {
            this.isVideoPlaying = true;
            // 隐藏播放按钮
            if (overlay) overlay.style.display = 'none';
          })
          .catch(error => {
            console.error('视频播放失败:', error);
          });
      } else {
        // 暂停视频
        videoElement.pause();
        this.isVideoPlaying = false;
        // 显示播放按钮
        if (overlay) overlay.style.display = 'flex';
      }
    },

    // 停止视频播放
    stopVideoPlayback() {
      const videoElement = document.getElementById('detail-post-video');
      if (videoElement) {
        videoElement.pause();
        this.isVideoPlaying = false;
      }
    }
  }
}
</script>

<style scoped>
.post-detail-container {
  width: 100%;
  max-width: 850px;
  margin: 0 auto;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  transition: all 0.3s ease;
}

/* 深色主题容器 */
.post-detail-container[data-theme="dark"],
.post-detail-container.dark-theme {
  color: #ffffff;
  background-color: #1a0b2e;
}

/* 浅色主题容器 */
.post-detail-container[data-theme="light"] {
  color: #333333;
  background-color: #f8fafc;
}

.post-content-container {
  border-radius: 12px;
  padding: 20px 0 24px 0;
  margin-bottom: 16px;
  overflow: hidden;
  transition: background-color 0.3s ease;
}

/* 深色主题帖子内容容器 */
.post-detail-container[data-theme="dark"] .post-content-container,
.post-detail-container.dark-theme .post-content-container {
  background-color: #1e1033;
}

/* 浅色主题帖子内容容器 */
.post-detail-container[data-theme="light"] .post-content-container {
  background-color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.post-image-container {
  width: 100%;
  margin-bottom: 16px;
}

.post-image {
  width: 100%;
  max-height: 500px;
  object-fit: contain;
  display: block;
}

.post-title {
  font-size: 28px;
  font-weight: 600;
  margin: 0 16px 24px;
  line-height: 1.3;
  transition: color 0.3s ease;
}

/* 深色主题标题 */
.post-detail-container[data-theme="dark"] .post-title,
.post-detail-container.dark-theme .post-title {
  color: #ffffff;
}

/* 浅色主题标题 */
.post-detail-container[data-theme="light"] .post-title {
  color: #1a202c;
}

.post-text {
  font-size: 17px;
  line-height: 1.8;
  margin: 0 16px 24px;
  letter-spacing: 0.01em;
  transition: color 0.3s ease;
}

/* 深色主题文本 */
.post-detail-container[data-theme="dark"] .post-text,
.post-detail-container.dark-theme .post-text {
  color: #ffffff;
}

/* 浅色主题文本 */
.post-detail-container[data-theme="light"] .post-text {
  color: #2d3748;
}

.post-text p {
  margin-bottom: 32px;
  padding-bottom: 8px;
  line-height: 1.8;
}

.post-text p:last-child {
  margin-bottom: 0;
}

.post-text a {
  text-decoration: none;
  transition: color 0.3s ease;
}

/* 深色主题链接 */
.post-detail-container[data-theme="dark"] .post-text a,
.post-detail-container.dark-theme .post-text a {
  color: #b99dff;
}

/* 浅色主题链接 */
.post-detail-container[data-theme="light"] .post-text a {
  color: #7c3aed;
}

.post-text a:hover {
  text-decoration: underline;
}

.post-text strong {
  font-weight: 600;
  transition: color 0.3s ease;
}

/* 深色主题加粗文本 */
.post-detail-container[data-theme="dark"] .post-text strong,
.post-detail-container.dark-theme .post-text strong {
  color: #ffffff;
}

/* 浅色主题加粗文本 */
.post-detail-container[data-theme="light"] .post-text strong {
  color: #1a202c;
}

.post-text em {
  font-style: italic;
  transition: color 0.3s ease;
}

/* 深色主题斜体文本 */
.post-detail-container[data-theme="dark"] .post-text em,
.post-detail-container.dark-theme .post-text em {
  color: #d0c0e0;
}

/* 浅色主题斜体文本 */
.post-detail-container[data-theme="light"] .post-text em {
  color: #718096;
}

.post-meta {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin: 0 16px 16px;
}

.post-date {
  font-size: 14px;
  transition: color 0.3s ease;
}

/* 深色主题日期 */
.post-detail-container[data-theme="dark"] .post-date,
.post-detail-container.dark-theme .post-date {
  color: #9d8ab0;
}

/* 浅色主题日期 */
.post-detail-container[data-theme="light"] .post-date {
  color: #718096;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag {
  background-color: #3a4a9c;
  color: #ffffff;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 14px;
  display: inline-block;
}

.post-actions {
  display: flex;
  padding: 0 16px;
  margin-top: 16px;
}

.action-button {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 8px;
  display: flex;
  align-items: center;
  margin-right: 24px;
  transition: color 0.3s ease;
}

/* 深色主题操作按钮 */
.post-detail-container[data-theme="dark"] .action-button,
.post-detail-container.dark-theme .action-button {
  color: #9d8ab0;
}

/* 浅色主题操作按钮 */
.post-detail-container[data-theme="light"] .action-button {
  color: #718096;
}

.action-button.liked {
  color: #6a359c !important;
}

.action-button.liked i {
  color: #6a359c !important;
}

.action-button i {
  margin-right: 6px;
}

.action-count {
  font-size: 14px;
  transition: color 0.3s ease;
}

/* 深色主题操作计数 */
.post-detail-container[data-theme="dark"] .action-count,
.post-detail-container.dark-theme .action-count {
  color: #9d8ab0;
}

/* 浅色主题操作计数 */
.post-detail-container[data-theme="light"] .action-count {
  color: #718096;
}

.comment-input-container {
  display: flex;
  margin-bottom: 16px;
  border-radius: 30px;
  padding: 5px;
  transition: background-color 0.3s ease;
}

/* 深色主题评论输入容器 */
.post-detail-container[data-theme="dark"] .comment-input-container,
.post-detail-container.dark-theme .comment-input-container {
  background-color: #1e1033;
}

/* 浅色主题评论输入容器 */
.post-detail-container[data-theme="light"] .comment-input-container {
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
}

.comment-input {
  flex: 1;
  background: none;
  border: none;
  padding: 10px 15px;
  font-size: 16px;
  outline: none;
  transition: color 0.3s ease;
}

/* 深色主题评论输入框 */
.post-detail-container[data-theme="dark"] .comment-input,
.post-detail-container.dark-theme .comment-input {
  color: #ffffff;
}

/* 浅色主题评论输入框 */
.post-detail-container[data-theme="light"] .comment-input {
  color: #2d3748;
}

.comment-input::placeholder {
  transition: color 0.3s ease;
}

/* 深色主题占位符 */
.post-detail-container[data-theme="dark"] .comment-input::placeholder,
.post-detail-container.dark-theme .comment-input::placeholder {
  color: #9d8ab0;
}

/* 浅色主题占位符 */
.post-detail-container[data-theme="light"] .comment-input::placeholder {
  color: #a0aec0;
}

.comment-submit-button {
  background-color: #6a359c;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  cursor: pointer;
}

.comments-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.comment {
  display: flex;
  gap: 12px;
  border-radius: 12px;
  padding: 12px;
  transition: background-color 0.3s ease;
}

/* 深色主题评论 */
.post-detail-container[data-theme="dark"] .comment,
.post-detail-container.dark-theme .comment {
  background-color: #1e1033;
}

/* 浅色主题评论 */
.post-detail-container[data-theme="light"] .comment {
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
}

.comment-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background-color: #6a359c;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.avatar-letter {
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.comment-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.comment-content {
  flex: 1;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.comment-author {
  font-weight: 600;
  font-size: 14px;
  transition: color 0.3s ease;
}

/* 深色主题评论作者 */
.post-detail-container[data-theme="dark"] .comment-author,
.post-detail-container.dark-theme .comment-author {
  color: #b99dff;
}

/* 浅色主题评论作者 */
.post-detail-container[data-theme="light"] .comment-author {
  color: #7c3aed;
}

.comment-time {
  font-size: 12px;
  transition: color 0.3s ease;
}

/* 深色主题评论时间 */
.post-detail-container[data-theme="dark"] .comment-time,
.post-detail-container.dark-theme .comment-time {
  color: #9d8ab0;
}

/* 浅色主题评论时间 */
.post-detail-container[data-theme="light"] .comment-time {
  color: #a0aec0;
}

.comment-text {
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 14px;
  transition: color 0.3s ease;
}

/* 深色主题评论文本 */
.post-detail-container[data-theme="dark"] .comment-text,
.post-detail-container.dark-theme .comment-text {
  color: #e0e0e0;
}

/* 浅色主题评论文本 */
.post-detail-container[data-theme="light"] .comment-text {
  color: #4a5568;
}

.comment-actions {
  display: flex;
  gap: 16px;
}

.comment-action-button {
  background: none;
  border: none;
  font-size: 12px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 4px;
  transition: color 0.3s ease;
}

/* 深色主题评论操作按钮 */
.post-detail-container[data-theme="dark"] .comment-action-button,
.post-detail-container.dark-theme .comment-action-button {
  color: #9d8ab0;
}

/* 浅色主题评论操作按钮 */
.post-detail-container[data-theme="light"] .comment-action-button {
  color: #a0aec0;
}

.comment-action-button.liked {
  color: #6a359c !important;
}

.comment-action-button.liked i {
  color: #6a359c !important;
}

.comment-action-button i {
  font-size: 14px;
}

.comment-action-button.delete-button {
  color: #ff6b6b;
}

.comment-action-button.delete-button:hover {
  color: #ff4757;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

.loading-spinner {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
  transition: all 0.3s ease;
}

/* 深色主题加载动画 */
.post-detail-container[data-theme="dark"] .loading-spinner,
.post-detail-container.dark-theme .loading-spinner {
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #ffffff;
}

/* 浅色主题加载动画 */
.post-detail-container[data-theme="light"] .loading-spinner {
  border: 4px solid rgba(51, 51, 51, 0.3);
  border-top: 4px solid #7c3aed;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
  padding: 20px;
}

.error-message {
  color: #ff6b6b;
  margin-bottom: 20px;
  text-align: center;
}

.retry-button {
  background-color: #6a359c;
  border: none;
  border-radius: 5px;
  padding: 10px 20px;
  color: white;
  font-size: 16px;
  cursor: pointer;
}

.reply-container {
  margin-top: 12px;
}

.reply-input-container {
  display: flex;
  border-radius: 20px;
  padding: 5px;
  transition: background-color 0.3s ease;
}

/* 深色主题回复输入容器 */
.post-detail-container[data-theme="dark"] .reply-input-container,
.post-detail-container.dark-theme .reply-input-container {
  background-color: #2a1a47;
}

/* 浅色主题回复输入容器 */
.post-detail-container[data-theme="light"] .reply-input-container {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
}

.reply-input {
  flex: 1;
  background: none;
  border: none;
  padding: 8px 12px;
  font-size: 14px;
  outline: none;
  transition: color 0.3s ease;
}

/* 深色主题回复输入框 */
.post-detail-container[data-theme="dark"] .reply-input,
.post-detail-container.dark-theme .reply-input {
  color: #ffffff;
}

/* 浅色主题回复输入框 */
.post-detail-container[data-theme="light"] .reply-input {
  color: #2d3748;
}

.reply-input::placeholder {
  transition: color 0.3s ease;
}

/* 深色主题回复占位符 */
.post-detail-container[data-theme="dark"] .reply-input::placeholder,
.post-detail-container.dark-theme .reply-input::placeholder {
  color: #9d8ab0;
}

/* 浅色主题回复占位符 */
.post-detail-container[data-theme="light"] .reply-input::placeholder {
  color: #a0aec0;
}

.reply-buttons {
  display: flex;
}

.reply-button {
  background: none;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: #9d8ab0;
}

.reply-button.submit-button {
  background-color: #6a359c;
  color: white;
  margin-right: 5px;
}

.reply-button.cancel-button {
  background-color: #3a2a55;
  color: #d0c0e0;
}

.nested-replies {
  margin-top: 8px;
  padding-left: 12px;
  transition: border-color 0.3s ease;
}

/* 深色主题嵌套回复边框 */
.post-detail-container[data-theme="dark"] .nested-replies,
.post-detail-container.dark-theme .nested-replies {
  border-left: 2px solid #3a2a55;
}

/* 浅色主题嵌套回复边框 */
.post-detail-container[data-theme="light"] .nested-replies {
  border-left: 2px solid #e2e8f0;
}

.nested-reply {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

/* 深色主题嵌套回复 */
.post-detail-container[data-theme="dark"] .nested-reply,
.post-detail-container.dark-theme .nested-reply {
  background-color: #2a1a47;
}

/* 浅色主题嵌套回复 */
.post-detail-container[data-theme="light"] .nested-reply {
  background-color: #f7fafc;
  border: 1px solid #e2e8f0;
}

.nested-reply-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #6a359c;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.nested-reply-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.nested-reply-content {
  flex: 1;
}

.nested-reply-header {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  flex-wrap: wrap;
}

.nested-reply-author {
  font-weight: 600;
  font-size: 13px;
  margin-right: 6px;
  transition: color 0.3s ease;
}

/* 深色主题嵌套回复作者 */
.post-detail-container[data-theme="dark"] .nested-reply-author,
.post-detail-container.dark-theme .nested-reply-author {
  color: #b99dff;
}

/* 浅色主题嵌套回复作者 */
.post-detail-container[data-theme="light"] .nested-reply-author {
  color: #7c3aed;
}

.reply-to-indicator {
  font-size: 12px;
  margin-right: 6px;
  transition: color 0.3s ease;
}

/* 深色主题回复指示器 */
.post-detail-container[data-theme="dark"] .reply-to-indicator,
.post-detail-container.dark-theme .reply-to-indicator {
  color: #9d8ab0;
}

/* 浅色主题回复指示器 */
.post-detail-container[data-theme="light"] .reply-to-indicator {
  color: #a0aec0;
}

.reply-to-user {
  transition: color 0.3s ease;
}

/* 深色主题回复用户 */
.post-detail-container[data-theme="dark"] .reply-to-user,
.post-detail-container.dark-theme .reply-to-user {
  color: #b99dff;
}

/* 浅色主题回复用户 */
.post-detail-container[data-theme="light"] .reply-to-user {
  color: #7c3aed;
}

.nested-reply-time {
  font-size: 12px;
  margin-left: auto;
  transition: color 0.3s ease;
}

/* 深色主题嵌套回复时间 */
.post-detail-container[data-theme="dark"] .nested-reply-time,
.post-detail-container.dark-theme .nested-reply-time {
  color: #9d8ab0;
}

/* 浅色主题嵌套回复时间 */
.post-detail-container[data-theme="light"] .nested-reply-time {
  color: #a0aec0;
}

.nested-reply-text {
  margin-bottom: 6px;
  line-height: 1.4;
  font-size: 13px;
  transition: color 0.3s ease;
}

/* 深色主题嵌套回复文本 */
.post-detail-container[data-theme="dark"] .nested-reply-text,
.post-detail-container.dark-theme .nested-reply-text {
  color: #e0e0e0;
}

/* 浅色主题嵌套回复文本 */
.post-detail-container[data-theme="light"] .nested-reply-text {
  color: #4a5568;
}

.nested-reply-actions {
  display: flex;
  gap: 12px;
}

.nested-reply-action-button {
  background: none;
  border: none;
  font-size: 11px;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  gap: 3px;
  transition: color 0.3s ease;
}

/* 深色主题嵌套回复操作按钮 */
.post-detail-container[data-theme="dark"] .nested-reply-action-button,
.post-detail-container.dark-theme .nested-reply-action-button {
  color: #9d8ab0;
}

/* 浅色主题嵌套回复操作按钮 */
.post-detail-container[data-theme="light"] .nested-reply-action-button {
  color: #a0aec0;
}

.nested-reply-action-button.liked {
  color: #6a359c !important;
}

.nested-reply-action-button.liked i {
  color: #6a359c !important;
}

.view-replies {
  margin-top: 12px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 0;
  transition: color 0.3s ease;
}

/* 深色主题查看回复 */
.post-detail-container[data-theme="dark"] .view-replies,
.post-detail-container.dark-theme .view-replies {
  color: #b99dff;
}

.post-detail-container[data-theme="dark"] .view-replies:hover,
.post-detail-container.dark-theme .view-replies:hover {
  color: #d0c0e0;
}

/* 浅色主题查看回复 */
.post-detail-container[data-theme="light"] .view-replies {
  color: #7c3aed;
}

.post-detail-container[data-theme="light"] .view-replies:hover {
  color: #553c9a;
}

.second-level-replies {
  margin-top: 10px;
  padding-left: 10px;
  transition: border-color 0.3s ease;
}

/* 深色主题二级回复边框 */
.post-detail-container[data-theme="dark"] .second-level-replies,
.post-detail-container.dark-theme .second-level-replies {
  border-left: 2px solid #3a2a55;
}

/* 浅色主题二级回复边框 */
.post-detail-container[data-theme="light"] .second-level-replies {
  border-left: 2px solid #cbd5e0;
}

.second-level-reply {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
  padding: 6px;
  border-radius: 8px;
  transition: background-color 0.3s ease;
}

/* 深色主题二级回复 */
.post-detail-container[data-theme="dark"] .second-level-reply,
.post-detail-container.dark-theme .second-level-reply {
  background-color: #251640;
}

/* 浅色主题二级回复 */
.post-detail-container[data-theme="light"] .second-level-reply {
  background-color: #edf2f7;
  border: 1px solid #e2e8f0;
}

/* 移动端样式优化 */
@media (max-width: 768px) {
  .post-detail-container {
    padding: 0 8px;
  }
  
  .post-content-container {
    padding: 15px 0 20px 0;
    margin-bottom: 12px;
    border-radius: 10px;
  }
  
  .post-text {
    font-size: 15px;
    line-height: 1.6;
    margin: 0 12px 20px;
  }
  
  .post-text p {
    margin-bottom: 28px;  /* 增加段落底部间距 */
    padding-bottom: 8px;  /* 增加内部底部间距 */
  }
  
  /* 移动端段落样式 - 模拟截图效果 */
  .post-text p.mobile-paragraph {
    padding-left: 12px;
    border-left: 4px solid #6a359c;
    margin-left: 5px;
    margin-top: 25px;  /* 增加段落顶部间距 */
    position: relative;  /* 为添加额外分隔符做准备 */
    padding-top: 10px;  /* 增加段落顶部内边距 */
  }
  
  /* 为段落之间添加额外的视觉分隔 */
  .post-text p.mobile-paragraph:not(:first-child)::before {
    content: "";
    position: absolute;
    top: -18px;
    left: -40px;
    width: 80px;
    height: 2px;
    background-color: #3a2a55;
  }
  
  /* 添加装饰点 */
  .post-text p.mobile-paragraph:not(:first-child)::after {
    content: "";
    position: absolute;
    top: -18px;
    left: 0;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background-color: #6a359c;
  }
  
  /* 第一个段落的特殊样式 */
  .post-text p.mobile-paragraph:first-child {
    margin-top: 10px;
  }
  
  /* 最后一个段落的特殊样式 */
  .post-text p.mobile-paragraph:last-child {
    margin-bottom: 15px;
  }
  
  /* 移动端列表样式 */
  .post-text ul, .post-text ol {
    padding-left: 20px;
    margin-bottom: 28px;  /* 与段落保持一致 */
  }
  
  /* 移动端标题样式 */
  .post-title {
    font-size: 22px;
    margin: 0 12px 18px;
    line-height: 1.3;
  }
  
  /* 移动端评论样式 */
  .comment {
    padding: 10px 8px;
  }
  
  .comment-text {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 6px;
  }
  
  .comment-actions {
    flex-wrap: wrap;
  }
  
  .comment-action-button {
    font-size: 11px;
    margin-right: 12px;
    margin-bottom: 4px;
  }
  
  /* 移动端嵌套回复样式 */
  .nested-replies {
    margin-top: 6px;
    padding-left: 8px;
  }
  
  .nested-reply {
    padding: 6px;
    margin-bottom: 6px;
  }
  
  .nested-reply-text {
    font-size: 12px;
    line-height: 1.4;
  }
  
  .nested-reply-actions {
    gap: 8px;
  }
  
  /* 移动端查看回复按钮 */
  .view-replies {
    font-size: 12px;
    margin-top: 8px;
  }
  
  /* 移动端评论输入框 */
  .comment-input-container {
    margin-bottom: 12px;
    border-radius: 25px;
    padding: 3px;
  }
  
  .comment-input {
    padding: 8px 12px;
  font-size: 14px;
  }
  
  .comment-submit-button {
    width: 35px;
    height: 35px;
  }
  
  /* 移动端回复输入框 */
  .reply-input-container {
    padding: 3px;
    border-radius: 18px;
  }
  
  .reply-input {
    padding: 6px 10px;
    font-size: 13px;
  }
  
  .reply-button {
    width: 28px;
    height: 28px;
  }
  
  /* 移动端头像大小 */
  .comment-avatar {
  width: 32px;
  height: 32px;
}

  .nested-reply-avatar {
    width: 24px;
    height: 24px;
}
}

/* 移动端底部操作栏 */
.mobile-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12px 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  z-index: 100;
  transition: all 0.3s ease;
}

/* 深色主题移动端操作栏 */
.post-detail-container[data-theme="dark"] .mobile-action-bar,
.post-detail-container.dark-theme .mobile-action-bar {
  background-color: #1a0b2e;
  border-top: 1px solid #3a2a55;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.3);
}

/* 浅色主题移动端操作栏 */
.post-detail-container[data-theme="light"] .mobile-action-bar {
  background-color: #ffffff;
  border-top: 1px solid #e2e8f0;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-action-item {
  cursor: pointer;
  font-size: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0 15px;
  transition: color 0.3s ease;
}

/* 深色主题移动端操作项 */
.post-detail-container[data-theme="dark"] .mobile-action-item,
.post-detail-container.dark-theme .mobile-action-item {
  color: #ffffff;
}

/* 浅色主题移动端操作项 */
.post-detail-container[data-theme="light"] .mobile-action-item {
  color: #4a5568;
}

.mobile-action-count {
  font-size: 12px;
  margin-top: 4px;
  transition: color 0.3s ease;
}

/* 深色主题移动端操作计数 */
.post-detail-container[data-theme="dark"] .mobile-action-count,
.post-detail-container.dark-theme .mobile-action-count {
  color: #9d8ab0;
}

/* 浅色主题移动端操作计数 */
.post-detail-container[data-theme="light"] .mobile-action-count {
  color: #a0aec0;
}

/* 确保底部有足够的空间，避免内容被底部栏遮挡 */
@media (max-width: 768px) {
  .post-detail-container {
    padding-bottom: 70px;
  }
}

.media-container {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 12px;
  max-height: 500px;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.video-overlay:hover {
  background-color: rgba(0, 0, 0, 0.3);
}

/* 深色主题播放按钮 */
.post-detail-container[data-theme="dark"] .play-button,
.post-detail-container.dark-theme .play-button {
  background-color: rgba(90, 51, 160, 0.8);
  box-shadow: 0 0 20px rgba(90, 51, 160, 0.4);
}

/* 浅色主题播放按钮 */
.post-detail-container[data-theme="light"] .play-button {
  background-color: rgba(142, 45, 226, 0.8);
  box-shadow: 0 0 20px rgba(142, 45, 226, 0.3);
}

.play-button {
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: white;
  font-size: 24px;
  transition: all 0.3s ease;
}

.play-button:hover {
  transform: scale(1.1);
}

.post-video {
  width: 100%;
  max-height: 500px;
  object-fit: contain;
  background-color: #000;
}

/* 为视频添加浅色模式下的边框 */
.post-detail-container[data-theme="light"] .post-video {
  border: 1px solid rgba(142, 45, 226, 0.2);
}
</style> 