<template>
  <div class="logo">
    <div class="logo-container">
      <img :src="logoSrc" alt="Logo" class="logo-image" />
      <div class="letters">
        <span class="letter o">O</span>
        <span class="letter m">M</span>
        <span class="letter g">G</span>
        <span class="letter r">R</span>
        <span class="letter e">e</span>
        <span class="letter v">v</span>
        <span class="letter i">i</span>
        <span class="letter e2">e</span>
        <span class="letter w">w</span>
      </div>
    </div>
  </div>
</template>

<script>
import { emitter } from '@/utils/eventBus'

export default {
  name: 'BrandLogo',
  data() {
    return {
      isDarkMode: true // 默认深色主题
    }
  },
  computed: {
    logoSrc() {
      // 根据主题模式返回对应的logo图片
      return this.isDarkMode
        ? 'https://omgbuy.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/29/20250729-144453_compressed_20250729144813A002.png' // 深色主题logo
        : 'https://omgbuy.oss-eu-central-1.aliyuncs.com/ylkj-uploads/2025/07/29/20250728-182903_compressed_20250729144751A001.png' // 浅色主题logo
    }
  },
  created() {
    // 从localStorage获取主题设置
    const savedDarkMode = localStorage.getItem('darkMode');
    this.isDarkMode = savedDarkMode !== null ? savedDarkMode === 'true' : true;

    // 监听主题变化事件
    emitter.on('theme-changed', this.handleThemeChange);
    emitter.on('apply-theme-to-page', this.handleThemeChange);
  },
  beforeUnmount() {
    // 清理事件监听
    emitter.off('theme-changed', this.handleThemeChange);
    emitter.off('apply-theme-to-page', this.handleThemeChange);
  },
  methods: {
    handleThemeChange(data) {
      if (data && typeof data.isDarkMode === 'boolean') {
        this.isDarkMode = data.isDarkMode;
      }
    }
  }
}
</script>

<style scoped>
.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.logo-container {
  display: flex;
  align-items: center;
  position: relative;
}

.logo-image {
  height: 45px;
  width: 45px;
  object-fit: contain;
  margin-right: 8px;
}

.letters {
  display: flex;
  align-items: flex-end;
}

.letter {
  font-size: 2.2rem;
  font-weight: 900;
  color: #ffffff;
  font-family: 'Arial Black', 'Arial', sans-serif;
  line-height: 1;
}

/* 彩色字母效果 */
.letter.o { color: #4285F4; }
.letter.m { color: #EA4335; }
.letter.g { color: #FBBC05; }
.letter.r { color: #4285F4; }
.letter.e { color: #34A853; }
.letter.v { color: #EA4335; }
.letter.i { color: #4285F4; }
.letter.e2 { color: #FBBC05; }
.letter.w { color: #34A853; }

@media (max-width: 768px) {
  .logo-image {
    height: 35px;
    width: 35px;
  }
  
  .letter {
    font-size: 1.8rem;
  }
}

@media (max-width: 480px) {
  .logo-image {
    height: 30px;
    width: 30px;
  }
  
  .letter {
    font-size: 1.4rem;
  }
}
</style> 