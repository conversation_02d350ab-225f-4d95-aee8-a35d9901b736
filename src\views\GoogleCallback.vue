<template>
  <div class="google-callback">
    <div v-if="loading" class="loading">
      <el-loading :fullscreen="true" text="正在处理登录信息..."></el-loading>
    </div>
    <div v-if="error" class="error">
      {{ error }}
    </div>
  </div>
</template>

<script>
import { ElMessage } from 'element-plus'
import { emitter } from '@/utils/eventBus'
import { tokenUtil } from '@/services/api'

export default {
  name: 'GoogleCallback',
  data() {
    return {
      loading: true,
      error: null
    }
  },
  mounted() {
    this.processLoginData();
  },
  methods: {
    processLoginData() {
      try {
        // 获取URL参数
        const params = new URLSearchParams(window.location.search);
        
        // 检查必要的参数是否存在
        if (!params.get('tokenValue') || !params.get('userId')) {
          throw new Error('登录信息不完整');
        }

        // 构建登录数据对象
        const loginData = {
          tokenValue: params.get('tokenValue'),
          tokenName: params.get('tokenName') || 'satoken', // 默认为satoken
          loginType: params.get('loginType'),
          loginDeviceType: params.get('loginDeviceType'),
          tag: params.get('tag'),
          tokenTimeout: parseInt(params.get('tokenTimeout')),
          sessionTimeout: parseInt(params.get('sessionTimeout')),
          userName: decodeURIComponent(params.get('userName')),
          userId: params.get('userId'),
          userRole: params.get('userRole'),
          email: decodeURIComponent(params.get('email')),
          userAvatar: decodeURIComponent(params.get('userAvatar')),
          isFinish: params.get('isFinish') === 'true'
        };

        // 使用tokenUtil工具类保存token
        tokenUtil.saveToken(loginData.tokenName, loginData.tokenValue);
        
        // 直接保存到satoken键
        localStorage.setItem('satoken', loginData.tokenValue);
        
        // 同时也保存原始token信息（可选，为了兼容性）
        localStorage.setItem('tokenValue', loginData.tokenValue);
        localStorage.setItem('tokenName', loginData.tokenName);

        // 保存用户信息
        const userInfo = {
          userName: loginData.userName,
          userId: loginData.userId,
          userRole: loginData.userRole,
          email: loginData.email,
          userAvatar: loginData.userAvatar,
          isFinish: loginData.isFinish
        };
        localStorage.setItem('userInfo', JSON.stringify(userInfo));

        // 保存其他登录相关信息
        localStorage.setItem('loginType', loginData.loginType);
        localStorage.setItem('loginDeviceType', loginData.loginDeviceType);
        localStorage.setItem('tag', loginData.tag);
        localStorage.setItem('tokenTimeout', loginData.tokenTimeout);
        localStorage.setItem('sessionTimeout', loginData.sessionTimeout);
        localStorage.setItem('isLoggedIn', 'true');

        // 验证token是否正确保存

        // 触发登录成功事件
        emitter.emit('login-success', userInfo);
        
        // 显示成功消息
        ElMessage({
          message: '登录成功',
          type: 'success',
          duration: 2000
        });

        // 重定向到首页
        this.$router.push('/');
      } catch (error) {
        this.error = error.message || '处理登录信息时发生错误';
        this.loading = false;
        // 3秒后重定向到登录页
        setTimeout(() => {
          this.$router.push('/login');
        }, 3000);
      }
    }
  }
}
</script>

<style scoped>
.google-callback {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
}

.loading {
  text-align: center;
}

.error {
  color: #f56c6c;
  text-align: center;
  padding: 20px;
}
</style> 