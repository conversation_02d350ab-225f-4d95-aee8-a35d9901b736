<template>
  <div class="bookmarked-posts-container">
    <div class="page-header">
      <h1>Your Bookmarked Posts</h1>
      <p>Posts you've saved for later</p>
    </div>

    <!-- 加载状态 -->
    <div v-if="isLoading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading bookmarked posts...</p>
    </div>

    <!-- 错误信息 -->
    <div v-if="error && !isLoading" class="error-message">
      <p>{{ error }}</p>
      <button @click="fetchFavoritePosts">Retry</button>
    </div>

    <!-- 无内容提示 -->
    <div v-if="posts.length === 0 && !isLoading && !error" class="empty-state">
      <div class="empty-content">
        <i class="fas fa-bookmark empty-icon"></i>
        <p>You don't have any bookmarked posts yet</p>
        <router-link to="/community" class="browse-btn">
          <i class="fas fa-search"></i> Browse community
        </router-link>
      </div>
    </div>

    <!-- 帖子列表 -->
    <div class="posts-grid">
      <div v-for="post in posts" :key="post.postId" class="post-card" @click="viewPostDetail(post.postId)">
        <div class="post-header">
          <div class="post-author">
            <img :src="post.avatarUrl || getDefaultAvatar(post.username)" alt="User Avatar" class="author-avatar">
            <span class="author-name">{{ post.username }}</span>
          </div>
          <div class="bookmark-date">
            <i class="fas fa-bookmark"></i>
            {{ formatDate(post.favoriteTime) }}
          </div>
        </div>
        <h3 class="post-title">{{ post.title }}</h3>
        <p class="post-excerpt">{{ formatExcerpt(post.content) }}</p>
        <div class="post-footer">
          <div class="post-stats">
            <span class="post-stat"><i class="far fa-eye"></i> {{ post.views || 0 }}</span>
            <span class="post-stat"><i class="far fa-thumbs-up"></i> {{ post.likes || 0 }}</span>
            <span class="post-stat"><i class="far fa-bookmark"></i> {{ post.favorites || 0 }}</span>
          </div>
          <div class="post-actions">
            <span class="post-date">{{ formatDate(post.postCreateTime) }}</span>
            <button class="unfavorite-btn" @click.stop="unfavoritePost(post)" title="Remove from bookmarks">
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import postsApi from '@/api/posts';
import { getPlaceholderImage } from '@/assets/placeholder.js';
import { ElMessageBox, ElMessage } from 'element-plus';

export default {
  name: 'BookmarkedPostsView',
  data() {
    return {
      posts: [],
      isLoading: false,
      error: null
    }
  },
  created() {
    this.fetchFavoritePosts();
  },
  methods: {
    // 获取默认头像
    getDefaultAvatar(name) {
      return getPlaceholderImage(name || 'User');
    },
    
    // 获取用户收藏的帖子
    async fetchFavoritePosts() {
      this.isLoading = true;
      this.error = null;
      
      try {
        // 从localStorage获取用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;
        
        if (!userId) {
          this.error = '未登录或用户信息缺失';
          this.$router.push('/login');
          return;
        }
        
        const response = await postsApi.getUserFavorites(userId);
        
        if (response.code === 200 && Array.isArray(response.data)) {
          this.posts = response.data;
        } else {
          this.error = response.msg || '获取收藏帖子失败';
        }
      } catch (error) {
        this.error = '获取收藏帖子失败，请稍后重试';
      } finally {
        this.isLoading = false;
      }
    },
    
    // 查看帖子详情
    viewPostDetail(postId) {
      this.$router.push(`/community/post/${postId}`);
    },
    
    // 取消收藏帖子
    async unfavoritePost(post) {
      // 使用确认对话框
      ElMessageBox.confirm(
        '确定要取消收藏这篇帖子吗？',
        '取消收藏',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }
      )
        .then(async () => {
          try {
            // 从localStorage获取用户ID
            const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
            const userId = userInfo.userId;
            
            if (!userId) {
              ElMessage.warning('请先登录后再操作');
              return;
            }
            
            const data = {
              userId: userId,
              postId: post.postId
            };
            
            const response = await postsApi.unfavoritePost(data);
            
            if (response.code === 200) {
              // 从列表中移除该帖子
              this.posts = this.posts.filter(p => p.postId !== post.postId);
              ElMessage.success('已从收藏中移除');
            } else {
              ElMessage.error(response.msg || '取消收藏失败');
            }
          } catch (error) {
            ElMessage.error('操作失败，请稍后重试');
          }
        })
        .catch(() => {
          // 取消操作
          ElMessage({
            type: 'info',
            message: '已取消操作'
          });
        });
    },
    
    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return 'Unknown time';
      
      const date = new Date(dateString);
      const now = new Date();
      const diff = now - date;
      
      // 如果时间在一小时内
      if (diff < 3600000) {
        const minutes = Math.floor(diff / 60000);
        return `${minutes === 0 ? 'Just now' : minutes + ' minutes ago'}`;
      }
      
      // 如果时间在今天内
      if (date.toDateString() === now.toDateString()) {
        return `Today ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
      }
      
      // 如果时间在昨天
      const yesterday = new Date(now);
      yesterday.setDate(yesterday.getDate() - 1);
      if (date.toDateString() === yesterday.toDateString()) {
        return `Yesterday ${date.getHours()}:${String(date.getMinutes()).padStart(2, '0')}`;
      }
      
      // 其他时间
      return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
    },
    
    // 格式化摘要
    formatExcerpt(content) {
      if (!content) return 'No content';
      if (content.length <= 100) return content;
      return content.substring(0, 100) + '...';
    }
  }
}
</script>

<style scoped>
.bookmarked-posts-container {
  width: 100%;
  max-width: 1400px; /* Increased max-width to match products page */
  margin: 0 auto;
  padding: 2rem 5rem 32px; /* Adjusted padding to match products page */
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background: #121212; /* Dark background */
  background-image: radial-gradient(circle at 50% 50%, #1a1a1a 0%, #0a0a0a 100%); /* Gradient background */
  color: #e0e0e0; /* Light text color */
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem; /* Increased font size */
  color: #c3a3ff; /* Purple color */
  margin-bottom: 0.8rem; /* Adjusted margin */
  font-weight: 700;
  text-shadow: 0 0 10px rgba(195, 163, 255, 0.5); /* Purple text shadow */
}

.page-header p {
  color: #a0a0a0; /* Slightly lighter grey for body text */
  font-size: 1.1rem; /* Increased font size */
}

/* 帖子网格布局 - 每行4个 */
.posts-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr); /* Default 4 columns */
  gap: 1.5rem; /* Gap remains the same */
  margin-top: 1.5rem;
}

.post-card {
  background: linear-gradient(135deg, #242428, #1a1a20); /* Dark card background */
  border-radius: 15px; /* Larger border radius */
  overflow: hidden;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4); /* Darker shadow */
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275); /* Bounce transition */
  cursor: pointer;
  border: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
  display: flex;
  flex-direction: column;
  position: relative; /* For glow effect */
}

.post-card:hover {
  transform: translateY(-5px) scale(1.03); /* Lift and scale on hover */
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.5), 0 0 20px rgba(120, 70, 200, 0.4); /* Darker and purple shadow */
  border-color: rgba(120, 70, 200, 0.5); /* Purple border on hover */
  background: linear-gradient(135deg, #2d2d35, #22222a); /* Darker gradient on hover */
  z-index: 2;
}

/* Mystical glow effect */
.post-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(circle at 50% 0%, rgba(120, 70, 200, 0.15), transparent 70%); /* Purple radial gradient */
  opacity: 0;
  transition: opacity 0.4s ease;
  pointer-events: none;
  border-radius: 15px;
  z-index: 1;
}

.post-card:hover::before {
  opacity: 1;
}

.post-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  background: rgba(35, 35, 40, 0.8); /* Darker background for header */
  padding-bottom: 10px; /* Added padding */
  border-bottom: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
}

.post-author {
  display: flex;
  align-items: center;
  gap: 0.6rem; /* Increased gap */
}

.author-avatar {
  width: 35px; /* Increased size */
  height: 35px; /* Increased size */
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(120, 70, 200, 0.3); /* Purple border */
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s;
}

.post-author:hover .author-avatar {
  transform: scale(1.1);
}

.author-name {
  font-size: 0.9rem; /* Increased font size */
  font-weight: 600;
  color: #e0e0e0; /* Light text color */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100px;
}

.bookmark-date {
  font-size: 0.8rem; /* Increased font size */
  color: #888; /* Darker grey for date */
  display: flex;
  align-items: center;
  gap: 0.4rem; /* Increased gap */
}

.bookmark-date i {
  color: #c3a3ff; /* Purple icon */
}

.post-title {
  font-size: 1.1rem; /* Increased font size */
  font-weight: 700; /* Bolder */
  margin-bottom: 0.8rem; /* Adjusted margin */
  line-height: 1.5; /* Increased line height */
  color: #e0e0e0; /* Light text color */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  transition: color 0.3s;
}

.post-card:hover .post-title {
  color: #c3a3ff; /* Purple on hover */
}

.post-excerpt {
  font-size: 0.95rem; /* Increased font size */
  color: #a0a0a0; /* Slightly lighter grey for body text */
  margin-bottom: 1.2rem; /* Increased margin */
  line-height: 1.6; /* Increased line height */
  flex-grow: 1;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word; /* Ensure long words break */
}

.post-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.85rem; /* Increased font size */
  color: #a9a9a9; /* Grey color */
  border-top: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
  padding-top: 0.8rem; /* Increased padding */
  margin-top: auto;
  background: rgba(35, 35, 40, 0.8); /* Darker background for footer */
}

.post-stats {
  display: flex;
  gap: 1rem; /* Gap remains the same */
  align-items: center;
  flex-wrap: wrap; /* Allow wrapping on smaller screens */
}

.post-actions {
  display: flex;
  align-items: center;
  gap: 0.8rem; /* Increased gap */
}

.post-stat {
  display: flex;
  align-items: center;
  gap: 0.4rem; /* Increased gap */
  transition: all 0.2s;
  padding: 4px 8px; /* Adjusted padding */
  border-radius: 20px; /* Pill shape */
  color: #a9a9a9; /* Grey color */
  font-size: 13px;
}

.post-stat:hover {
  background-color: rgba(120, 70, 200, 0.2); /* Light purple background on hover */
  color: #c3a3ff; /* Purple text on hover */
}

.post-stat i {
  color: #c3a3ff; /* Purple icon color */
}

.post-date {
  font-style: italic;
  color: #888; /* Darker grey for date */
  font-size: 0.8rem; /* Increased font size */
}

.unfavorite-btn {
  width: 28px; /* Increased size */
  height: 28px; /* Increased size */
  min-width: 28px; /* Ensure minimum size */
  min-height: 28px; /* Ensure minimum size */
  border-radius: 50%;
  border: none;
  background-color: rgba(245, 108, 108, 0.1); /* Light red background */
  color: #F56C6C; /* Red text */
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem; /* Increased font size */
  padding: 0;
  flex-shrink: 0; /* Prevent shrinking */
}

.unfavorite-btn:hover {
  background-color: #F56C6C; /* Red background on hover */
  color: white;
  transform: scale(1.1); /* Scale on hover */
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.4); /* Red shadow */
}

.unfavorite-btn i {
  font-size: 0.9rem; /* Increased icon size */
  width: auto;
  height: auto;
  line-height: 1; /* Adjust line height */
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 60px 0; /* Increased padding */
  color: #c3a3ff; /* Purple text */
}

.loading-spinner {
  width: 40px; /* Size remains the same */
  height: 40px; /* Size remains the same */
  border: 4px solid rgba(195, 163, 255, 0.2); /* Light purple border */
  border-radius: 50%;
  border-top-color: #c3a3ff; /* Purple border top */
  animation: spin 1s linear infinite;
  margin-bottom: 16px; /* Margin remains the same */
  box-shadow: 0 0 20px rgba(120, 70, 200, 0.5); /* Purple shadow */
}

@keyframes spin {
  to { transform: rotate(360deg) }
}

/* 错误信息 */
.error-message {
  background-color: rgba(245, 108, 108, 0.1); /* Light red background */
  border: 1px solid rgba(245, 108, 108, 0.3); /* Red border */
  border-radius: 12px; /* Larger border radius */
  padding: 24px; /* Increased padding */
  color: #f56c6c; /* Red text */
  text-align: center;
  margin: 24px 0; /* Increased margin */
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.2); /* Red shadow */
}

.error-message button {
  margin-top: 16px; /* Increased margin */
  padding: 10px 24px; /* Increased padding */
  background: linear-gradient(135deg, #f56c6c, #e05151); /* Red gradient */
  color: white; /* White text */
  border: none;
  border-radius: 30px; /* Pill shape */
  font-size: 14px; /* Font size remains the same */
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.25);
}

.error-message button:hover {
  background: linear-gradient(135deg, #e05151, #c03030); /* Darker red gradient on hover */
  transform: translateY(-2px); /* Subtle lift */
  box-shadow: 0 6px 16px rgba(245, 108, 108, 0.3);
}

/* 无内容提示 */
.empty-state {
  display: flex;
  justify-content: center;
  padding: 80px 20px; /* Increased padding */
  background-color: rgba(30, 30, 35, 0.9); /* Darker background */
  border-radius: 12px; /* Larger border radius */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4); /* Darker shadow */
  text-align: center;
  border: 1px solid rgba(80, 80, 100, 0.3); /* Grey border */
  color: #e0e0e0; /* Light text */
  margin: 24px auto; /* Center and add margin */
  max-width: 500px; /* Increased max-width */
}

.empty-content {
  max-width: 400px;
}

.empty-icon {
  font-size: 64px; /* Increased size */
  color: #c3a3ff; /* Purple icon */
  margin-bottom: 20px; /* Increased margin */
  opacity: 0.7;
}

.empty-state p {
  font-size: 1.2rem; /* Increased font size */
  color: #a0a0a0; /* Slightly lighter grey for body text */
  margin-bottom: 24px; /* Increased margin */
}

.browse-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.8rem; /* Increased gap */
  padding: 10px 24px; /* Adjusted padding */
  background: linear-gradient(135deg, #323232, #252525); /* Dark gradient background */
  color: #c3a3ff; /* Purple text */
  border: 1px solid rgba(120, 70, 200, 0.3); /* Purple border */
  border-radius: 30px; /* Pill shape */
  text-decoration: none;
  font-weight: 600;
  transition: all 0.3s;
  box-shadow: 0 2px 8px rgba(70, 20, 100, 0.3); /* Purple shadow */
  font-size: 16px; /* Increased font size */
}

.browse-btn:hover {
  background: linear-gradient(135deg, #404040, #303030); /* Darker gradient on hover */
  transform: translateY(-3px); /* Subtle lift */
  box-shadow: 0 5px 15px rgba(120, 70, 200, 0.4); /* Darker purple shadow */
  border-color: rgba(120, 70, 200, 0.6);
}

/* 响应式布局 */
@media (max-width: 1400px) {
  .bookmarked-posts-container {
    padding: 2rem 3rem 32px; /* Adjusted padding */
  }
}

@media (max-width: 1200px) {
  .posts-grid {
    grid-template-columns: repeat(3, 1fr); /* 3 columns on medium screens */
    gap: 1.2rem; /* Adjusted gap */
  }
}

@media (max-width: 992px) {
  .posts-grid {
    grid-template-columns: repeat(2, 1fr); /* 2 columns on smaller screens */
    gap: 1rem; /* Adjusted gap */
  }
  
  .page-header h1 {
    font-size: 2.2rem; /* Adjusted font size */
  }
}

@media (max-width: 576px) {
  .bookmarked-posts-container {
    padding: 1rem 1rem 32px; /* Adjusted padding */
  }

  .posts-grid {
    grid-template-columns: 1fr; /* Single column on mobile */
    gap: 0.8rem; /* Adjusted gap */
  }
  
  .page-header h1 {
    font-size: 1.8rem; /* Adjusted font size */
  }
  
  .page-header p {
    font-size: 1rem; /* Adjusted font size */
  }
  
  .post-card {
    border-radius: 10px; /* Adjusted border radius */
    padding: 1rem; /* Adjusted padding */
  }
  
  .author-avatar {
    width: 30px; /* Adjusted size */
    height: 30px; /* Adjusted size */
  }
  
  .author-name {
    font-size: 0.8rem; /* Adjusted font size */
  }
  
  .bookmark-date {
    font-size: 0.7rem; /* Adjusted font size */
  }
  
  .post-title {
    font-size: 1rem; /* Adjusted font size */
    margin-bottom: 0.6rem; /* Adjusted margin */
  }
  
  .post-excerpt {
    font-size: 0.9rem; /* Adjusted font size */
    margin-bottom: 1rem; /* Adjusted margin */
  }
  
  .post-footer {
    font-size: 0.75rem; /* Adjusted font size */
    padding-top: 0.6rem; /* Adjusted padding */
  }
  
  .post-stats {
    gap: 0.8rem; /* Adjusted gap */
  }
  
  .post-actions {
    gap: 0.6rem; /* Adjusted gap */
  }
  
  .unfavorite-btn {
    width: 24px; /* Adjusted size */
    height: 24px; /* Adjusted size */
    min-width: 24px; /* Ensure minimum size */
    min-height: 24px; /* Ensure minimum size */
    font-size: 0.8rem; /* Adjusted font size */
  }
  
  .unfavorite-btn i {
    font-size: 0.8rem; /* Adjusted icon size */
  }
  
  .empty-state {
    padding: 30px 10px; /* Adjusted padding */
  }
  
  .empty-icon {
    font-size: 50px; /* Adjusted size */
    margin-bottom: 15px; /* Adjusted margin */
  }
  
  .empty-state p {
    font-size: 1rem; /* Adjusted font size */
    margin-bottom: 15px; /* Adjusted margin */
  }
  
  .browse-btn {
    padding: 8px 16px; /* Adjusted padding */
    gap: 0.6rem; /* Adjusted gap */
    font-size: 14px; /* Adjusted font size */
  }
}

/* For touch devices */
@media (hover: none) {
  .post-card {
    transform: none !important;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.4) !important;
    background: linear-gradient(135deg, #242428, #1a1a20) !important;
    border-radius: 15px !important;
    border: 1px solid rgba(80, 80, 100, 0.3) !important;
  }

  .post-stat {
    min-width: 30px; /* Increased tap area */
  }

  .unfavorite-btn {
    min-width: 30px; /* Increased tap area */
    min-height: 30px; /* Increased tap area */
  }
}

</style> 