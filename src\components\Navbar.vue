<template>
  <nav class="navbar">
    <div class="container">
      <div class="left-section">
        <router-link to="/" class="logo-link">
          <Logo />
        </router-link>
      </div>
      <div class="right-section">
        <div class="nav-links">
          <router-link to="/" class="nav-link">{{ $t('navbar.home') }}</router-link>
          <router-link to="/products" class="nav-link">{{ $t('navbar.products') }}</router-link>
          <router-link to="/sellers" class="nav-link">{{ $t('navbar.sellers') }}</router-link>
          <router-link to="/how-to" class="nav-link">{{ $t('navbar.howToBuy') }}</router-link>
          <router-link to="/community" class="nav-link">{{ $t('navbar.community') }}</router-link>
        </div>
        <div class="nav-icons">
          <router-link to="/search" class="nav-icon" :title="$t('navbar.search')">
            <i class="fas fa-search"></i>
          </router-link>
          <router-link to="/cart" class="nav-icon" :title="$t('navbar.cart')">
            <i class="fas fa-shopping-cart"></i>
          </router-link>
          <router-link to="/user" class="nav-icon" :title="$t('navbar.user')">
            <i class="fas fa-user"></i>
          </router-link>
          <div class="language-switcher">
            <el-dropdown @command="handleLanguageChange">
              <span class="language-icon">
                <i class="fas fa-globe"></i>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="en">English</el-dropdown-item>
                  <el-dropdown-item command="zh">中文</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>
  </nav>
</template>

<script>
import { useI18n } from 'vue-i18n'
import Logo from './Logo.vue'
import { ElDropdown, ElDropdownMenu, ElDropdownItem } from 'element-plus'

export default {
  name: 'Navbar',
  components: {
    Logo,
    ElDropdown,
    ElDropdownMenu,
    ElDropdownItem
  },
  setup() {
    const { locale } = useI18n()
    
    const handleLanguageChange = (lang) => {
      locale.value = lang
      localStorage.setItem('locale', lang)
    }
    
    return {
      handleLanguageChange
    }
  }
}
</script>

<style scoped>
.language-switcher {
  margin-left: 10px;
  cursor: pointer;
}

.language-icon {
  font-size: 18px;
  color: #333;
}

.language-icon:hover {
  color: #1890ff;
}
</style>