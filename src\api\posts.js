import apiClient from '@/services/api';

/**
 * 通用API响应处理程序
 * 提供标准化的错误处理和响应格式
 */
const handleApiResponse = async (apiCall) => {
    try {
        const response = await apiCall();

        // 检查response是否为undefined或null
        if (!response) {
            console.error('API响应为空');
            return {
                code: 500,
                msg: 'API响应为空',
                data: null
            };
        }

        // 检查response.data是否存在，兼容不同的API响应格式
        if (response.data !== undefined) {
            return {
                code: response.data.code || 200,
                msg: response.data.msg || 'Success',
                data: response.data.data || response.data
            };
        } else {
            // 直接使用response作为数据，适用于拦截器已经提取了data的情况
            return {
                code: response.code || 200,
                msg: response.msg || 'Success',
                data: response.data || response
            };
        }
    } catch (error) {
        console.error('API请求错误:', error);
        return {
            code: error.response?.data?.code || error.response?.status || 500,
            msg: error.response?.data?.msg || error.message || '服务器错误',
            data: null
        };
    }
};

/**
 * 帖子相关API接口
 */
const postsApi = {
    /**
     * 获取所有帖子列表（分页）
     * @param {Object} params 查询参数
     * @param {number} [params.pageNum=1] 页码
     * @param {number} [params.pageSize=20] 每页条数
     * @param {string} [params.sortBy] 排序方式：time（时间）、views（浏览量）
     * @param {string} [params.title] 搜索关键词，可选
     * @param {string} [params.tagId] 标签ID，可选
     * @returns {Promise} 帖子列表，包含list数组和分页信息
     */
    getAllPosts(params = {}) {
        // 设置默认分页参数
        const defaultParams = {
            pageNum: 1,
            pageSize: 20,
            ...params
        };
        return handleApiResponse(() => apiClient.get('/post/getAllPosts', { params: defaultParams }));
    },

    /**
     * 获取帖子详情
     * @param {number} postId 帖子ID
     * @returns {Promise} 帖子详情
     */
    getPostDetail(postId) {
        return handleApiResponse(() => apiClient.get('/post/getPostDetail', { 
            params: { postId }
        }));
    },

    /**
     * 获取所有标签
     * @returns {Promise} 标签列表
     */
    getAllTags() {
        return handleApiResponse(() => apiClient.get('/tag/getAllTags'));
    },

    /**
     * 按标签获取帖子
     * @param {number} tagId 标签ID
     * @param {Object} params 分页参数
     * @returns {Promise} 帖子列表
     */
    getPostsByTag(tagId, params = {}) {
        return handleApiResponse(() => apiClient.get(`/post/getPostsByTag/${tagId}`, { params }));
    },

    /**
     * 根据帖子ID获取标签
     * @param {number} postId 帖子ID
     * @returns {Promise} 标签ID列表
     */
    getTagsByPostId(postId) {
        return handleApiResponse(() => apiClient.get(`/postTag/getTagsByPostId`, { params: { postId } }));
    },

    /**
     * 帖子点赞
     * @param {number} postId 帖子ID
     * @returns {Promise} 操作结果
     */
    likePost(postId) {
        // 从localStorage获取用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;

        return handleApiResponse(() => apiClient.post(`/post/like/${postId}`, { userId }));
    },

    /**
     * 取消帖子点赞
     * @param {number} postId 帖子ID
     * @returns {Promise} 操作结果
     */
    unlikePost(postId) {
        // 从localStorage获取用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;

        return handleApiResponse(() => apiClient.post(`/post/unlike/${postId}`, { userId }));
    },

    // /**
    //  * 获取帖子的所有评论
    //  * @param {number} postId 帖子ID
    //  * @returns {Promise} 评论列表
    //  */
    // getAllPostComments(postId) {
    //     return handleApiResponse(() => apiClient.post(`/postComments/all`, { postId }));
    // },

    // /**
    //  * 获取帖子的所有评论（带分页）
    //  * @param {number} postId 帖子ID
    //  * @param {number} pageNum 页码
    //  * @param {number} pageSize 每页条数
    //  * @returns {Promise} 评论列表
    //  */
    // getAllPostComments(postId, pageNum = 1, pageSize = 3) {
    //     return handleApiResponse(() => apiClient.post(`/postComments/all`, {
    //         postId,
    //         pageNum,
    //         pageSize
    //     }));
    // },

    /**
     * 获取帖子的所有评论（带分页）
     * @param {number} postId 帖子ID
     * @returns {Promise} 评论列表
     */
    getPostCommentsWithPaging(postId) {
        return handleApiResponse(() => apiClient.post(`/postComments/all`, {
            postId
        }));
    },

    /**
     * 添加帖子评论
     * @param {number} postId 帖子ID
     * @param {Object} comment 评论内容
     * @returns {Promise} 操作结果
     */
    addPostComment(postId, comment) {
        // 从localStorage获取用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;

        return handleApiResponse(() => apiClient.post(`/post/comment/${postId}`, {
            userId,
            ...comment
        }));
    },

    /**
     * 收藏帖子
     * @param {number} postId 帖子ID
     * @returns {Promise} 操作结果
     */
    collectPost(postId) {
        // 从localStorage获取用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;

        return handleApiResponse(() => apiClient.post(`/post/collect/${postId}`, { userId }));
    },

    /**
     * 取消收藏帖子
     * @param {number} postId 帖子ID
     * @returns {Promise} 操作结果
     */
    uncollectPost(postId) {
        // 从localStorage获取用户ID
        const userInfo = JSON.parse(localStorage.getItem('userInfo') || '{}');
        const userId = userInfo.userId;

        return handleApiResponse(() => apiClient.post(`/post/uncollect/${postId}`, { userId }));
    },

    /**
     * 根据标签ID获取帖子列表
     * @param {number} tagId 标签ID
     * @param {Object} params 分页参数
     * @returns {Promise} 帖子列表
     */
    getPostsByTagId(tagId, params = {}) {
        return handleApiResponse(() => apiClient.get('/post/getPostsByTagId', {
            params: {
                tagId,
                ...params
            }
        }));
    },

    /**
     * 批量获取多个帖子的标签
     * @param {Array<number>} postIds 帖子ID数组
     * @returns {Promise} 帖子ID到标签ID数组的映射
     */
    getTagsByPostIds(postIds) {
        return handleApiResponse(() => apiClient.post('/postTag/getTagsByPostIds', postIds));
    },

    /**
     * 创建新帖子
     * @param {Object} post 帖子对象，包含标题、内容、用户ID和标签数组
     * @returns {Promise} 创建结果
     */
    createPost(post) {
        // 确保userId是字符串
        post.userId = post.userId ? post.userId.toString() : post.userId;
        
        return handleApiResponse(() => apiClient.post('/post/create', post));
    },

    /**
     * 删除帖子
     * @param {number|string} postId 帖子ID
     * @returns {Promise} 删除结果
     */
    deletePost(postId) {
        // 不再转换为整数，直接使用原始ID
        return handleApiResponse(() => apiClient.get(`/post/delete`, {
            params: { postId }
        }));
    },

    /**
     * 更新帖子
     * @param {Object} post 帖子对象，包含id、标题、内容和可选的标签数组
     * @returns {Promise} 更新结果
     */
    updatePost(post) {
        return handleApiResponse(() => apiClient.post('/post/updatePost', post));
    },

    /**
     * 上传图片
     * @param {File} file 要上传的图片文件
     * @returns {Promise} 上传结果，包含图片URL
     */
    uploadImage(file) {
        const formData = new FormData();
        formData.append('file', file);

        // 使用直接的fetch调用，因为需要特殊的FormData处理
        return handleApiResponse(async () => {
            // 获取token
            const token = localStorage.getItem('satoken') || '';

            const response = await fetch('/post/upload', {
                method: 'POST',
                body: formData,
                headers: {
                    'Authorization': token
                }
            });

            const result = await response.json();
            return result;
        });
    },

    /**
     * 创建评论
     * @param {Object} commentData 评论数据，包含postId、userId和postContent
     * @returns {Promise} 操作结果
     */
    createComment(commentData) {
        return handleApiResponse(() => apiClient.post('/postComments/create', commentData));
    },

    /**
     * 删除评论
     * @param {number} commentId 评论ID
     * @returns {Promise} 操作结果
     */
    deleteComment(commentId) {
        return handleApiResponse(() => apiClient.get(`/postComments/delete`, {
            params: { commentId }
        }));
    },

    /**
     * 回复评论
     * @param {Object} replyData 包含 postId, userId, postContent, parentCommentId
     * @returns {Promise} 操作结果
     */
    replyToComment(replyData) {
        return handleApiResponse(() => apiClient.post('/postComments/reply', replyData));
    },

    /**
     * 上传帖子图片（走apiClient代理）
     * @param {File} file 图片文件
     * @returns {Promise} 上传结果，包含图片url等
     */
    uploadPostImage(file) {
        const formData = new FormData();
        formData.append('file', file);
        return handleApiResponse(() =>
            apiClient.post('/postImages/upload', formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            })
        );
    },

    /**
     * 绑定帖子图片
     * @param {number} postId 帖子ID
     * @param {string} imageUrl 图片URL
     * @returns {Promise} 绑定结果
     */
    bindPostImage(postId, imageUrl) {
        return handleApiResponse(() =>
            apiClient.post('/postImages/images', { postId, imageUrl })
        );
    },

    /**
     * 评论点赞
     * @param {Object} params 包含userId和commentId
     * @returns {Promise} 点赞结果，包含点赞数等
     */
    likeComment(params) {
        return handleApiResponse(() => apiClient.post('/userLikes/like', params));
    },

    /**
     * 批量检查评论点赞状态
     * @param {Object} params 包含userId和commentIds数组
     * @returns {Promise} 点赞状态，key为评论ID，value为是否点赞
     */
    checkLikedComments(params) {
        return handleApiResponse(() => apiClient.post('/userLikes/checkLikes', params));
    },

    /**
     * 取消评论点赞
     * @param {Object} params 包含userId和commentId
     * @returns {Promise} 取消点赞结果，包含点赞数等
     */
    unlikeComment(params) {
        return handleApiResponse(() => apiClient.post('/userLikes/unlike', params));
    },

    // 收藏帖子
    favoritePost(data) {
        return handleApiResponse(() => apiClient.post('/postFavorites/favorite', data));
    },

    // 取消收藏帖子
    unfavoritePost(data) {
        return handleApiResponse(() => apiClient.post('/postFavorites/unfavorite', data));
    },

    // 获取收藏状态
    checkFavoriteStatus(params) {
        return handleApiResponse(() => apiClient.get('/postFavorites/checkStatus', { params }));
    },

    // 批量检查收藏状态
    batchCheckFavoriteStatus(data) {
        return handleApiResponse(() => {
            console.log('发送批量检查收藏请求:', data);
            return apiClient.post('/postFavorites/batchCheck', data)
                .then(response => {
                    // 确保response.data里的数据都是布尔值
                    if (response.data && response.data.data) {
                        const processedData = { ...response.data };
                        // 转换所有收藏状态为布尔值
                        Object.keys(processedData.data).forEach(key => {
                            processedData.data[key] = !!processedData.data[key];
                        });
                        return processedData;
                    }
                    return response.data;
                })
                .catch(error => {
                    console.error('批量检查收藏状态API错误:', error);
                    throw error;
                });
        });
    },

    /**
     * 帖子点赞
     * @param {Object} data 包含userId和postId
     * @returns {Promise} 点赞结果，包含点赞数等
     */
    likePostNew(data) {
        return handleApiResponse(() => apiClient.post('/postLikes/like', data));
    },

    /**
     * 取消帖子点赞
     * @param {Object} data 包含userId和postId
     * @returns {Promise} 取消点赞结果，包含点赞数等
     */
    unlikePostNew(data) {
        return handleApiResponse(() => apiClient.post('/postLikes/unlike', data));
    },

    /**
     * 批量检查帖子点赞状态
     * @param {Object} data 包含userId和postIds数组
     * @returns {Promise} 点赞状态，key为帖子ID，value为是否点赞
     */
    batchCheckPostLikeStatus(data) {
        return handleApiResponse(() => apiClient.post('/postLikes/batchCheck', data));
    },

    /**
     * 增加帖子浏览量
     * @param {number} postId 帖子ID
     * @returns {Promise} 更新后的浏览量
     */
    incrementPostView(postId) {
        return handleApiResponse(() => apiClient.post('/post/increment', { postId }));
    },

    /**
     * 获取帖子浏览量
     * @param {number} postId 帖子ID
     * @returns {Promise} 帖子浏览量
     */
    getPostViews(postId) {
        return handleApiResponse(() => apiClient.get(`/post/count/${postId}`));
    },

    /**
     * 获取用户收藏的帖子
     * @param {number} userId 用户ID
     * @returns {Promise} 用户收藏的帖子列表
     */
    getUserFavorites(userId) {
        return handleApiResponse(() => apiClient.get('/postFavorites/getUserFavorites', {
            params: { userId }
        }));
    },

    /**
     * 获取用户发布的帖子
     * @param {number} userId 用户ID
     * @param {Object} params 可选的查询参数（页码、每页条数等）
     * @returns {Promise} 用户发布的帖子列表
     */
    getUserPosts(userId, params = {}) {
        return handleApiResponse(() => apiClient.get('/post/getPostsByUserId', {
            params: {
                userId,
                ...params
            }
        }));
    },

    /**
     * 上传评论图片
     * @param {FormData} formData 包含图片文件的表单数据
     * @returns {Promise} 上传结果，包含图片url数组
     */
    uploadCommentImages(formData) {
        return handleApiResponse(() =>
            apiClient.post('/postComments/upload', formData, {
                headers: { 'Content-Type': 'multipart/form-data' }
            })
        );
    },

};

export default postsApi; 