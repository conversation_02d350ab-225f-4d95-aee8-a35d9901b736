/**
 * 移除OMGBUY标志下方的紫色横线
 */
export function removePurpleLine() {
  // 等待DOM加载完成
  document.addEventListener('DOMContentLoaded', () => {
    removeLine();
  });
  
  // 也在window加载完成后尝试移除
  window.addEventListener('load', () => {
    removeLine();
    
    // 多次尝试移除以防万一
    setTimeout(removeLine, 500);
    setTimeout(removeLine, 1000);
  });
}

function removeLine() {
  // 查找可能包含紫色线条的元素
  const navLogo = document.querySelector('.nav-logo');
  const logo = document.querySelector('.logo');
  const logoContainer = document.querySelector('.logo-container');
  
  if (navLogo) {
    // 只移除底部边框
    navLogo.style.borderBottom = 'none';
  }
  
  if (logo) {
    logo.style.borderBottom = 'none';
  }
  
  if (logoContainer) {
    logoContainer.style.borderBottom = 'none';
  }
  
  // 添加样式覆盖所有可能的紫色横线
  const style = document.createElement('style');
  style.innerHTML = `
    .nav-logo::after, .logo::after, 
    .logo-container::after, .main-nav::after {
      display: none !important;
      content: none !important;
      border-bottom: none !important;
    }
    
    .main-nav {
      border-bottom: none !important;
    }
  `;
  
  document.head.appendChild(style);
} 