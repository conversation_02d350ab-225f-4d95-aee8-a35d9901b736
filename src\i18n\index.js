import { createI18n } from 'vue-i18n'
import en from './locales/en.js'
import fr from './locales/fr.js'
import de from './locales/de.js'
import es from './locales/es.js'
import it from './locales/it.js'
import pt from './locales/pt.js'
import ru from './locales/ru.js'

const i18n = createI18n({
  legacy: false, // 使用组合式API
  locale: localStorage.getItem('locale') || 'en', // 默认语言
  fallbackLocale: 'en', // 回退语言
  globalInjection: true, // 全局注入 $t 方法
  messages: {
    en,
    fr,
    de,
    es,
    it,
    pt,
    ru
  }
})

export default i18n